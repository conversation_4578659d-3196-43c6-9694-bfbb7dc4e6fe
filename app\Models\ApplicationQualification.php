<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ApplicationQualification extends Model
{
    protected $table = 'application_qualifications';

    protected $fillable = [
        'application_id',
        'major',
        'location',
        'gpa',
        'end_year',
        'file_id',
        'qualification'
    ];

    public function application()
    {
        return $this->belongsTo(Application::class, 'application_id');
    }
}
