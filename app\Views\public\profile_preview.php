<?= $this->extend('layouts/public') ?>
<?= $this->section('content') ?>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-12 col-xl-12">
        <?= display()->messages() ?>
            <!-- Job Information Header -->
            <div class="card gradient-card mb-4">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h2 class="mb-0"><?= tr("Application for") ?>: <?= esc($job->name) ?></h2>
                        </div>
                        <div class="col text-end">
                            <span class="badge badge-primary">GSC-<?= str_pad($job->id, 5, "0", STR_PAD_LEFT) ?></span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <?= tr("Please review your profile information below before confirming your job application") ?>
                    </div>
                </div>
            </div>

            <!-- Profile Preview Card -->
            <div class="card gradient-card">
                <div class="card-header">
                    <h3 class="mb-0"><?= tr("Profile Preview") ?></h3>
                </div>
                <div class="card-body">
                    
                    <!-- Personal Information Section -->
                    <div class="row mb-4">
                        <div class="col-md-4 text-center mb-4">
                            <div class="profile-image-container">
                                <img src="<?= $profile->getImageUrl() ?>" alt="<?= tr("Profile Image") ?>"
                                    class="img-fluid rounded-circle shadow"
                                    style="width: 120px; height: 120px; object-fit: cover;">
                            </div>
                            <h5 class="mt-3 mb-1"><?= esc($profile->name) ?></h5>
                            <p class="text-muted"><?= esc($profile->name_en) ?></p>
                        </div>

                        <div class="col-md-8">
                            <h4 class="text-primary border-bottom pb-2 mb-3"><?= tr("Personal Information") ?></h4>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Phone") ?></label>
                                    <div class="fw-bold" dir="ltr">+968 <?= esc($profile->phone) ?></div>
                                </div>

                                <?php if ($profile->phone_2): ?>
                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Secondary Phone") ?></label>
                                    <div class="fw-bold" dir="ltr">+968 <?= esc($profile->phone_2) ?></div>
                                </div>
                                <?php endif ?>

                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Email") ?></label>
                                    <div class="fw-bold"><?= esc($profile->email) ?></div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Card ID") ?></label>
                                    <div class="fw-bold"><?= esc($profile->card_id) ?></div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Gender") ?></label>
                                    <div class="fw-bold"><?= tr($profile->gender) ?></div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Birth Date") ?></label>
                                    <div class="fw-bold"><?= $profile->birth ? $profile->birth->format('Y-m-d') : '' ?></div>
                                </div>

                                <?php if ($profile->reg_id): ?>
                                <div class="col-md-12 mb-3">
                                    <label class="text-muted small"><?= tr("Location") ?></label>
                                    <div class="fw-bold">
                                        <?= reg($profile->reg_id)->name_ar ?? '' ?>
                                        <?php if ($profile->city_id): ?>
                                        - <?= city($profile->city_id)->name_ar ?? '' ?>
                                        <?php endif ?>
                                    </div>
                                </div>
                                <?php endif ?>
                            </div>
                        </div>
                    </div>

                    <!-- Documents Section -->
                    <div class="mb-4">
                        <h4 class="text-primary border-bottom pb-2 mb-3"><?= tr("Documents") ?></h4>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <div class="card border">
                                    <div class="card-body text-center py-3">
                                        <i class="fas fa-file-pdf fa-2x text-danger mb-2"></i>
                                        <h6><?= tr("CV File") ?></h6>
                                        <?php if ($profile->cv_file): ?>
                                        <a href="<?= $profile->getCvUrl() ?>" target="_blank"
                                            class="btn btn-sm btn-link text-primary">
                                            <i class="fas fa-download"></i> <?= tr("Download") ?>
                                        </a>
                                        <?php else: ?>
                                        <small class="text-muted"><?= tr("Not uploaded") ?></small>
                                        <?php endif ?>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <div class="card border">
                                    <div class="card-body text-center py-3">
                                        <i class="fas fa-id-card fa-2x text-primary mb-2"></i>
                                        <h6><?= tr("Card ID File") ?></h6>
                                        <?php if ($profile->card_id_file): ?>
                                        <a href="<?= $profile->getCardIdUrl() ?>" target="_blank"
                                            class="btn btn-sm btn-link text-primary">
                                            <i class="fas fa-download"></i> <?= tr("Download") ?>
                                        </a>
                                        <?php else: ?>
                                        <small class="text-muted"><?= tr("Not uploaded") ?></small>
                                        <?php endif ?>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4 mb-3">
                                <div class="card border">
                                    <div class="card-body text-center py-3">
                                        <i class="fas fa-file-pdf fa-2x text-danger mb-2"></i>
                                        <h6><?= tr("Ministry of Labor File") ?></h6>
                                        <?php if ($profile->mol_file): ?>
                                        <a href="<?= $profile->getMolUrl() ?>" target="_blank"
                                            class="btn btn-sm btn-link text-primary">
                                            <i class="fas fa-download"></i> <?= tr("Download") ?>
                                        </a>
                                        <?php else: ?>
                                        <small class="text-muted"><?= tr("Not uploaded") ?></small>
                                        <?php endif ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Qualifications Section -->
                    <div class="mb-4">
                        <h4 class="text-primary border-bottom pb-2 mb-3"><?= tr("Qualifications") ?></h4>
                        <?php if ($profile->qualifications->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead class="bg-primary">
                                    <tr>
                                        <th><?= tr("Major") ?></th>
                                        <th><?= tr("Qualification") ?></th>
                                        <th><?= tr("Location") ?></th>
                                        <th><?= tr("Year") ?></th>
                                        <th><?= tr("GPA") ?></th>
                                        <th><?= tr("File") ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($profile->qualifications as $q): ?>
                                    <tr>
                                        <td><?= esc($q->major) ?></td>
                                        <td><?= esc($q->qualification) ?></td>
                                        <td><?= esc($q->location) ?></td>
                                        <td><?= esc($q->end_year) ?></td>
                                        <td><?= esc($q->gpa) ?></td>
                                        <td>
                                            <?php if ($q->file_id): ?>
                                            <a href="<?= $q->getFileUrl() ?>" target="_blank"
                                                class="btn btn-sm btn-link text-primary">
                                                <i class="fas fa-download"></i> <?= tr("Download") ?>
                                            </a>
                                            <?php endif ?>
                                        </td>
                                    </tr>
                                    <?php endforeach ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-3 text-muted">
                            <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                            <p><?= tr("No qualifications added") ?></p>
                        </div>
                        <?php endif ?>
                    </div>

                    <!-- Experiences Section -->
                    <div class="mb-4">
                        <h4 class="text-primary border-bottom pb-2 mb-3"><?= tr("Work Experience") ?></h4>
                        <?php if ($profile->experiences->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead class="bg-primary">
                                    <tr>
                                        <th><?= tr("experience_job") ?></th>
                                        <th><?= tr("experience_location") ?></th>
                                        <th><?= tr("experience_start_date") ?></th>
                                        <th><?= tr("experience_end_date") ?></th>
                             
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($profile->experiences as $e): ?>
                                    <tr>
                                        <td><?= esc($e->job_name) ?></td>
                                        <td><?= esc($e->location) ?></td>
                                        <td><?= $e->start_date ? date('Y-m', strtotime($e->start_date)) : '' ?></td>
                                        <td><?= $e->is_current ? tr("experience_current") : ($e->end_date ? date('Y-m', strtotime($e->end_date)) : '') ?></td>
                                    </tr>
                                    <?php endforeach ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-3 text-muted">
                            <i class="fas fa-briefcase fa-2x mb-2"></i>
                            <p><?= tr("No work experience added") ?></p>
                        </div>
                        <?php endif ?>
                    </div>

                    <!-- Certificates Section -->
                    <div class="mb-4">
                        <h4 class="text-primary border-bottom pb-2 mb-3"><?= tr("Other Certificates") ?></h4>
                        <?php if ($profile->certs->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead class="bg-primary">
                                    <tr>
                                        <th><?= tr("Title") ?></th>
                                        <th><?= tr("File") ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($profile->certs as $c): ?>
                                    <tr>
                                        <td><?= esc($c->name) ?></td>
                                        <td>
                                            <?php if ($c->file_id): ?>
                                            <a href="<?= $c->getFileUrl() ?>" target="_blank"
                                                class="btn btn-sm btn-link text-primary">
                                                <i class="fas fa-download"></i> <?= tr("Download") ?>
                                            </a>
                                            <?php else: ?>
                                            <small class="text-muted"><?= tr("No file") ?></small>
                                            <?php endif ?>
                                        </td>
                                    </tr>
                                    <?php endforeach ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-3 text-muted">
                            <i class="fas fa-certificate fa-2x mb-2"></i>
                            <p><?= tr("No files added") ?></p>
                        </div>
                        <?php endif ?>
                    </div>

                    <!-- Confirmation Section -->
                    <div class="border-top pt-4">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong><?= tr("Important") ?>:</strong> <?= tr("Once you confirm your application, this information will be submitted for the job position. Please make sure all information is correct.") ?>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <a class="btn btn-secondary px-3 py-2" href="<?= base_url('job/'.$job->code) ?>">
                                <i class="fas fa-arrow-right"></i> <?= tr("Back") ?>
                            </a>
                            <div>
                                <form action="<?= base_url('confirm_application') ?>" method="post" class="ajax">
                                    <input type="hidden" name="job_code" value="<?= $job->code ?>">
                                    <button type="submit" class="btn btn-success px-5 py-2">
                                        <i class="fas fa-check"></i> <?= tr("Proceed") ?>
                                    </button>

                                    <a href="<?= base_url('profile/edit') ?>" class="btn btn-primary px-5 py-2">
                                        <i class="fas fa-edit"></i> <?= tr("Edit") ?>
                                    </a>
                                </form>
                            </div>
                            
                            <span class=""></span><!-- Spacer to center the button -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmApplication() {
    if (confirm('<?= tr("Are you sure you want to submit your application for this job position?") ?>')) {
        // Show loading
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <?= tr("Submitting...") ?>';
        button.disabled = true;
        
        // Submit application
        fetch('<?= base_url("confirm_application") ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: 'job_code=<?= esc($job->code) ?>'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.action) {
                    eval(data.action);
                } else {
                    window.location.href = '<?= base_url() ?>';
                }
            } else {
                alert(data.message.join('\n'));
                button.innerHTML = originalText;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('<?= tr("An error occurred. Please try again.") ?>');
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
}
</script>

<?= $this->endSection() ?> 