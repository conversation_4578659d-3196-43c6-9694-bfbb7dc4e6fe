<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>
<div class="d-flex justify-content-between mb-1 mt-3">
    <h3><?= tr("Jobs") ?></h3>
    <div>
        <?php if (_can("jobs")): ?>
           <a class="btn btn-secondary" href="<?= base_url("jobs/import_applications") ?>">
               <i class="fas fa-upload"></i> <?= tr("Import applications") ?>
           </a>
        <?php endif ?>

        <a class="btn btn-primary" href="<?= base_url("jobs/create") ?>">
            <i class="fas fa-plus"></i> <?= tr("Add new") ?>
        </a>
    </div>
</div>
<div class="card shadow border-0">
    
    <table class="table table-striped dt" id="vipTable">
        <thead class="bg-primary text-white">
            <tr>
                <td><?= tr("ID") ?></td>
                <th><?= tr("job_name") ?></th>
                <th><?= tr("Applications") ?></th>
                <th><?= tr("Status") ?></th>
                <th><?= tr("") ?></th>
               
      
                
            </tr>
        </thead>
        <tbody>
            
        </tbody>
    </table>
</div>






<?= $this->endSection() ?>


<?= $this->section("script") ?>

<script type="text/javascript">
    $(document).ready(function() {
        createDatatable("#vipTable", true, {
            ajax: "<?= base_url("jobs/datatable") ?>"
        });
    });
</script>
<?= $this->endSection() ?>