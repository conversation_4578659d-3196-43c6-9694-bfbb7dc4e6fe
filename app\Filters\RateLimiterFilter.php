<?php

namespace App\Filters;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;
use Config\Services;

class RateLimiterFilter implements FilterInterface
{
    private $ip_max_requests = 50; // Maximum allowed requests
    private $session_max_requests = 20; // Maximum allowed requests
    private $timeWindow = 120;   // Time window in seconds

    public function before(RequestInterface $request, $arguments = null)
    {
        if (strtolower($request->getMethod()) == 'get') {

            return;
        }

        $ipAddress = _ip();

        $session = session();
        if (!$session->has('unique_id')) {
            $session->set('unique_id', bin2hex(random_bytes(16)));
        }
        $sessionId = $session->get('unique_id');

        $cache = Services::cache();

        // Create unique keys for IP and session
        $ipKey = 'rate_limit_ip_' . md5($ipAddress);
        $sessionKey = 'rate_limit_session_' . md5($sessionId);

        // Rate limit by IP
        if ($this->isRateLimited($cache, $ipKey,$this->ip_max_requests)) {
            return $this->rateLimitExceededResponse('IP address');
        }

        // Rate limit by Session ID
        if ($this->isRateLimited($cache, $sessionKey,$this->session_max_requests)) {
            return $this->rateLimitExceededResponse('session');
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No action needed after the request
    }

    private function isRateLimited($cache, $key,$maxRequests)
    {
        $requests = $cache->get($key);

        if ($requests === null) {
            // First request, set the counter
            $cache->save($key, 1, $this->timeWindow);
            return false;
        } elseif ($requests < $maxRequests) {
            // Increment the request count
            $requests++;
            $cache->save($key, $requests, $this->timeWindow);

            return false;
        } else {
            // Rate limit exceeded
            return true;
        }
    }


    private function rateLimitExceededResponse($type)
    {

        return _response([
            "success"=>false,
            "message"=>[tr("Too Many Requests. Please try again later").($type=='IP address'?".":"")]
        ]);

    }
}
