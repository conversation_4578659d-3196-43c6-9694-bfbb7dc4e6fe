<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;


class JobInterviewGroup extends Model
{
 
    use SoftDeletes;

    protected $table = 'job_interview_groups';
    protected $fillable = ['job_id', 'name',"application_limit"];
  
 


    public function job()
    {
        return $this->belongsTo(Job::class,'job_id');
    }

    public function Applications()
    {
        return $this->hasMany(Application::class,'interview_group_id');
    }

    public static function default($id)
    {
        if ($id == 0) {
            return new self([
                'id' => 0,
                'name' => 'غير معرف',
                'application_limit' => 9999
            ]);
        }

        return self::find($id);
    }
    
    
}