<?= $this->extend('layouts/main',) ?>
<?= $this->section('content') ?>
<div class="d-flex justify-content-between">

    <h3><?= tr("Logs") ?></h3>
    
    <div>
        
        
        
    </div>
    
</div>

<div class="card ">


    <table class="table table-striped " id="Datatable">
        <thead class="bg-primary text-white">
            <tr>
                <th><?= tr("ID") ?></th>
                <th><?= tr("User") ?></th>
                <th><?= tr("Created at") ?></th>
                <th><?= tr("Description") ?></th>


            </tr>
        </thead>
        <tbody>
            
        </tbody>
    </table>
</div>




    <script type="text/javascript">
    $(document).ready(function() {
        createDatatable("#Datatable", true, {

          
          ajax : {
                url: "<?= base_url('logs/datatable') ?>",
                type: 'GET',
                data: function(d) {
                    return $.extend({}, d, getFilterData());
                }
            }
        });

        $(".gender").change(function() {
            initDatatable()
          });
    });

    function getFilterData() {
 
      const data = {
   
        gender: $('.user').val(),
      };
      
      return data;
    }







</script>


<?= $this->endSection() ?>

