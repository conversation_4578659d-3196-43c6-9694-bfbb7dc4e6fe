<?php

namespace App\Controllers;


use App\Models\User;
use App\Libraries\Notify;
use CodeIgniter\Email\Email;
use Carbon\Carbon;

use Illuminate\Database\Capsule\Manager as DB;

class Cron extends BaseController
{


	public function index(){
		
		


		

	}

	public function sms(){

		$this->send_sms();
		sleep(10);
		$this->send_sms();
		sleep(10);
		$this->send_sms();
		sleep(10);
		$this->send_sms();
		sleep(10);
		$this->send_sms();

	}

	private function send_sms(){

		$today = Carbon::today(); // Get the start of today

	    $sms = DB::table("sms_q")
	             ->where("status", "Queue")
	             ->where("created_at", '>=', $today)
	             ->get();

		foreach ($sms as $key => $sm) {

			$phone = _dr($sm->phone);
			
			if (strlen($phone)==8) {
				_sms($phone,$sm->sms);
			}
			
			DB::update("UPDATE sms_q SET status = 'Done' WHERE id = ? ",[$sm->id]);
		}
	}

	public function email(){
		$this->send_eamil();
		sleep(20);
		$this->send_eamil();
		sleep(20);
		$this->send_eamil();

	}


	private function send_eamil(){
		$email = DB::table("email_q")->where("sent_01","0")->where("created_at", '>=', Carbon::today())->get();


		foreach ($email as $key => $em) {
			

			$email = \Config\Services::email();

	        $email->setFrom(env("email.fromEmail"), env("email.fromName"));

	        $recipients=json_decode($em->send_to,true);

	        foreach ($recipients as $recipient) {

		        $email->setTo(_dr($recipient[0]));

		        $email->setSubject($em->title);
		        $email->setMailType('html');  
		        $email->setMessage($em->template);

		        if ($email->send())
		        {
		            // echo 'Email successfully sent';
		        }
		        else
		        {
		            $data = $email->printDebugger(['headers']);
		            // print_r($data);
		        }
		    }

			DB::update("UPDATE email_q SET sent_01 = 1 WHERE id = ? ",[$em->id]);
		}
	}
}