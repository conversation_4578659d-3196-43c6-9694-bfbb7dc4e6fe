

function get_cities(event) {
    const region_id = event.target.value;
    console.log('Region ID:', region_id);
    $.post(base_url+'get_cities/'+region_id, function(data) {
        var cities = (data);
        var citySelect = $('select[name="city_id"]');
        citySelect.empty(); 

        $.each(cities, function (index, city) {
            citySelect.append($('<option>', {
                value: city.id,
                text: city.name
            }));
        });

        citySelect.trigger('change');
    },'json');
}

function fillForm($form, data) {
    $.each(data, function(key, value) {
        let $field = $form.find('[name="' + key + '"]');
        if ($field.is(':radio') || $field.is(':checkbox')) {
            $field.filter('[value="' + value + '"]').prop('checked', true);
        } else {
            $field.val(value);
        }

        $field.val(value).trigger('change');
    });
}

function notifi(msg="",type='success'){
    new PNotify({
        text: msg,
        addclass: 'bg-'+type+' border-'+type
    });
}
if (typeof swal == 'undefined') {
        console.warn('Warning - sweet_alert.min.js is not loaded.');
      
    }
function doswal(options){
      var swalInit = swal.mixin({
            buttonsStyling: false,
            confirmButtonClass: 'btn btn-primary',
            cancelButtonClass: 'btn btn-light'
        });

      swalInit.fire(options);
}

function loading(action='start'){
    if (action=='start') {
        notice = new PNotify({
            text: "Loading",
            addclass: 'bg-light border-dark',
            type: 'info',
            icon: 'icon-spinner4 spinner',
            hide: false,
            buttons: {
                closer: false,
                sticker: false
            },
            opacity: .9,
            width: "170px"
        });
          
    }else if(action=='remove' || action=='hide'){
        try {
            notice.remove();
        } catch(e) {

        }
         

    }
}

document.addEventListener('DOMContentLoaded', function() {
    var links = document.querySelectorAll('.after-confirm');

    links.forEach(function(link) {
        link.addEventListener('click', function(event) {
            event.preventDefault(); // Prevent the default link behavior

            var userConfirmed = confirm(_confirm_action_prompt);

            if (userConfirmed) {
                window.location.href = this.href;
            }
        });
    });
});



function initSelect2() {
    
    $('select.select2').each(function() {
        $(this).select2();
    });

    initAjaxSelect2();
}


function initAjaxSelect2(element, url) {

    $('select.ajax-select2').each(function() {
        const url = $(this).data('source'); // Assumes the element has a 'data-source' attribute containing the URL
    

        $(this).select2({
            ajax: {
                url: url,
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        q: params.term,
                        page: params.page
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;
                    return {
                        results: data.items,
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                },
                cache: true
            },

              templateResult: function (data) {
    if (data.loading) return data.text;
    
    let markup = $("<div></div>");
    markup.append($("<span></span>").text(data.text));
    
    if (data.subtext) {
      markup.append($("<br/>")).append($("<small class='text-muted'></small>").text(data.subtext));
    }

    return markup;
  },
  templateSelection: function (data) {
    return data.text || data.id;
  },
            minimumInputLength: 1
        });
    });

    
}


$(document).ready(function() {
    if($(".nav-group-sub li .active").length){
            $(".nav-group-sub li .active").parent("li").parent("ul").parent(".nav-item-submenu").addClass("active nav-item-expanded nav-item-open")
        }
});

$( document ).ajaxStop(function(){
      loading("remove")
      $(".ui-pnotify.bg-light.border-dark").remove()
    });

    $( document ).ajaxStart(function(){
        if ( document.hasFocus() ) {
            loading()
        }else{
            
        }
    });