<?= $this->extend('layouts/public') ?>
<?= $this->section('content') ?>

<form method="post" accept-charset="utf-8" class="ajax" enctype="multipart/form-data">

    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-md-12">
                <div class="message"></div>

                <div class="d-flex justify-content-between align-items-center">
                    <h3 class=""><i class="fas fa-user-edit fa-1x"></i> <?= tr("Update my profile") ?></h3>
                    <div class="">
                        <a href="<?= base_url('profile') ?>" class="btn btn-light btn-sm">
                            <?= tr("Back to Profile") ?> <i class="fas fa-arrow-left"></i>
                        </a>
                    </div>
                </div>

                <div class="card">


                    <div class="card-body ">


                        <!-- Profile Image Section -->
                        <div class="row mb-4 mt-5">
                            <div class="col-12 text-center">
                                <div class="profile-image-container">
                                    <?php if($profile->image_file > 0): ?>
                                    <img src="<?= $profile->getImageUrl() ?>" alt="<?= tr("Profile Image") ?>"
                                        class="img-fluid rounded-circle shadow"
                                        style="width: 120px; height: 120px; object-fit: cover;"
                                        id="profile-image-preview">
                                    <?php else: ?>
                                    <img src="<?= base_url('assets/images/profile.png') ?>"
                                        alt="<?= tr("Profile Image") ?>" class="img-fluid rounded-circle shadow"
                                        style="width: 120px; height: 120px; object-fit: cover;"
                                        id="profile-image-preview">
                                    <?php endif ?>
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-link btn-sm text-primary"
                                            onclick="$('#image_file').trigger('click')">
                                            <i class="fas fa-camera"></i> <?= tr("Change Image") ?>
                                        </button>
                                        <?= storage()->input(["style"=>"display:none","id"=>"image_file","name"=>"image_file","onchange"=>"previewImage(this)"]) ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Personal Information Section -->
                        <h5 class="text-primary border-bottom pb-2 mb-4 mt-3"><?= tr("Personal Information") ?></h5>

                        <div class="row">
                            <div class="col-md-6">
                                <label required><?= tr("Full name Arabic") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="far fa-user"></i></span>
                                    </div>
                                    <input type="text" name="name" class="form-control"
                                        value="<?= esc($profile->name) ?>">
                                </div>
                                <br>
                            </div>

                            <div class="col-md-6">
                                <label required><?= tr("Full name English") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="far fa-user"></i></span>
                                    </div>
                                    <input type="text" name="name_en" class="form-control"
                                        value="<?= esc($profile->name_en) ?>">
                                </div>
                                <br>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <label required><?= tr("Email") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-at"></i></span>
                                    </div>
                                    <input type="email" name="email" class="form-control"
                                        value="<?= esc($profile->email) ?>">
                                </div>
                                <br>
                            </div>

                            <div class="col-md-4">
                                <label required><?= tr("Card Id") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="far fa-address-card"></i></span>
                                    </div>
                                    <input type="text" name="card_id" class="form-control"
                                        value="<?= esc($profile->card_id) ?>">
                                </div>
                                <br>
                            </div>

                            <div class="col-md-4">
                                <label required><?= tr("Birth") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend ">
                                        <span class="input-group-text"><i class="far fa-calendar-alt"></i></span>
                                    </div>
                                    <input type="date" name="birth" class="form-control"
                                        value="<?= $profile->birth ? $profile->birth->format('Y-m-d') : '' ?>">
                                </div>
                                <br>
                            </div>
                        </div>

                        <div class="row">

                            <div class="col-md-4">
                                <label><?= tr("Primary phone") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="far fa-phone"></i></span>
                                    </div>
                                    <input type="tel" class="form-control text-center"
                                        value="<?= esc($profile->phone) ?>" disabled readonly>
                                    <div class="input-group-append">
                                        <span class="input-group-text"><i class="fas fa-check text-success"></i></span>
                                    </div>
                                </div>
                                <small class="text-muted"><?= tr("Verified phone number") ?></small>
                                <br>
                            </div>
                            <div class="col-md-4">
                                <label><?= tr("Secondary phone") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="far fa-phone"></i></span>
                                    </div>
                                    <input type="tel" name="phone_2" class="form-control text-center"
                                        value="<?= esc($profile->phone_2) ?>">
                                </div>
                                <br>
                            </div>

                            <div class="col-md-4">
                                <label required><?= tr("Gender") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-venus-mars"></i></span>
                                    </div>
                                    <select class="form-control rounded-0" name="gender">
                                        <option selected hidden disabled=""><?= tr("Select gender") ?></option>
                                        <?php foreach ($genders as $key => $gender): ?>
                                        <option <?= $profile->gender==$gender?'selected':'' ?> value="<?= $gender ?>">
                                            <?= tr($gender) ?></option>
                                        <?php endforeach ?>
                                    </select>
                                </div>
                                <br>
                            </div>


                        </div>

                        <!-- Location Section -->
                        <h5 class="text-primary border-bottom pb-2 mb-4 mt-3"><?= tr("Location") ?></h5>

                        <div class="row">
                            <div class="col-md-6">
                                <label required><?= tr("Governorate") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                    </div>
                                    <select class="form-control" name="reg_id" onchange="get_cities(event)">
                                        <option selected disabled="" hidden><?= tr("Select Governorate") ?></option>
                                        <?php foreach ($govs as $key => $gov): ?>
                                        <option <?= $profile->reg_id == $gov->id ? 'selected' : '' ?>
                                            value="<?= _cr($gov->id) ?>"><?= trl($gov->name_ar,$gov->name_en) ?>
                                        </option>
                                        <?php endforeach ?>
                                    </select>
                                </div>
                                <br>
                            </div>

                            <div class="col-md-6">
                                <label required><?= tr("City") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                    </div>
                                    <select class="form-control" name="city_id" id="city_select">
                                        <option selected disabled="" hidden><?= tr("Select City") ?></option>
                                        <?php if ($profile->city_id): ?>
                                        <option selected value="<?= _cr($profile->city_id) ?>">
                                            <?= city($profile->city_id)->name_ar ?? '' ?></option>
                                        <?php endif ?>
                                    </select>
                                </div>
                                <br>
                            </div>
                        </div>

                        <!-- Documents Section -->
                        <h5 class="text-primary border-bottom pb-2 mb-4 mt-3"><?= tr("Documents") ?></h5>

                        <div class="row">
                            <div class="col-md-4">
                                <label
                                    <?= !$profile->card_id_file ? 'required' : '' ?>><?= tr("Card ID copy") ?></label>
                                <?= storage()->input(["name"=>"card_id_file"]) ?>
                                <small class="text-muted">(PDF | <?= get_option("storage_max_size") ?>)</small>
                                <?php if ($profile->card_id_file): ?>
                                <div class="mt-1">
                                    <a href="<?= $profile->getCardIdUrl() ?>" target="_blank" class="text-success">
                                        <i class="fas fa-check-circle"></i> <?= tr("Current file") ?>
                                    </a>
                                </div>
                                <?php endif ?>
                                <br>
                            </div>

                            <div class="col-md-4">
                                <label <?= !$profile->cv_file ? 'required' : '' ?>><?= tr("CV copy") ?></label>
                                <?= storage()->input(["name"=>"cv_file"]) ?>
                                <small class="text-muted">(PDF | <?= get_option("storage_max_size") ?>)</small>
                                <?php if ($profile->cv_file): ?>
                                <div class="mt-1">
                                    <a href="<?= $profile->getCvUrl() ?>" target="_blank" class="text-success">
                                        <i class="fas fa-check-circle"></i> <?= tr("Current file") ?>
                                    </a>
                                </div>
                                <?php endif ?>
                                <br>
                            </div>

                            <div class="col-md-4">
                                <label <?= !$profile->mol_file ? 'required' : '' ?>><?= tr("Ministry of Labor File") ?></label>
                                <?= storage()->input(["name"=>"mol_file"]) ?>
                                <small class="text-muted">(PDF | <?= get_option("storage_max_size") ?>)</small>
                                <?php if ($profile->mol_file): ?>
                                <div class="mt-1">
                                    <a href="<?= $profile->getMolUrl() ?>" target="_blank" class="text-success">
                                        <i class="fas fa-check-circle"></i> <?= tr("Current file") ?>
                                    </a>
                                </div>
                                <?php endif ?>
                            </div>
                        </div>

                    </div>

                    
                </div>
                

                <!-- Qualifications Section -->
                <h5 class="text-primary mt-3"><?= tr("Qualifications") ?> <span
                        class="text-danger">*</span></h5>
                <div class="card">
                    <div class="">

                        <div class="table-responsive">
                            <table class="table table-sm" id="qualifications-table">
                                <thead>
                                    <tr class="bg-primary">
                                        <th><?= tr('Major') ?></th>
                                        <th><?= tr('Qualification') ?></th>
                                        <th><?= tr('qualification_location') ?></th>
                                        <th><?= tr('qualification_year') ?></th>
                                        <th><?= tr('GPA') ?></th>
                                        <th><?= tr('Attach') ?></th>
                                        <th class="text-center"></th>
                                    </tr>
                                </thead>

                                <tbody id="qualifications-tbody">
                                    <?php foreach ($profile_qualifications as $key => $q): ?>
                                    <tr data-id="<?= _cr($q->id) ?>">
                                        <td><?= esc($q->major) ?></td>
                                        <td><?= esc($q->qualification) ?></td>
                                        <td><?= esc($q->location) ?></td>
                                        <td><?= esc($q->end_year) ?></td>
                                        <td><?= esc($q->gpa) ?></td>
                                        <td class="text-center">
                                            <i class="far fa-file text-primary"></i>
                                        </td>
                                        <td class="d-flex">
                                            <button class="btn btn-link btn-sm text-danger delete-qualification-button"
                                                type="button" data-id="<?= _cr($q->id) ?>"
                                                title="<?= tr("Delete") ?>"><i class="fas fa-trash-alt"></i></button>
                                        </td>
                                    </tr>
                                    <?php endforeach ?>
                                </tbody>

                                <tfoot id="qualification-form">
                                    <tr>
                                        <td class=" py-1 px-1">
                                            <input type="text" name="major" class="form-control"
                                                style="font-size: 1rem !important;" placeholder="<?= tr("Major") ?>">
                                        </td>
                                        <td class=" py-1 px-1">
                                            <select class="form-control" name="qualification"
                                                style="font-size: 1rem !important;">
                                                <option><?= tr("Select qualification") ?></option>
                                                <?php foreach ($qualifications as $key => $qualification): ?>
                                                <option value="<?= $qualification ?>"><?= $qualification ?></option>
                                                <?php endforeach ?>
                                            </select>
                                        </td>
                                        <td class=" py-1 px-1">
                                            <input type="text" name="location" class="form-control"
                                                style="font-size: 1rem !important;" placeholder="<?= tr("Location") ?>">
                                        </td>
                                        <td class=" py-1 px-1" style="max-width: 80px;">
                                            <input type="text" name="end_year" class="form-control"
                                                style="font-size: 1rem !important;" placeholder="<?= tr("Year") ?>">
                                        </td>
                                        <td style="max-width: 50px;" class="px-1 py-1">
                                            <input type="text" name="gpa" class="form-control"
                                                style="font-size: 1rem !important;" placeholder="<?= tr("GPA") ?>">
                                        </td>
                                        <td style="max-width: 50px;" class="px-1 py-1 text-center">
                                            <button class="btn btn-link text-primary" type="button"
                                                onclick="$('#qualification_file').trigger('click')"><i
                                                    class="far fa-paperclip fa-1x"></i></button>
                                            <?= storage()->input(["style"=>"display:none","id"=>"qualification_file","name"=>"qualification_file"]) ?>
                                        </td>
                                        <td class="p-1 text-center" style="min-width: 100px;">
                                            <button class="btn btn-primary  btn-sm" type="button"
                                                id="qualification-submit"><?= tr("Save") ?> <i
                                                    class="far fa-save"></i></button>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                    </div>
                </div>

                <!-- Experience Section -->
                <h5 class="text-primary mt-3"><?= tr("Work Experience") ?></h5>
                <div class="card">
                    <div class="">
                        <div class="table-responsive">
                            <table class="table table-sm" id="experiences-table">
                                <thead class="bg-primary">
                                    <tr >
                                        <th class=""><?= tr('experience_job') ?></th>
                                        <th class=""><?= tr('experience_location') ?></th>
                                        <th class=""><?= tr('experience_start_date') ?></th>
                                        <th class=""><?= tr('experience_end_date') ?></th>
                                        <th class="text-center"><?= tr('experience_current') ?></th>
                                        <th class="text-center "></th>
                                    </tr>
                                </thead>

                                <tbody id="experiences-tbody">
                                    <?php foreach ($profile_experiences as $key => $e): ?>
                                    <tr data-id="<?= _cr($e->id) ?>">
                                        <td><?= esc($e->job_name) ?></td>
                                        <td><?= esc($e->location) ?></td>
                                        <td><?= esc($e->start_date) ?></td>
                                        <td><?= $e->is_current ? '<span class="text-muted">'.tr('experience_current').'</span>' : esc($e->end_date) ?></td>
                                        <td class="text-center">
                                            <?php if($e->is_current): ?>
                                                <i class="fas fa-check text-success" title="<?= tr('Current Position') ?>"></i>
                                            <?php else: ?>
                                                <i class="fas fa-times text-muted"></i>
                                            <?php endif ?>
                                        </td>
                                        <td class="d-flex">
                                            <button class="btn btn-link btn-sm text-danger delete-experience-button"
                                                type="button" data-id="<?= _cr($e->id) ?>"
                                                title="<?= tr("Delete") ?>"><i class="fas fa-trash-alt"></i></button>
                                        </td>
                                    </tr>
                                    <?php endforeach ?>
                                </tbody>

                                <tfoot id="experience-form">
                                    <tr>
                                        <td class="px-1 py-1">
                                            <input type="text" name="job_name" class="form-control"
                                                style="font-size: 1rem !important;" placeholder="<?= tr("experience_job") ?>">
                                        </td>
                                        <td class="px-1 py-1">
                                            <input type="text" name="location" class="form-control"
                                                style="font-size: 1rem !important;" placeholder="<?= tr("experience_location") ?>">
                                        </td>
                                        <td class="px-1 py-1 ">
                                            <div class="d-flex">
                                                <select class="form-control rounded-right" name="start_month"
                                                    style="font-size: 1rem !important; min-width: 110px;">
                                                    <?php foreach ($months as $key => $month): ?>
                                                    <option value="<?= $month ?>"><?= $month ?></option>
                                                    <?php endforeach ?>
                                                </select>
                                                <select class="form-control rounded-left" name="start_year"
                                                    style="font-size: 1rem !important; min-width: 120px;">
                                                    <?php foreach ($years as $key => $year): ?>
                                                    <option value="<?= $year ?>"><?= $year ?></option>
                                                    <?php endforeach ?>
                                                </select>
                                            </div>
                                        </td>
                                        <td class="px-1 py-1 ">
                                            <div class="d-flex" id="end-date-fields">
                                                <select class="form-control rounded-right" name="end_month"
                                                    style="font-size: 1rem !important; min-width: 110px;">
                                                    <?php foreach ($months as $key => $month): ?>
                                                    <option value="<?= $month ?>"><?= $month ?></option>
                                                    <?php endforeach ?>
                                                </select>
                                                <select class="form-control rounded-left" name="end_year"
                                                    style="font-size: 1rem !important; min-width: 120px;">
                                                    <?php foreach ($years as $key => $year): ?>
                                                    <option value="<?= $year ?>"><?= $year ?></option>
                                                    <?php endforeach ?>
                                                </select>
                                            </div>
                                        </td>
                                        <td class="px-1 py-1 text-center">
                                            <div class="form-check">
                                                <input class="" type="checkbox" name="is_current" id="is_current" 
                                                       onchange="toggleEndDate(this)">
                                                
                                            </div>
                                        </td>
                                        <td class="p-1 text-center" style="min-width: 100px;">
                                            <button class="btn btn-primary  btn-sm" type="button"
                                                id="experience-submit"><?= tr("Save") ?> <i
                                                    class="far fa-save"></i></button>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Certificates Section -->
                <h5 class="text-primary mt-3"><?= tr("Other Certificates") ?></h5>
                <div class="card">
                    <div class="">
                        <div class="table-responsive">
                            <table class="table table-sm" id="certs-table">
                                <thead class="bg-primary">
                                    <tr >
                                        <th class=""><?= tr('Title') ?></th>
                                        <th class=""><?= tr('Attach') ?></th>
                                        <th class="text-center "></th>
                                    </tr>
                                </thead>

                                <tbody id="certs-tbody">
                                    <?php foreach ($profile_certs as $key => $c): ?>
                                    <tr data-id="<?= _cr($c->id) ?>">
                                        <td><?= esc($c->name) ?></td>
                                        <td class="text-center">
                                            <i class="far fa-file text-primary"></i>
                                        </td>
                                        <td class="d-flex">
                                            <button class="btn btn-link btn-sm text-danger delete-cert-button"
                                                type="button" data-id="<?= _cr($c->id) ?>"
                                                title="<?= tr("Delete") ?>"><i class="fas fa-trash-alt"></i></button>
                                        </td>
                                    </tr>
                                    <?php endforeach ?>
                                </tbody>

                                <tfoot id="cert-form">
                                    <tr>
                                        <td class="px-1 py-1">
                                            <input type="text" name="cert_name" class="form-control"
                                                style="font-size: 1rem !important;" placeholder="<?= tr("File title example: Certificate of Completion") ?>">
                                        </td>
                                        <td style="max-width: 50px;" class="px-1 py-1 text-center">
                                            <button class="btn btn-link text-primary" type="button"
                                                onclick="$('#cert_file').trigger('click')"><i
                                                    class="far fa-paperclip fa-1x"></i></button>
                                            <?= storage()->input(["style"=>"display:none","id"=>"cert_file","name"=>"cert_file"]) ?>
                                        </td>
                                        <td class="p-1 text-center" style="min-width: 50px;">
                                            <button class="btn btn-primary  btn-sm" type="button"
                                                id="cert-submit"><?= tr("Save") ?> <i class="far fa-save"></i></button>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-5">
                    <button class="btn btn-primary btn-lg px-3 pt-2"><?= tr("Update Profile") ?> <i
                            class="fas fa-save"></i></button>
           
                </div>
            </div>
        </div>
    </div>

</form>

<script type="text/javascript">
function esc(str) {
    return $('<div>').text(str).html();
}

function previewImage(input) {
    if (input.files && input.files[0]) {
        var reader = new FileReader();
        reader.onload = function(e) {
            $('#profile-image-preview').attr('src', e.target.result);
        }
        reader.readAsDataURL(input.files[0]);
    }
}

function get_cities(event) {
    var reg_id = event.target.value;
    $.get('<?= base_url("home/get_cities") ?>/' + reg_id, function(data) {
        var cities = JSON.parse(data);
        var select = document.getElementById('city_select');
        select.innerHTML = '<option selected disabled hidden><?= tr("Select City") ?></option>';

        cities.forEach(function(city) {
            var option = document.createElement('option');
            option.value = city.id;
            option.textContent = city.name;
            select.appendChild(option);
        });
    });
}

function toggleEndDate(checkbox) {
    var endDateFields = document.getElementById('end-date-fields');
    var endMonthSelect = document.querySelector('select[name="end_month"]');
    var endYearSelect = document.querySelector('select[name="end_year"]');
    
    if (checkbox.checked) {
        endDateFields.style.opacity = '0.5';
        endMonthSelect.disabled = true;
        endYearSelect.disabled = true;
    } else {
        endDateFields.style.opacity = '1';
        endMonthSelect.disabled = false;
        endYearSelect.disabled = false;
    }
}

$(document).ready(function() {

    // Qualifications management
    $("#qualifications-tbody").on('click', '.delete-qualification-button', function(event) {
        event.preventDefault();

        var id = $(this).data("id");

        $.get('<?= base_url("profile/delete_qualification") ?>/' + id, function(data, textStatus, xhr) {
            if (data.success == true) {
                $('#qualifications-tbody tr[data-id="' + id + '"]').remove();
            }
        }, 'json');
    });

    $("#qualification-submit").on('click', function(event) {
        event.preventDefault();

        var major = $('#qualification-form input[name="major"]').val();
        var qualification = $('#qualification-form select[name="qualification"]').val();
        var location = $('#qualification-form input[name="location"]').val();
        var endYear = $('#qualification-form input[name="end_year"]').val();
        var gpa = $('#qualification-form input[name="gpa"]').val();

        var formData = new FormData();
        formData.append('major', major);
        formData.append('qualification', qualification);
        formData.append('location', location);
        formData.append('end_year', endYear);
        formData.append('gpa', gpa);

        var fileInput = document.getElementById('qualification_file');
        if (fileInput.files.length > 0) {
            formData.append("qualification_file", fileInput.files[0]);
        }

        var submit_button = $("#qualification-submit");
        $(submit_button).prop('disabled', true);
        var btntxt = $(submit_button).text();
        $(submit_button).empty().append(
            '<div class="spinner-border spinner-border-sm" role="status"><span class="sr-only">Loading...</span></div>'
        );

        $.ajax({
            url: "<?= base_url("profile/create_qualification") ?>",
            type: 'post',
            dataType: 'json',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {
                $(submit_button).prop('disabled', false).empty().text(btntxt);

                if (data.success && data.data) {
                    // Clear form
                    document.querySelectorAll('#qualification-form input[type="text"]')
                        .forEach(input => {
                            input.value = '';
                        });
                    document.querySelectorAll('#qualification-form select').forEach(
                        select => {
                            select.selectedIndex = 0;
                        });
                    document.querySelector('#qualification_file').value = '';

                    // Add new row
                    var newRow = '<tr data-id="' + data.data.q_id + '" >' +
                        '<td>' + esc(data.data.major) + '</td>' +
                        '<td>' + esc(data.data.qualification) + '</td>' +
                        '<td>' + esc(data.data.location) + '</td>' +
                        '<td>' + esc(data.data.end_year) + '</td>' +
                        '<td>' + esc(data.data.gpa) + '</td>' +
                        '<td class="text-center"><i class="far fa-file text-primary"></i></td>' +
                        '<td class="d-flex"><button class="btn btn-link btn-sm text-danger delete-qualification-button" type="button" data-id="' +
                        data.data.q_id +
                        '" title="<?= tr("Delete") ?>"><i class="fas fa-trash-alt"></i></button></td>' +
                        '</tr>';

                    $('#qualifications-tbody').append(newRow);
                }
            }
        });
    });

    // Experience management
    $("#experiences-tbody").on('click', '.delete-experience-button', function(event) {
        event.preventDefault();

        var id = $(this).data("id");

        $.get('<?= base_url("profile/delete_experience") ?>/' + id, function(data, textStatus, xhr) {
            if (data.success == true) {
                $('#experiences-tbody tr[data-id="' + id + '"]').remove();
            }
        }, 'json');
    });

    $("#experience-submit").on('click', function(event) {
        event.preventDefault();

        var job_name = $('#experience-form input[name="job_name"]').val();
        var location = $('#experience-form input[name="location"]').val();
        var start_month = $('#experience-form select[name="start_month"]').val();
        var start_year = $('#experience-form select[name="start_year"]').val();
        var end_month = $('#experience-form select[name="end_month"]').val();
        var end_year = $('#experience-form select[name="end_year"]').val();
        var is_current = $('#experience-form input[name="is_current"]').is(':checked') ? 1 : 0;

        var formData = new FormData();
        formData.append('job_name', job_name);
        formData.append('location', location);
        formData.append('start_month', start_month);
        formData.append('start_year', start_year);
        formData.append('end_month', end_month);
        formData.append('end_year', end_year);
        formData.append('is_current', is_current);

        var submit_button = $("#experience-submit");
        $(submit_button).prop('disabled', true);
        var btntxt = $(submit_button).text();
        $(submit_button).empty().append(
            '<div class="spinner-border spinner-border-sm" role="status"><span class="sr-only">Loading...</span></div>'
        );

        $.ajax({
            url: "<?= base_url("profile/create_experience") ?>",
            type: 'post',
            dataType: 'json',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {
                $(submit_button).prop('disabled', false).empty().text(btntxt);

                if (data.success && data.data) {
                    // Clear form
                    document.querySelectorAll('#experience-form input[type="text"]')
                        .forEach(input => {
                            input.value = '';
                        });
                    document.querySelectorAll('#experience-form select').forEach(select => {
                        select.selectedIndex = 0;
                    });
                    document.querySelector('#experience-form input[name="is_current"]').checked = false;
                    
                    // Reset end date fields
                    var endDateFields = document.getElementById('end-date-fields');
                    var endMonthSelect = document.querySelector('select[name="end_month"]');
                    var endYearSelect = document.querySelector('select[name="end_year"]');
                    endDateFields.style.opacity = '1';
                    endMonthSelect.disabled = false;
                    endYearSelect.disabled = false;

                    // Add new row
                    var endDateDisplay = data.data.is_current ? '<span class="text-muted"></span>' : esc(data.data.end_date);
                    var currentIcon = data.data.is_current ? '<i class="fas fa-check text-success" title="<?= tr('Current Position') ?>"></i>' : '<i class="fas fa-times text-muted"></i>';
                    
                    var newRow = '<tr data-id="' + data.data.e_id + '" >' +
                        '<td>' + esc(data.data.job_name) + '</td>' +
                        '<td>' + esc(data.data.location) + '</td>' +
                        '<td>' + esc(data.data.start_date) + '</td>' +
                        '<td>' + endDateDisplay + '</td>' +
                        '<td class="text-center">' + currentIcon + '</td>' +
                        '<td class="d-flex"><button class="btn btn-link btn-sm text-danger delete-experience-button" type="button" data-id="' +
                        data.data.e_id +
                        '" title="<?= tr("Delete") ?>"><i class="fas fa-trash-alt"></i></button></td>' +
                        '</tr>';

                    $('#experiences-tbody').append(newRow);
                }
            }
        });
    });

    // Certificates management
    $("#certs-tbody").on('click', '.delete-cert-button', function(event) {
        event.preventDefault();

        var id = $(this).data("id");

        $.get('<?= base_url("profile/delete_cert") ?>/' + id, function(data, textStatus, xhr) {
            if (data.success == true) {
                $('#certs-tbody tr[data-id="' + id + '"]').remove();
            }
        }, 'json');
    });

    $("#cert-submit").on('click', function(event) {
        event.preventDefault();

        var name = $('#cert-form input[name="cert_name"]').val();

        var formData = new FormData();
        formData.append('name', name);

        var fileInput = document.getElementById('cert_file');
        if (fileInput.files.length > 0) {
            formData.append("cert_file", fileInput.files[0]);
        }

        var submit_button = $("#cert-submit");
        $(submit_button).prop('disabled', true);
        var btntxt = $(submit_button).text();
        $(submit_button).empty().append(
            '<div class="spinner-border spinner-border-sm" role="status"><span class="sr-only">Loading...</span></div>'
        );

        $.ajax({
            url: "<?= base_url("profile/create_cert") ?>",
            type: 'post',
            dataType: 'json',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {
                $(submit_button).prop('disabled', false).empty().text(btntxt);

                if (data.success && data.data) {
                    // Clear form
                    document.querySelectorAll('#cert-form input[type="text"]').forEach(
                        input => {
                            input.value = '';
                        });
                    document.querySelector('#cert_file').value = '';

                    // Add new row
                    var newRow = '<tr data-id="' + data.data.c_id + '" >' +
                        '<td>' + esc(data.data.name) + '</td>' +
                        '<td class="text-center"><i class="far fa-file text-primary"></i></td>' +
                        '<td class="d-flex"><button class="btn btn-link btn-sm text-danger delete-cert-button" type="button" data-id="' +
                        data.data.c_id +
                        '" title="<?= tr("Delete") ?>"><i class="fas fa-trash-alt"></i></button></td>' +
                        '</tr>';

                    $('#certs-tbody').append(newRow);
                }
            }
        });
    });

});
</script>

<?= $this->endSection() ?>