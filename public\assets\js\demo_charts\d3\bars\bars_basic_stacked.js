/* ------------------------------------------------------------------------------
 *
 *  # D3.js - stacked bar chart
 *
 *  Demo d3.js stacked bar chart setup with .csv data source
 *
 * ---------------------------------------------------------------------------- */


// Setup module
// ------------------------------

var D3BarStacked = function() {


    //
    // Setup module components
    //

    // Chart
    var _barStacked = function() {
        if (typeof d3 == 'undefined') {
            console.warn('Warning - d3.min.js is not loaded.');
            return;
        }

        // Main variables
        var element = document.getElementById('d3-bar-stacked'),
            height = 400;


        // Initialize chart only if element exsists in the DOM
        if(element) {

            // Basic setup
            // ------------------------------

            // Define main variables
            var d3Container = d3.select(element),
                margin = {top: 5, right: 10, bottom: 20, left: 40},
                width = d3Container.node().getBoundingClientRect().width - margin.left - margin.right,
                height = height - margin.top - margin.bottom - 5;

            // Colors
            var bar_colors = ["#98abc5", "#8a89a6", "#7b6888", "#6b486b", "#a05d56", "#d0743c", "#ff8c00"];



            // Construct scales
            // ------------------------------

            // Horizontal
            var x = d3.scale.ordinal()
                .rangeRoundBands([0, width], .1, .5);

            // Vertical
            var y = d3.scale.linear()
                .rangeRound([height, 0]);

            // Colors
            var color = d3.scale.ordinal()
                .range(bar_colors);



            // Create axes
            // ------------------------------

            // Horizontal
            var xAxis = d3.svg.axis()
                .scale(x)
                .orient("bottom");

            // Vertical
            var yAxis = d3.svg.axis()
                .scale(y)
                .orient("left")
                .tickFormat(d3.format(".2s"));



            // Create chart
            // ------------------------------

            // Add SVG element
            var container = d3Container.append("svg");

            // Add SVG group
            var svg = container
                .attr("width", width + margin.left + margin.right)
                .attr("height", height + margin.top + margin.bottom)
                .append("g")
                    .attr("transform", "translate(" + margin.left + "," + margin.top + ")");



            // Load data
            // ------------------------------

            d3.csv("../../../../global_assets/demo_data/d3/bars/bars_stacked.csv", function(error, data) {

                // Filter values by key
                color.domain(d3.keys(data[0]).filter(function(key) { return key !== "State"; }));

                // Pull out values
                data.forEach(function(d) {
                    var y0 = 0;
                    d.ages = color.domain().map(function(name) { return {name: name, y0: y0, y1: y0 += +d[name]}; });
                    d.total = d.ages[d.ages.length - 1].y1;
                });

                // Sort data
                data.sort(function(a, b) { return b.total - a.total; });


                // Set input domains
                // ------------------------------

                // Horizontal
                x.domain(data.map(function(d) { return d.State; }));

                // Vertical
                y.domain([0, d3.max(data, function(d) { return d.total; })]);


                //
                // Append chart elements
                //

                // Append axes
                // ------------------------------

                // Horizontal
                svg.append("g")
                    .attr("class", "d3-axis d3-axis-horizontal")
                    .attr("transform", "translate(0," + height + ")")
                    .call(xAxis);

                // Vertical
                var verticalAxis = svg.append("g")
                    .attr("class", "d3-axis d3-axis-vertical")
                    .call(yAxis);

                // Add text label
                verticalAxis.append("text")
                    .attr("class", "d3-axis-title")
                    .attr("transform", "rotate(-90)")
                    .attr("y", 10)
                    .attr("dy", ".71em")
                    .style("text-anchor", "end")
                    .text("Population");


                // Add bars
                // ------------------------------

                // Group values
                var state = svg.selectAll(".bar-group")
                    .data(data)
                    .enter()
                    .append("g")
                        .attr("class", "bar-group")
                        .attr("transform", function(d) { return "translate(" + x(d.State) + ",0)"; });

                // Append bars
                state.selectAll(".d3-bar")
                    .data(function(d) { return d.ages; })
                    .enter()
                    .append("rect")
                        .attr("class", "d3-bar")
                        .attr("width", x.rangeBand())
                        .attr("y", function(d) { return y(d.y1); })
                        .attr("height", function(d) { return y(d.y0) - y(d.y1); })
                        .style("fill", function(d) { return color(d.name); });


                // Add legend
                // ------------------------------

                // Create legend
                var legend = svg.selectAll(".d3-legend")
                    .data(color.domain().slice().reverse())
                    .enter()
                    .append("g")
                        .attr("class", "d3-legend")
                        .attr("transform", function(d, i) { return "translate(0," + i * 20 + ")"; });

                // Legend indicator
                legend.append("rect")
                    .attr("x", width - 18)
                    .attr("width", 18)
                    .attr("height", 18)
                    .style("fill", color);

                // Legend text
                legend.append("text")
                    .attr("x", width - 24)
                    .attr("y", 9)
                    .attr("dy", ".35em")
                    .style("text-anchor", "end")
                    .text(function(d) { return d; });
            });



            // Resize chart
            // ------------------------------

            // Call function on window resize
            window.addEventListener('resize', resize);

            // Call function on sidebar width change
            var sidebarToggle = document.querySelector('.sidebar-control');
            sidebarToggle && sidebarToggle.addEventListener('click', resize);

            // Resize function
            // 
            // Since D3 doesn't support SVG resize by default,
            // we need to manually specify parts of the graph that need to 
            // be updated on window resize
            function resize() {

                // Layout variables
                width = d3Container.node().getBoundingClientRect().width - margin.left - margin.right;


                // Layout
                // -------------------------

                // Main svg width
                container.attr("width", width + margin.left + margin.right);

                // Width of appended group
                svg.attr("width", width + margin.left + margin.right);


                // Axes
                // -------------------------

                // Horizontal ranges
                x.rangeRoundBands([0, width], .1, .5);

                // Horizontal axis
                svg.selectAll('.d3-axis-horizontal').call(xAxis);


                // Chart elements
                // -------------------------

                // Bar group
                svg.selectAll('.bar-group').attr("transform", function(d) { return "translate(" + x(d.State) + ",0)"; });

                // Bars
                svg.selectAll('.d3-bar').attr("width", x.rangeBand()).attr("x", function(d) { return x(d.name); });

                // Legend
                svg.selectAll(".d3-legend text").attr("x", width - 24);
                svg.selectAll(".d3-legend rect").attr("x", width - 18);
            }
        }
    };


    //
    // Return objects assigned to module
    //

    return {
        init: function() {
            _barStacked();
        }
    }
}();


// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function() {
    D3BarStacked.init();
});
