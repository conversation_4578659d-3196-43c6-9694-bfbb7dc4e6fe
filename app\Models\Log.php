<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Log extends Model
{
    protected $table = 'logs';

    protected $fillable=[
        "description","user_id"
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public static  function new($description){
        self::create([
            "description" => $description,
            "user_id" => auth()->id,  // Ensure this is called correctly
        ]);
    }
}
