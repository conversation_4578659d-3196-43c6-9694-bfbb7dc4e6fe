<?php
namespace App\Libraries;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Security\Exceptions\SecurityException;
use CodeIgniter\Debug\ExceptionHandlerInterface;
use Throwable;

class DefaultException implements ExceptionHandlerInterface
{

    public function handle(Throwable $exception, RequestInterface $request, ResponseInterface $response, int $statusCode, int $exitCode): void
    {
        // Check if the request is an AJAX request or based on some other condition
        if ($request->isAJAX() || $this->shouldReturnJson($exception)) {
            $responseData = [
                'success' => false,
               "action"=>"doswal({
                                        title: '',
                                        text: 'انتهت فترة السماح بالعمل/ طلب غير مستوفي ، يرجى إعادة المحاولة',
                                        icon: 'success',
                                        confirmButtonText: 'رجوع',
                                        showCancelButton: false, // Optional, if you don't want a cancel button
                                        focusConfirm: true,
                                        preConfirm: () => {
                                            window.location.reload() ;
                                        }
                                    });", // Customize the message as needed
            ];

            // Set the appropriate status code for the response
            $response->setStatusCode($statusCode);

            // Set the Content-Type to application/json
            $response->setHeader('Content-Type', 'application/json');

            // Set the JSON response body
            $response->setJSON($responseData);

            // Send the response back to the client
            $response->send();

            if (ENVIRONMENT !== 'testing') {
                exit($exitCode);
            }

            return;
        }

        // Handle non-AJAX requests here (you can use your existing logic)

        // ...
    }

    private function shouldReturnJson(Throwable $exception): bool
    {
        // Implement your logic to determine if a JSON response should be returned
        // Example: return $exception instanceof SomeSpecificExceptionType;
        return false; // Default behavior
    }

 
}