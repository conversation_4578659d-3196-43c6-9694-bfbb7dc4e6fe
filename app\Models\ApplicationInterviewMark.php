<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ApplicationInterviewMark extends Model
{
    protected $table = 'application_interview_marks';

    protected $fillable = [
        'application_id',
        'marks',
        'note',
    ];

    protected $casts = [
        'marks' => 'array',
    ];

    public function application()
    {
        return $this->belongsTo(Application::class, 'application_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
