<?php

namespace App\Controllers;



use App\Models\User;
use App\Models\Role;
use Illuminate\Database\Capsule\Manager as DB;
use PgSql\Lob;
use App\Models\Log;

class Roles extends BaseController
{

    public function index()
    {   
        if (!_can("admins")) {
            return _error_code(403);
        }
        
        $data=[];

        $this->d['page']['title']=tr("Roles");
        $this->d['nav']=[
            "active"=>"admins",
            "breadcrumb"=>[
                tr("Dashboard")=>base_url("dashboard"),
                tr("Roles")=>"",
            ]
        ];

        $this->d['list']=Role::all();

        return view("roles/index",$this->d);

    }

    public function create()
    {   

        if (!_can("admins")) {
            return _error_code(403);
        }

        if (is_post()) {
            $v = validate([
                tr("Name")=>["name","required|unique:roles,name"],

            ]);

            if ($v->passes()) {

                $role = Role::create($_POST);

                if ($role) {

                    hooks()->do("after_role_created",$role->id);

                    display()->success(tr("Created successfully."));

                    Log::new("Role created [id:$role->id]");

                    return _response([
                        "success"=>true,
                        "redirect"=>base_url("roles")
                    ]);
                }
            }

            return _response([
                "success"=>false,
                "message"=>$v->errors()->all()
            ]);
        }
        
        $data=[];

        $this->d['page']['title']=tr("Create role");
        $this->d['nav']=[
            "active"=>"admins",
            "breadcrumb"=>[
                tr("Dashboard")=>base_url("dashboard"),
                tr("Roles")=>base_url("roles"),
                tr("Create role")=>"",
            ]
        ];

        $this->d['rights'] = $this->rights;

        return view("roles/create",$this->d);
    }

    public function update($id)
    {   
        if (!_can("admins")) {
            return _error_code(403);
        }

        if (is_post()) {

            $v = validate([
              
                tr("Name")=>["name","required"],
   
            ]);

            if (!$v->passes()) {

                return _response([
                    "success"=>false,
                    "message"=>$v->errors()->all()
                ]);
            }

            $role = Role::find($id);

            $_POST['roles'] = input("roles",[]);

            if ($role) {

                $role->update($_POST);

                Log::new("Role updated [id:$role->id]");
                // hooks()->do("after_role_updated",$role->id);

                display()->success(tr("Role updated successfully."));

                return _response([
                    "success"=>true,
                    "action"=>"reload"
                ]);
            }
            
            return _response([
                "success"=>false,
                "message"=>$v->errors()->all()
            ]);
        }
        
        $data=[];

        $role = Role::find($id);
        

        $this->d['page']['title']=$role->name;
        $this->d['nav']=[
            "active"=>"admins",
            "breadcrumb"=>[
                tr("Dashboard")=>base_url("dashboard"),
                tr("Roles")=>base_url("roles"),
                $role->name=>"",
            ]
        ];

        $this->d['data'] = $role;
        $this->d['rights'] = $this->rights;

        return view("roles/update",$this->d);
    }

    public function delete($id){

        if (!_can("admins")) {
            return _error_code(403);
        }

        $role = Role::find($id);

        if ($role) {

            $role->delete();
            
            hooks()->do("after_role_deleted",$role->id);

            display()->success(tr("Deleted successfully."));

            return redirect()->to(base_url("roles"));
        }
    }

    
}