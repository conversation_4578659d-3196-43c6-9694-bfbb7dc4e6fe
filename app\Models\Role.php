<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Capsule\Manager as DB;

class Role extends Model
{


    protected $table = 'roles';
    protected $fillable = [
        'name', "description","roles","created_by","updated_by"
    ];

    protected $casts = [
        'roles' => 'array',
    ];

    public function users() {
        return $this->hasMany(User::class, 'role_id');
    }
}