<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Discipline extends Model
{
    use SoftDeletes;

    protected $table = 'disciplines';
    
    protected $fillable = [
        'name_ar',
        'name_en',
        'parent_id',
        'sort_order',
        'active'
    ];

    protected $casts = [
        'active' => 'boolean',
        'sort_order' => 'integer',
        'parent_id' => 'integer'
    ];

    protected $dates = ['created_at', 'updated_at', 'deleted_at'];

    /**
     * Get the parent discipline
     */
    public function parent()
    {
        return $this->belongsTo(Discipline::class, 'parent_id');
    }

    /**
     * Get the child disciplines
     */
    public function children()
    {
        return $this->hasMany(Discipline::class, 'parent_id')->orderBy('sort_order');
    }

    /**
     * Get all descendants recursively
     */
    public function descendants()
    {
        return $this->children()->with('descendants');
    }

    /**
     * Scope to get active disciplines only
     */
    public function scopeActive($query)
    {
        return $query->where('active', true);
    }

    /**
     * Scope to get root disciplines (no parent)
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Get display name based on current language
     */
    public function getDisplayNameAttribute()
    {
        $lang = session()->get('lang') ?? 'ar';
        return $lang === 'en' ? $this->name_en : $this->name_ar;
    }

    /**
     * Get full hierarchy path
     */
    public function getFullPathAttribute()
    {
        $path = [];
        $current = $this;
        
        while ($current) {
            array_unshift($path, $current->display_name);
            $current = $current->parent;
        }
        
        return implode(' > ', $path);
    }

    /**
     * Get indented name for dropdowns
     */
    public function getIndentedNameAttribute()
    {
        $level = $this->getLevel();
        $indent = str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $level);
        return $indent . $this->display_name;
    }

    /**
     * Get the level in hierarchy (0 = root)
     */
    public function getLevel()
    {
        $level = 0;
        $current = $this->parent;
        
        while ($current) {
            $level++;
            $current = $current->parent;
        }
        
        return $level;
    }

    /**
     * Check if this discipline has children
     */
    public function hasChildren()
    {
        return $this->children()->count() > 0;
    }

    /**
     * Get hierarchy tree (recursive)
     */
    public static function getTree($parentId = null)
    {
        return static::where('parent_id', $parentId)
                    ->active()
                    ->orderBy('sort_order')
                    ->orderBy('name_ar')
                    ->with('children')
                    ->get();
    }

    /**
     * Get flat list for dropdowns
     */
    public static function getFlatList($parentId = null, $level = 0)
    {
        $list = [];
        $disciplines = static::where('parent_id', $parentId)
                             ->active()
                             ->orderBy('sort_order')
                             ->orderBy('name_ar')
                             ->get();

        foreach ($disciplines as $discipline) {
            $indent = str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $level);
            $list[$discipline->id] = $indent . $discipline->display_name;
            
            // Add children recursively
            $children = static::getFlatList($discipline->id, $level + 1);
            $list = array_merge($list, $children);
        }

        return $list;
    }
} 