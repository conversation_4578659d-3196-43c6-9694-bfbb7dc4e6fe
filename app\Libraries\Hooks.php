<?php

namespace App\Libraries;

class Hooks
{
    protected $hooks = [];

    public function add($hook_name, callable $callback)
    {
        if (!isset($this->hooks[$hook_name])) {
            $this->hooks[$hook_name] = [];
        }
        $this->hooks[$hook_name][] = $callback;
    }

    public function do($hook_name, ...$args)
    {
        if (isset($this->hooks[$hook_name])) {
            foreach ($this->hooks[$hook_name] as $callback) {
                call_user_func_array($callback, $args);
            }
        }
    }
}