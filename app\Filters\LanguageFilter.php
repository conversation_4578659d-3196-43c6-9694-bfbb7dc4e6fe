<?php 

namespace App\Filters;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;

class LanguageFilter implements FilterInterface
{
     public function before(RequestInterface $request, $arguments = null)
    {
        $lang = $request->getGet('lang');
        session()->set('locale', "ar");
        if ($lang) {
            // session()->set('locale', $lang);
            session()->set('locale', "ar");
        }
        
        if (session('locale')) {
            // $request->setLocale(session('locale'));
            $request->setLocale("ar");
        }
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No action needed here
    }
}
