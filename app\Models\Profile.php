<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Profile extends Model
{
    use SoftDeletes;

    protected $table = 'profiles';
    protected $fillable = [
        'name',
        'name_en',
        'gender',
        'birth',
        'card_id',
        'email',
        'reg_id',
        'city_id',
        'phone',
        'phone_2',
        'cv_file',
        'mol_file',
        'card_id_file',
        'image_file',
        'is_job_seeker',
        'locked_until'
    ];

    protected $casts = [
        'birth' => 'date',
        'is_job_seeker' => 'boolean',
        'locked_until' => 'datetime'
    ];

    protected function cardId(): Attribute
    {
        return Attribute::make(
            get: fn (?string $value) => $value ? _dr($value) : null,
            set: fn (?string $value) => $value ? _cr($value) : null,
        );
    }

    protected function email(): Attribute
    {
        return Attribute::make(
            get: fn (?string $value) => $value ? _dr($value) : null,
            set: fn (?string $value) => $value ? _cr($value) : null,
        );
    }

    protected function phone(): Attribute
    {
        return Attribute::make(
            get: fn (?string $value) => $value ? _dr($value) : null,
            set: fn (?string $value) => $value ? _cr($value) : null,
        );
    }

    protected function phone2(): Attribute
    {
        return Attribute::make(
            get: fn (?string $value) => $value ? _dr($value) : null,
            set: fn (?string $value) => $value ? _cr($value) : null,
        );
    }

    public function applications(){
        return $this->hasMany(Application::class, 'profile_id');
    }

    public function qualifications(){
        return $this->hasMany(ProfileQualification::class, 'profile_id');
    }

    public function experiences(){
        return $this->hasMany(ProfileExperience::class, 'profile_id');
    }

    public function certs(){
        return $this->hasMany(ProfileCert::class, 'profile_id');
    }

    /**
     * Check if profile is complete with all required information
     */
    public function isComplete()
    {
        $requiredFields = [
            'name', 'name_en', 'gender', 'email', 'card_id', 
            'birth', 'reg_id', 'city_id', 'cv_file', 'card_id_file', 'mol_file'
        ];

        foreach ($requiredFields as $field) {
            if (empty($this->$field)) {
                return false;
            }
        }

        // At least one qualification is required
        return $this->qualifications()->count() > 0;
    }

    /**
     * Get missing required fields
     */
    public function getMissingFields()
    {
        $requiredFields = [
            'name' => tr('Full name Arabic'),
            'name_en' => tr('Full name English'), 
            'gender' => tr('Gender'),
            'email' => tr('Email'),
            'card_id' => tr('Card ID'),
            'birth' => tr('Birth Date'),
            'reg_id' => tr('Governorate'),
            'city_id' => tr('City'),
            'cv_file' => tr('CV File'),
            'card_id_file' => tr('Card ID File'),
            'mol_file' => tr('Ministry of Labor File')
        ];

        $missing = [];
        foreach ($requiredFields as $field => $label) {
            if (empty($this->$field)) {
                $missing[] = $label;
            }
        }

        if ($this->qualifications()->count() == 0) {
            $missing[] = tr('At least one qualification');
        }

        return $missing;
    }

    /**
     * Get profile completion percentage
     */
    public function getCompletionPercentage()
    {
        $totalFields = 11; // 10 required fields + qualifications
        $completedFields = $totalFields - count($this->getMissingFields());
        
        return round(($completedFields / $totalFields) * 100);
    }

    /**
     * Check if phone is confirmed
     */
    public function isPhoneConfirmed()
    {
        return !empty($this->phone);
    }

    /**
     * Get profile image URL
     */
    public function getImageUrl()
    {
        if ($this->image_file) {
            return storage()->get($this->image_file);
        }
        
        return base_url('assets/images/profile.png');
    }

    /**
     * Get CV file URL
     */
    public function getCvUrl()
    {
        if ($this->cv_file) {
            return storage()->get($this->cv_file);
        }
        
        return null;
    }

    /**
     * Get Card ID file URL
     */
    public function getCardIdUrl()
    {
        if ($this->card_id_file) {
            return storage()->get($this->card_id_file);
        }
        
        return null;
    }

    public function getMolUrl()
    {
        if ($this->mol_file) {
            return storage()->get($this->mol_file);
        }
        
        return null;
    }
}
