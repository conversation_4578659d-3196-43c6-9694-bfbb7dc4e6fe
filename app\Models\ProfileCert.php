<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProfileCert extends Model
{
    protected $table = 'profile_certs';

    protected $fillable = [
        'profile_id',
        'name',
        'file_id'
    ];

    public function profile()
    {
        return $this->belongsTo(Profile::class, 'profile_id');
    }

    public function getFileUrl()
    {
        if ($this->file_id) {
            return storage()->get($this->file_id);
        }
        return null;
    }
}
