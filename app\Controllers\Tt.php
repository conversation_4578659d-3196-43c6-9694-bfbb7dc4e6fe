<?php



namespace App\Controllers;





use App\Models\User;

use App\Libraries\Notify;

use CodeIgniter\Email\Email;

use Carbon\Carbon;



use Illuminate\Database\Capsule\Manager as DB;



class Tt extends BaseController

{





	public function index(){

		

		





		



	}



	public function sms(){



		$this->send_sms();

		// sleep(10);

		// $this->send_sms();

		// sleep(10);

		// $this->send_sms();

		// sleep(10);

		// $this->send_sms();

		// sleep(10);

		// $this->send_sms();



	}



	private function send_sms(){



		$today = Carbon::today(); // Get the start of today



	    $sms = DB::table("sms_q")

	             ->where("status", "Queue")

	             ->where("created_at", '>=', $today)

	             ->get();



		foreach ($sms as $key => $sm) {



			$check = DB::table("sms_q")

	             ->where("status", "Queue")->where("id",$sm->id)->first();



	        if ($check) {



	        	DB::table('sms_q')

                  ->where('status', 'Queue')

                  ->where('id', $sm->id)

                  ->update(['status' => 'Sending']);



	        	$phone = _dr($sm->phone);

			

				if (strlen($phone)==8) {

					_sms($phone,$sm->sms);

				}



				DB::update("UPDATE sms_q SET status = 'Done' WHERE id = ? ",[$sm->id]);

	        }



			

			

			

		}

	}



	public function email(){

		$this->send_eamil();

		sleep(20);

		$this->send_eamil();

		sleep(20);

		$this->send_eamil();



	}





	private function send_eamil(){

		$email = DB::table("email_q")

		->where("sent_01","0")

		->where("created_at", '>=', Carbon::today())->get();





		foreach ($email as $key => $em) {



			$check = DB::table("email_q")

	             ->where("sent_01", "0")->where("id",$em->id)->first();

			



			if ($check) {



				if (empty($em->send_to)) {

				    // Handle error: send_to is empty

				    log_message('error', 'Email send_to is empty');

				    return;

				}



				$recipients = json_decode($em->send_to, true);

				if (json_last_error() !== JSON_ERROR_NONE) {

				    // Handle error: JSON decode error

				    log_message('error', 'JSON decode error: ' . json_last_error_msg());

				    return;

				}



				$emailService = \Config\Services::email();

				$emailService->setFrom(env("email.fromEmail"), env("email.fromName"));

				$emailService->setMailType('html');



				foreach ($recipients as $recipient) {

				    try {

				        $emailService->setTo(_dr($recipient[0]));

				        $subject = strlen($em->title) > 1 ? $em->title:env("email.fromName");

				        $emailService->setSubject($subject);

				        $emailService->setMessage($em->template);



				        if (!$emailService->send()) {

				            // Log the error

				            $error = $emailService->printDebugger(['headers']);

				            log_message('error', 'Email sending error: ' . print_r($error, true));

				        }

				    } catch (\Exception $e) {

				        // Handle the exception

				        log_message('error', 'Email sending exception: ' . $e->getMessage());

				    }

				}

				DB::update("UPDATE email_q SET sent_01 = 1 WHERE id = ? ",[$em->id]);

			}

		}

	}

}