<?php
use Config\Services;
use Illuminate\Database\Capsule\Manager as DB;
use App\Models\User;
use App\Libraries\Navigation;
use App\Libraries\Storage;
use App\Models\IpRateLimit;

function check_rate_limit($ipAddress, $maxRequests, $timeFrame)
{
    $currentTime = date('Y-m-d H:i:s');
    $record = IpRateLimit::where('ip_address', $ipAddress)->first();

    if ($record) {
        $lastRequestTime = strtotime($record->last_request_time);
        $timeDifference = strtotime($currentTime) - $lastRequestTime;

        if ($timeDifference <= $timeFrame) {
            if ($record->request_count >= $maxRequests) {
                return ['allowed' => false];
            }
            $record->increment('request_count');
        } else {
            $record->request_count = 1;
            $record->last_request_time = $currentTime;
            $record->save();
        }
    } else {
        IpRateLimit::create([
            'ip_address' => $ipAddress,
            'request_count' => 1,
            'last_request_time' => $currentTime
        ]);
    }

    return ['allowed' => true];
}


function escap($var){
	return esc($var);
}

function a2e($string) {
    $arabic_numbers = array('٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩');
    $english_numbers = array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9');
    
    return str_replace($arabic_numbers, $english_numbers, $string);
}


function navigation()
{

    if (!isset($navigation)) {
        $navigation = service('navigation');
    }
    return $navigation;
}


function _error_code($code){
    $response = service('response');
    return $response->setStatusCode($code);
}


function storage(){
    return new Storage();
}

function input($field = "", $default = false) {
    if (isset($_POST[$field])) {
        return $_POST[$field];
    }

    if (isset($_FILES[$field])) {
        // Check if there are multiple files uploaded
        $multiple = is_array($_FILES[$field]['name']);

        if ($multiple) {
            $numFiles = count($_FILES[$field]['name']);
            $noFileErrors = array_fill(0, $numFiles, UPLOAD_ERR_NO_FILE);

            // If all files have UPLOAD_ERR_NO_FILE error, return default
            if ($_FILES[$field]['error'] == $noFileErrors) {
                return $default;
            }
        } else {
            // Check if there is an uploaded file
            if ($_FILES[$field]['error'] === UPLOAD_ERR_NO_FILE) {
                return $default;
            }
        }

        // Handle other error cases
        // You can expand this to handle multiple files if needed
        switch ($_FILES[$field]['error'][0]) {
            case UPLOAD_ERR_OK:
                break;
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
            default:
                // You can handle specific errors or return the default value
                return $default;
        }

        return $_FILES[$field];
    }

    if (isset($_GET[$field])) {
        return $_GET[$field];
    }

    return $default;
}

function is_json($string) {
 return is_string($string) && is_array(json_decode($string, true)) && (json_last_error() == JSON_ERROR_NONE) ? true : false;
}

function is_ajax() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest';
}

function _ip() {
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    }
    elseif (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        // IP address from shared internet
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        // IP address passed from proxy
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        // Otherwise, remote address is suitable
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}

// old() 
function _o($name,$value=""){

	if (isset($_POST[$name])) {

		if (is_json($_POST[$name])) {
			return $_POST[$name];
		}
		return ($_POST[$name]);
	}else{
		if (is_json($value)) {
			return $value;
		}
		return ($value);
	}

}	

function timestamp(){

	return date("Y-m-d H:i:s");
}

function tr($word, ...$replacements) {
    // Get the localized string
    $localizedString = lang(get_local() . "." . $word);

    // If string not found, return the original word
    if ($localizedString === get_local() . "." . $word) {
        write_untranslated_to_file($word, get_local()); // Optionally write to an untranslated strings file
        $localizedString = $word;
    }

    // Since $replacements is an array of arrays, flatten it into one associative array
    $replacements = count($replacements) === 1 && is_array($replacements[0]) ? $replacements[0] : $replacements;

    // Replace any dynamic attributes
    foreach ($replacements as $attribute => $value) {
        $localizedString = str_replace(':' . $attribute, $value, $localizedString);
    }

    return $localizedString;
}

function write_untranslated_to_file($word, $locale) {
    $filePath = APPPATH . "Language/{$locale}_untranslated.php";

    // Initialize an empty array to hold untranslated strings
    $untranslatedStrings = [];

    // Read existing untranslated strings from the file
    if (file_exists($filePath)) {
        $contents = file_get_contents($filePath);
        $untranslatedStrings = json_decode($contents, true);
    }

    // Check if the word is already in the untranslated list
    if (!isset($untranslatedStrings[$word])) {
        $untranslatedStrings[$word] = $word;

        // Write the updated array back to the file
        file_put_contents($filePath, json_encode($untranslatedStrings, JSON_PRETTY_PRINT));
    }
}


function _l($word,$lang=""){
    
    return tr($word);
}


function trl($ar_label='', $en_label='')
{
    $language = Services::request()->getLocale();
    if ($language === "ar") {
        if (strlen($ar_label) > 0) {
            return esc($ar_label);
        } else {
            return esc($en_label);
        }
    } else {
        if (strlen($ar_label) > 0) {
            return esc($en_label);
        } else {
            return esc($ar_label);
        }
    }
}

///encrypt string
function _cr($data){
    $output = false;
        $encrypt_method = "AES-256-CBC";
        $secret_key = "8409ee95a8c96d5af83d16002c9dc5d5";
    $secret_iv = "12321318409ee95a8c96d5af83d16002c9dc5d5123";
        $key = hash('sha256', $secret_key);
        $iv = substr(hash('sha256', $secret_iv), 0, 16);
        $output = openssl_encrypt($data, $encrypt_method, $key, 0, $iv);
        $output = base64_encode($output);
        return $output;
    
}


//decrypt string
function _dr($data){
    $output = false;
    $encrypt_method = "AES-256-CBC";
    $secret_key = "8409ee95a8c96d5af83d16002c9dc5d5";
    $secret_iv = "12321318409ee95a8c96d5af83d16002c9dc5d5123";
    $key = hash('sha256', $secret_key);
    $iv = substr(hash('sha256', $secret_iv), 0, 16);
    $output = openssl_decrypt(base64_decode($data), $encrypt_method, $key, 0, $iv);
    return $output;

}



function current_lang()
{
    $session = \Config\Services::session();
    return $session->get('locale') ?? 'en'; // default to 'en' if no session locale is set
}

function get_local(){
    return current_lang();
}


function get_reg($id){
    return DB::table("regions")->find($id);
}
function get_city($id){
    return DB::table("cities")->find($id);
}


if (!function_exists('is_post')) {
    function is_post(): bool
    {
        return ($_SERVER['REQUEST_METHOD'] === 'POST');
    }
}

function _response($data = []) {
    // Get the response object
    $response = service('response');

    // Set Content-Type Header
    $response->setContentType('application/json');

    // Add security headers
    $response->setHeader('X-Frame-Options', 'SAMEORIGIN');
    $response->setHeader('X-Content-Type-Options', 'nosniff');
    $response->setHeader('X-XSS-Protection', '1; mode=block');
    $response->setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    // Add Content-Security-Policy or any other security headers as needed
    // $response->setHeader('Content-Security-Policy', 'default-src \'self\'; ...');

    // CSRF protection headers - only if necessary and specifically handling CSRF tokens in headers
    $response->setHeader(csrf_token(), csrf_hash());

    // Prepare response data
    $responseData = [
        'status' => 200,
        'message' => $data['message'] ?? [],
        'data' => $data['data'] ?? [],
        "success" => $data['success'] ?? true,
        "action" => $data['action'] ?? "",
        "type" => $data['type'] ?? ($data['success'] ?? true) ? "success" : "error"
    ];

    // Set HTTP response code
    $response->setStatusCode(200);

    // Send the JSON response
    return $response->setJSON($responseData);
}


function _d($date){
    return date("Y-m-d",strtotime($date));
}
function auth(){

    $auth = new \App\Controllers\Auth();

    if ($auth->isLoggedIn()) {
        
       
        return User::find(session()->get("id"));
    }

    return false;
}

function _can($role){
    if (!auth()) {
        return false;
    }

    if (strlen($role)==0) {
        return true;
    }

    return User::find(session()->get("id"))->can($role);
}


function profile_auth(){

    if(session()->get("profile_logged_in") && session()->get("profile_id")){
        return \App\Models\Profile::find(session()->get("profile_id"));
    }

    return false;
}

function isMobile(){
    return false;
}
//ajax
function reload()
{
    return json_encode(['action' => 'reload']);
    die();
}

function redirect_to($url)
{
    if (is_ajax()) {
        return json_encode([
            'action' => 'redirect',
            'url' => $url
        ]);
        die;
    }
    
    return url($url)->go();
}

function city($city_id){
    return DB::table("cities")->where("id",$city_id)->first();
}

function reg($reg_id){
    return DB::table("regions")->where("id",$reg_id)->first();
}

function _date($format="Y-m-d",$strtotime=false){
    if ($strtotime) {
        return date($format, strtotime($strtotime));
    }

    return date($format);
}