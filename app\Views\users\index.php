<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="d-flex mb-1 justify-content-sm-between">
	<div>
		<h3><?= tr("Users") ?></h3>
	</div>
	<div>
		<a class="btn btn-primary" href="<?= base_url("users/new") ?>"><i class="fas fa-plus"></i> <?= tr("New user") ?></a>

	</div>
</div>
<div class="card shadow">
	<!-- <div class="card-header bg-primary"></div> -->
	<table class="table datatable table-striped table-hover table-sm">
		<thead class="bg-primary text-white">
			<tr>
				<th><?= tr("No.") ?></th>
				<th><?= tr("Name") ?></th>
				<th><?= tr("Phone") ?></th>
				<th><?= tr("Email") ?></th>
				<th><?= tr("Role") ?></th>
			</tr>
		</thead>
		<tbody>
			<?php foreach ($list as $key => $item): ?>
				<tr onclick="location.href='<?= base_url("users/view/".($item->id)) ?>'" style="cursor: pointer;">
					<td><?= $item->id ?></td>
					<td><?= $item->name ?></td>
					<td><?= $item->phone ?></td>
					<td><?= $item->email ?></td>
					<td><?= $item->role->name ?></td>
				</tr>
			<?php endforeach ?>
		</tbody>
	</table>
</div>

<?= $this->endSection() ?>

