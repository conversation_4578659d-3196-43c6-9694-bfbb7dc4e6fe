<?php

namespace App\Controllers;


use App\Models\File;

class Files extends BaseController
{

    public function index($file) {
        
        // Validate file parameter
        if (empty($file)) {
            return $this->response->setStatusCode(400, 'Bad Request');
        }

        // Read file content using storage library
        $fileContent = storage()->read($file);
        
        if ($fileContent === false) {
            return $this->response->setStatusCode(404, 'File Not Found');
        }

        return $this->serveContent($fileContent, $file); 
    }

    private function serveContent($content, $fileName, $forceDownload = false) {
        $temp = explode(".", $fileName);
        $ext = strtolower(end($temp));
        $mimeType = $this->getMimeType($ext);

        // Set headers for proper file serving
        $this->response->setHeader('Content-Type', $mimeType);
        $this->response->setHeader('Content-Length', strlen($content));
        
        $disposition = 'inline';
        
        // Set to 'attachment' to force download, otherwise default to 'inline' for viewable types
        if ($forceDownload || !in_array($ext, ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'txt', 'html', 'svg'])) {
            $disposition = 'attachment';
        }

        $this->response->setHeader('Content-Disposition', $disposition . '; filename="' . basename($fileName) . '"');
        
        // Set cache headers for images and other static files
        if (in_array($ext, ['jpg', 'jpeg', 'png', 'gif', 'svg', 'pdf'])) {
            $this->response->setHeader('Cache-Control', 'public, max-age=31536000'); // 1 year
            $this->response->setHeader('Expires', gmdate('D, d M Y H:i:s \G\M\T', time() + 31536000));
        }
        
        $this->response->setBody($content);

        return $this->response;
    }

    private function getMimeType($extension) {
        
        // Common MIME types for fallback
        $commonMimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'pdf' => 'application/pdf',
            'txt' => 'text/plain',
            'html' => 'text/html',
            'css' => 'text/css',
            'js' => 'application/javascript',
            'json' => 'application/json',
            'xml' => 'application/xml',
            'zip' => 'application/zip',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ];

        // Check common types first
        if (isset($commonMimeTypes[$extension])) {
            return $commonMimeTypes[$extension];
        }

        // Try CodeIgniter's MIME types
        try {
            $mimeTypes = new \Config\Mimes();
            if (isset($mimeTypes::$mimes[$extension])) {
                $type = $mimeTypes::$mimes[$extension];
                return is_array($type) ? $type[0] : $type;
            }
        } catch (\Exception $e) {
            // Fallback if Config\Mimes is not available
        }

        return 'application/octet-stream';
    }

    
}