<?php

namespace App\Models\Drive;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;

class File extends Model
{
    use SoftDeletes;

    protected $table = 'files';
    
    // UUID primary key
    protected $keyType = 'string';
    public $incrementing = false;
    
    protected $fillable = [
        'id',
        'name',
        'original_name',
        'directory_id',
        'user_id',
        'file_path',
        'mime_type',
        'size_bytes',
        'hash_sha256',
        'version_number',
        'is_shared',
        'download_count',
        'status'
    ];

    protected $casts = [
        'is_shared' => 'boolean',
        'size_bytes' => 'integer',
        'version_number' => 'integer',
        'download_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    const STATUS_ACTIVE = 'ACTIVE';
    const STATUS_PROCESSING = 'PROCESSING';
    const STATUS_DELETED = 'DELETED';



    /**
     * Get the user that owns the file
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the directory containing this file
     */
    public function directory()
    {
        return $this->belongsTo(Directory::class, 'directory_id');
    }

    /**
     * Get file versions
     */
    public function versions()
    {
        return $this->hasMany(FileVersion::class, 'file_id');
    }

    /**
     * Get file operations (move, copy, etc.)
     */
    public function operations()
    {
        return $this->hasMany(FileOperation::class, 'file_id');
    }

    /**
     * Get shares for this file
     */
    public function shares()
    {
        return $this->hasMany(FileShare::class, 'file_id');
    }

    /**
     * Get activity logs for this file
     */
    public function activityLogs()
    {
        return $this->hasMany(ActivityLog::class, 'file_id');
    }

    /**
     * Scope to get active files
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope to get files for a specific user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get files in a directory
     */
    public function scopeInDirectory($query, $directoryId)
    {
        return $query->where('directory_id', $directoryId);
    }

    /**
     * Get file extension
     */
    public function getExtensionAttribute()
    {
        return pathinfo($this->name, PATHINFO_EXTENSION);
    }

    /**
     * Get human readable file size
     */
    public function getHumanSizeAttribute()
    {
        $bytes = $this->size_bytes;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if file is an image
     */
    public function isImage()
    {
        $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
        return in_array(strtolower($this->extension), $imageTypes);
    }

    /**
     * Check if file is a document
     */
    public function isDocument()
    {
        $docTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'];
        return in_array(strtolower($this->extension), $docTypes);
    }

    /**
     * Check if file can be edited with OnlyOffice
     */
    public function canEditWithOnlyOffice()
    {
        $editableTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
        return in_array(strtolower($this->extension), $editableTypes);
    }

    /**
     * Check if user can access this file
     */
    public function canAccess($userId)
    {
        // Owner can always access
        if ($this->user_id == $userId) {
            return true;
        }
        
        // Check if file is shared with user
        return $this->shares()
            ->where('shared_with_user_id', $userId)
            ->whereNull('revoked_at')
            ->where(function($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->exists();
    }

    /**
     * Check if user can edit this file
     */
    public function canEdit($userId)
    {
        // Owner can always edit
        if ($this->user_id == $userId) {
            return true;
        }
        
        // Check if file is shared with edit permission
        return $this->shares()
            ->where('shared_with_user_id', $userId)
            ->where('permission_level', 'EDIT')
            ->whereNull('revoked_at')
            ->where(function($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->exists();
    }

    /**
     * Get file URL for serving
     */
    public function getUrl()
    {
        return base_url("files/" . urlencode($this->file_path));
    }

    /**
     * Get download URL
     */
    public function getDownloadUrl()
    {
        return base_url("drive/files/{$this->id}/download");
    }

    /**
     * Increment download count
     */
    public function incrementDownloadCount()
    {
        $this->increment('download_count');
    }

    /**
     * Create a new version of this file
     */
    public function createVersion($filePath, $sizeBytes, $hashSha256, $changeNotes = null, $createdBy = null)
    {
        $version = new FileVersion([
            'file_id' => $this->id,
            'version_number' => $this->version_number + 1,
            'file_path' => $filePath,
            'size_bytes' => $sizeBytes,
            'hash_sha256' => $hashSha256,
            'change_notes' => $changeNotes,
            'created_by' => $createdBy ?: $this->user_id
        ]);
        
        $version->save();
        
        // Update current file version
        $this->version_number = $version->version_number;
        $this->file_path = $filePath;
        $this->size_bytes = $sizeBytes;
        $this->hash_sha256 = $hashSha256;
        $this->save();
        
        return $version;
    }

    /**
     * Move file to another directory
     */
    public function moveTo($targetDirectoryId, $performedBy)
    {
        $sourceDirectoryId = $this->directory_id;
        
        // Create operation record
        $operation = new FileOperation([
            'file_id' => $this->id,
            'operation_type' => 'MOVE',
            'source_directory_id' => $sourceDirectoryId,
            'target_directory_id' => $targetDirectoryId,
            'performed_by' => $performedBy
        ]);
        $operation->save();
        
        // Update file directory
        $this->directory_id = $targetDirectoryId;
        $this->save();
        
        // Update directory sizes
        if ($sourceDirectory = Directory::find($sourceDirectoryId)) {
            $sourceDirectory->updateSize();
        }
        if ($targetDirectory = Directory::find($targetDirectoryId)) {
            $targetDirectory->updateSize();
        }
        
        return $operation;
    }
}
