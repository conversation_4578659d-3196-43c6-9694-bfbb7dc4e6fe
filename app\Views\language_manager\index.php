<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between mb-3">
    <h3><?= tr("Language Management") ?></h3>
    <div class="btn-group">
        <form action="<?= base_url('language-manager/save') ?>" method="post" class="ajax">
            <input type="hidden" name="action" value="backup_files">
            <input type="hidden" name="key" id="backupKey">
            <input type="hidden" name="source" id="backupSource">
            <button type="submit" class="btn btn-secondary" id="createBackupBtn">
                <i class="fas fa-save"></i> <?= tr("Create Backup") ?>
            </button>
        </form>

        <form action="<?= base_url('language-manager/save') ?>" method="post" class="ajax">
            <input type="hidden" name="action" value="remove_duplicates">
            <button type="submit" class="btn btn-warning" id="removeDuplicatesBtn">
                <i class="fas fa-exclamation-triangle"></i> <?= tr("Remove Duplicates") ?>
            </button>
        </form>
        

        <?php if ($statistics['total_auto_translated'] > 0): ?>
            <form action="<?= base_url('language-manager/save') ?>" method="post" class="ajax">
                <input type="hidden" name="action" value="move_all_auto_translated">
                <button type="submit" class="btn btn-success" id="moveAllAutoTranslatedBtn">
                    <i class="fas fa-arrow-right"></i> <?= tr("Move Auto-Translated") ?> (<?= $statistics['total_auto_translated'] ?>)
                </button>
            </form>
        <?php endif; ?>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $statistics['total_translated'] ?></h4>
                        <p class="mb-0"><?= tr("Translated") ?></p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-language fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $statistics['total_untranslated'] ?></h4>
                        <p class="mb-0"><?= tr("Untranslated") ?></p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $statistics['total_duplicates'] ?></h4>
                        <p class="mb-0"><?= tr("Duplicates") ?></p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-copy fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0"><?= $statistics['total_entries'] ?></h4>
                        <p class="mb-0"><?= tr("Total Entries") ?></p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-list fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Navigation Tabs -->
<ul class="nav nav-tabs" id="languageTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <a class="nav-link active" id="translations-tab" data-toggle="tab" href="#translations" role="tab" aria-controls="translations" aria-selected="true">
            <i class="fas fa-list"></i> <?= tr("All Translations") ?>
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link" id="duplicates-tab" data-toggle="tab" href="#duplicates" role="tab" aria-controls="duplicates" aria-selected="false">
            <i class="fas fa-exclamation-triangle"></i> <?= tr("Duplicates") ?> 
            <?php if ($statistics['total_duplicates'] > 0): ?>
                <span class="badge badge-danger"><?= $statistics['total_duplicates'] ?></span>
            <?php endif; ?>
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link" id="untranslated-tab" data-toggle="tab" href="#untranslated" role="tab" aria-controls="untranslated" aria-selected="false">
            <i class="fas fa-clock"></i> <?= tr("Untranslated") ?>
            <?php if ($statistics['total_untranslated'] > 0): ?>
                <span class="badge badge-warning"><?= $statistics['total_untranslated'] ?></span>
            <?php endif; ?>
        </a>
    </li>
    <?php if ($statistics['total_auto_translated'] > 0): ?>
    <li class="nav-item" role="presentation">
        <a class="nav-link" id="auto-translated-tab" data-toggle="tab" href="#auto-translated" role="tab" aria-controls="auto-translated" aria-selected="false">
            <i class="fas fa-magic"></i> <?= tr("Auto-Translated") ?>
            <span class="badge badge-info"><?= $statistics['total_auto_translated'] ?></span>
        </a>
    </li>
    <?php endif; ?>
</ul>

<!-- Tab Content -->
<div class="tab-content" id="languageTabContent">
    
    <!-- All Translations Tab -->
    <div class="tab-pane fade show active" id="translations" role="tabpanel" aria-labelledby="translations-tab">
        <div class="card shadow border-0 mt-3">
            <div class="card-header">
                <h5 class="text-primary mb-0"><?= tr("All Translation Entries") ?></h5>
                <small class="text-muted"><?= tr("Manage all translation keys and their values") ?></small>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="translationsTable">
                        <thead class="thead-dark">
                            <tr>
                                <th><?= tr("Key") ?></th>
                                <th><?= tr("Translated Value") ?></th>
                                <th><?= tr("Untranslated Value") ?></th>
                                <th><?= tr("Source") ?></th>
                                <th><?= tr("Duplicate") ?></th>
                                <th><?= tr("Actions") ?></th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Duplicates Tab -->
    <div class="tab-pane fade" id="duplicates" role="tabpanel" aria-labelledby="duplicates-tab">
        <div class="card shadow border-0 mt-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="text-primary mb-0"><?= tr("Duplicate Entries") ?></h5>
                    <small class="text-muted"><?= tr("Translation keys that exist in both files") ?></small>
                </div>
                <?php if ($statistics['total_duplicates'] > 0): ?>
                    <form action="<?= base_url('language-manager/save') ?>" method="post" class="ajax">
                        <input type="hidden" name="action" value="remove_duplicates">
                        <button type="submit" class="btn btn-warning btn-sm" id="removeAllDuplicatesBtn">
                            <i class="fas fa-trash"></i> <?= tr("Remove All Duplicates") ?>
                        </button>
                    </form>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if (count($duplicates) > 0): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?= tr("Found {count} duplicate translation keys. These keys exist in both ar.php and ar_untranslated.php files.", ['count' => count($duplicates)]) ?>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th><?= tr("Translation Key") ?></th>
                                    <th><?= tr("Translated Value") ?></th>
                                    <th><?= tr("Untranslated Value") ?></th>
                                    <th><?= tr("Action") ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($duplicates as $key => $sources): ?>
                                    <?php 
                                    $translationData = $translations[$key] ?? [];
                                    ?>
                                    <tr>
                                        <td><code><?= esc($key) ?></code></td>
                                        <td><?= esc($translationData['translated'] ?? '') ?></td>
                                        <td><?= esc($translationData['untranslated'] ?? '') ?></td>
                                        <td>
                                            <form action="<?= base_url('language-manager/save') ?>" method="post" class="ajax">
                                                <input type="hidden" name="action" value="remove_duplicates">
                                                <input type="hidden" name="key" value="<?= esc($key) ?>">
                                                <button type="submit" class="btn btn-sm btn-warning remove-duplicate">
                                                    <i class="fas fa-trash"></i> <?= tr("Remove from Untranslated") ?>
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?= tr("No duplicate entries found. All translation keys are unique across files.") ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Untranslated Tab -->
    <div class="tab-pane fade" id="untranslated" role="tabpanel" aria-labelledby="untranslated-tab">
        <div class="card shadow border-0 mt-3">
            <div class="card-header">
                <h5 class="text-primary mb-0"><?= tr("Untranslated Entries") ?></h5>
                <small class="text-muted"><?= tr("Keys that need Arabic translation") ?></small>
            </div>
            <div class="card-body">
                <?php 
                $untranslatedEntries = array_filter($translations, function($item) {
                    return $item['source'] === 'untranslated';
                });
                ?>
                
                <?php if (count($untranslatedEntries) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th><?= tr("Key") ?></th>
                                    <th><?= tr("Current Value") ?></th>
                                    <th><?= tr("Action") ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($untranslatedEntries as $key => $data): ?>
                                    <tr>
                                        <td><code><?= esc($key) ?></code></td>
                                        <td><?= esc($data['untranslated']) ?></td>
                                        <td>
                                            <button class="btn btn-sm btn-primary edit-translation" data-key="<?= esc($key) ?>" data-source="untranslated">
                                                <i class="fas fa-edit"></i> <?= tr("Add Translation") ?>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?= tr("All entries have been translated! No untranslated entries found.") ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Auto-Translated Tab -->
    <?php if ($statistics['total_auto_translated'] > 0): ?>
    <div class="tab-pane fade" id="auto-translated" role="tabpanel" aria-labelledby="auto-translated-tab">
        <div class="card shadow border-0 mt-3">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="text-primary mb-0"><?= tr("Auto-Translated Entries") ?></h5>
                    <small class="text-muted"><?= tr("Entries that contain Arabic text but are still in the untranslated file") ?></small>
                </div>
                <form action="<?= base_url('language-manager/save') ?>" method="post" class="ajax">
                    <input type="hidden" name="action" value="move_all_auto_translated">
                    <button type="submit" class="btn btn-success btn-sm" id="moveAllAutoTranslatedTabBtn">
                        <i class="fas fa-arrow-right"></i> <?= tr("Move All to Translated") ?>
                    </button>
                </form>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <?= tr("Found {count} entries that already contain Arabic translations but are still in the untranslated file. You can move them to the translated file.", ['count' => $statistics['total_auto_translated']]) ?>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th><?= tr("Key") ?></th>
                                <th><?= tr("Arabic Value") ?></th>
                                <th><?= tr("Action") ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $autoTranslatedEntries = array_filter($translations, function($item) {
                                return $item['source'] === 'auto_translated';
                            });
                            ?>
                            <?php foreach ($autoTranslatedEntries as $key => $data): ?>
                                <tr>
                                    <td><code><?= esc($key) ?></code></td>
                                    <td><span class="text-success"><?= esc($data['translated']) ?></span></td>
                                    <td>
                                        <form action="<?= base_url('language-manager/save') ?>" method="post" class="ajax">
                                            <input type="hidden" name="action" value="move_to_translated">
                                            <input type="hidden" name="key" value="<?= esc($key) ?>">
                                            <button type="submit" class="btn btn-sm btn-success move-to-translated">
                                                <i class="fas fa-arrow-right"></i> <?= tr("Move to Translated") ?>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Edit Translation Modal -->
<div class="modal fade" id="editTranslationModal" tabindex="-1" role="dialog" aria-labelledby="editTranslationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTranslationModalLabel"><?= tr("Edit Translation") ?></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="editTranslationForm" class="ajax" method="post" action="<?= base_url('language-manager/save') ?>">
                
                <input type="hidden" name="action" value="update_translation">
                <input type="hidden" name="key" id="editKey">
                <input type="hidden" name="source" id="editSource">
                
                <div class="modal-body">
                    <div class="form-group">
                        <label><?= tr("Translation Key") ?></label>
                        <input type="text" class="form-control" id="displayKey" readonly>
                    </div>
                    
                    <div class="form-group">
                        <label for="translatedValue"><?= tr("Arabic Translation") ?></label>
                        <textarea class="form-control" name="translated_value" id="translatedValue" rows="3" placeholder="<?= tr("Enter Arabic translation...") ?>"></textarea>
                        <small class="form-text text-muted"><?= tr("Leave empty to mark as untranslated") ?></small>
                    </div>
                    
                    <div class="form-group" id="currentValueGroup" style="display: none;">
                        <label><?= tr("Current Untranslated Value") ?></label>
                        <input type="text" class="form-control" id="currentUntranslatedValue" readonly>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal"><?= tr("Cancel") ?></button>
                    <button type="submit" class="btn btn-primary"><?= tr("Save Translation") ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script>
$(document).ready(function() {
    
    // Initialize DataTable for all translations
    $('#translationsTable').DataTable({
        processing: true,
        ajax: {
            url: "<?= base_url('language-manager/datatable') ?>",
            type: "GET",
        },
        columns: [
            { data: 'key', width: '25%' },
            { data: 'translated', width: '25%' },
            { data: 'untranslated', width: '25%' },
            { data: 'source', width: '10%' },
            { 
                data: 'is_duplicate', 
                width: '5%',
                render: function(data, type, row) {
                    return data ? 'Yes' : 'No';
                }
            },
            { 
                data: 'actions', 
                width: '10%', 
                orderable: false,
                render: function(data, type, row) {
                    return generateActionButtons(row);
                }
            }
        ],
        order: [[0, 'asc']],
        pageLength: 25,
        language: {
            search: "<?= tr('Search') ?>:",
            lengthMenu: "<?= tr('Show') ?> _MENU_ <?= tr('entries') ?>",
            info: "<?= tr('Showing') ?> _START_ <?= tr('to') ?> _END_ <?= tr('of') ?> _TOTAL_ <?= tr('entries') ?>",
            paginate: {
                first: "<?= tr('First') ?>",
                last: "<?= tr('Last') ?>",
                next: "<?= tr('Next') ?>",
                previous: "<?= tr('Previous') ?>"
            }
        }
    });
    
    // Generate action buttons for DataTable
    function generateActionButtons(row) {
        let buttons = '<div class="btn-group btn-group-sm">';
        
        // Edit button
        buttons += '<button class="btn btn-primary btn-sm edit-translation-dt" data-key="' + 
                   escapeHtml(row.key) + '" data-toggle="tooltip" title="<?= tr("Edit") ?>">';
        buttons += '<i class="fas fa-edit"></i></button>';
        
        // Remove duplicate button
        if (row.is_duplicate) {
            buttons += '<button class="btn btn-warning btn-sm remove-duplicate-dt" data-key="' + 
                       escapeHtml(row.key) + '" data-toggle="tooltip" title="<?= tr("Remove Duplicate") ?>">';
            buttons += '<i class="fas fa-exclamation-triangle"></i></button>';
        }
        
        // Move to translated button
        if (row.source === 'auto_translated') {
            buttons += '<button class="btn btn-success btn-sm move-to-translated-dt" data-key="' + 
                       escapeHtml(row.key) + '" data-toggle="tooltip" title="<?= tr("Move to Translated") ?>">';
            buttons += '<i class="fas fa-arrow-right"></i></button>';
        }
        
        buttons += '</div>';
        return buttons;
    }
    
    // Helper function to escape HTML
    function escapeHtml(unsafe) {
        return unsafe
             .replace(/&/g, "&amp;")
             .replace(/</g, "&lt;")
             .replace(/>/g, "&gt;")
             .replace(/"/g, "&quot;")
             .replace(/'/g, "&#039;");
    }
    
    // Edit translation function
    function editTranslation(key, source = null) {
        console.log('editTranslation called with:', key, source);
        
        $('#editKey').val(key);
        $('#displayKey').val(key);
        
        if (source) {
            // Called from other tabs with known source
            $('#editSource').val(source);
            
            // For untranslated entries, set current value
            if (source === 'untranslated') {
                $('#translatedValue').val('');
                $('#currentUntranslatedValue').val(key); // Usually the untranslated value is the key
                $('#currentValueGroup').show();
            } else {
                $('#currentValueGroup').hide();
            }
        } else {
            // Called from DataTable - find the row data
            $('#editSource').val('translated'); // Default source
            
            const table = $('#translationsTable').DataTable();
            const rows = table.rows().data();
            
            for (let i = 0; i < rows.length; i++) {
                if (rows[i].key === key) {
                    const rowData = rows[i];
                    $('#translatedValue').val(rowData.translated || '');
                    $('#editSource').val(rowData.source);
                    
                    if (rowData.untranslated) {
                        $('#currentUntranslatedValue').val(rowData.untranslated);
                        $('#currentValueGroup').show();
                    } else {
                        $('#currentValueGroup').hide();
                    }
                    break;
                }
            }
        }
        
        $('#editTranslationModal').modal('show');
    }
    
    // Remove duplicate function
    function removeDuplicate(key) {
        if (confirm("<?= tr('Are you sure you want to remove this duplicate entry from the untranslated file?') ?>")) {
            $.post("<?= base_url('language-manager/save') ?>", {
                action: 'remove_duplicates',
                key: key,
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
            })
            .done(function(response) {
                if (response.success) {
                    showMessage(response.message, 'success');
                    refreshDataTableAndStats();
                } else {
                    showMessage(response.message, 'error');
                }
            })
            .fail(function() {
                showMessage(["<?= tr('An error occurred while removing duplicate') ?>"], 'error');
            });
        }
    }
    
    // Move to translated function
    function moveToTranslated(key) {
        if (confirm("<?= tr('Are you sure you want to move this entry to the translated file?') ?>")) {
            $.post("<?= base_url('language-manager/save') ?>", {
                action: 'move_to_translated',
                key: key,
                '<?= csrf_token() ?>': '<?= csrf_hash() ?>'
            })
            .done(function(response) {
                if (response.success) {
                    showMessage(response.message, 'success');
                    refreshDataTableAndStats();
                } else {
                    showMessage(response.message, 'error');
                }
            })
            .fail(function() {
                showMessage(["<?= tr('An error occurred while moving translation') ?>"], 'error');
            });
        }
    }
    
    // Edit translation button handler for buttons in other tabs (not DataTable)
    $(document).on('click', '.edit-translation', function() {
        const key = $(this).data('key');
        const source = $(this).data('source') || 'translated';
        editTranslation(key, source);
    });
    
    // Event handlers for DataTable buttons
    $(document).on('click', '.edit-translation-dt', function() {
        const key = $(this).data('key');
        editTranslation(key);
    });
    
    $(document).on('click', '.remove-duplicate-dt', function() {
        const key = $(this).data('key');
        removeDuplicate(key);
    });
    
    $(document).on('click', '.move-to-translated-dt', function() {
        const key = $(this).data('key');
        moveToTranslated(key);
    });

    
    
    // Remove all duplicates button handler

    // Form submission handler for edit translation
    $('#editTranslationForm').on('ajax:success', function(response) {
        if (response.success) {
            showMessage(response.message, 'success');
            $('#editTranslationModal').modal('hide');
            refreshDataTableAndStats();
        } else {
            showMessage(response.message, 'error');
        }
    });

    // Form submission handler for bulk operations (backup, remove duplicates, move auto-translated)
    $('form.ajax').on('ajax:success', function(response) {
        if (response.success) {
            showMessage(response.message, 'success');
            refreshDataTableAndStats();
        } else {
            showMessage(response.message, 'error');
        }
    });

    // Helper function to refresh datatable and update statistics
    function refreshDataTableAndStats() {
        // Refresh datatable if it exists and is initialized
        if ($.fn.DataTable.isDataTable('#translationsTable')) {
            $('#translationsTable').DataTable().ajax.reload();
        }
        
        // Update page content and statistics
        setTimeout(function() {
            location.reload();
        }, 1500);
    }
    

    
    // Helper function to show messages
    function showMessage(messages, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'fa-check' : 'fa-exclamation-triangle';
        
        let messageHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">';
        messageHtml += '<i class="fas ' + icon + '"></i> ';
        
        if (Array.isArray(messages)) {
            messageHtml += messages.join('<br>');
        } else {
            messageHtml += messages;
        }
        
        messageHtml += '<button type="button" class="close" data-dismiss="alert" aria-label="Close">';
        messageHtml += '<span aria-hidden="true">&times;</span></button></div>';
        
        // Show message at top of page
        $('body').prepend(messageHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>
<?= $this->endSection() ?> 