<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<div class="d-flex justify-content-between mb-1 mt-3">
    <h3><?= tr("Create Job") ?></h3>

</div>

<form class="ajax" method="post" action="<?= base_url("jobs/create") ?>" onsubmit="selectAllOptions('selectedUsers-new')">
    <?= csrf_field() ?>
    
    <div class="card shadow border-0">
        <div class="card-header">
            <h5 class="text-primary mb-0"><?= tr("Job Information") ?></h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label required><?= tr("job_name") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                            </div>
                            <input type="text" class="form-control" name="name" value="<?= set_value('name') ?>" required>
                        </div>
                        <?= validation_show_error('name') ?>
                    </div>

                    <div class="form-group">
                        <label required><?= tr("job_end_date") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            </div>
                            <input type="date" class="form-control" name="end_date" value="<?= set_value('end_date') ?>" required>
                        </div>
                        <?= validation_show_error('end_date') ?>
                    </div>

                    <div class="form-group">
                        <label><?= tr("job_count") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-users"></i></span>
                            </div>
                            <input type="number" class="form-control" name="number" value="<?= set_value('number', 1) ?>" min="1">
                        </div>
                        <?= validation_show_error('number') ?>
                    </div>

                    <div class="form-group">
                        <label><?= tr("job_grade") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-graduation-cap"></i></span>
                            </div>
                            <input type="text" class="form-control" name="grade" value="<?= set_value('grade') ?>">
                        </div>
                        <?= validation_show_error('grade') ?>
                    </div>

                    <div class="form-group">
                        <label><?= tr("job_gender") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-venus-mars"></i></span>
                            </div>
                            <select class="form-control" name="gender">
                                <option value="Both" <?= set_select('gender', 'Both', true) ?>><?= tr("Both") ?></option>
                                <option value="Male" <?= set_select('gender', 'Male') ?>><?= tr("Male") ?></option>
                                <option value="Female" <?= set_select('gender', 'Female') ?>><?= tr("Female") ?></option>
                            </select>
                        </div>
                        <?= validation_show_error('gender') ?>
                    </div>

                    <div class="form-group">
                        <label><?= tr("Nationality") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-flag"></i></span>
                            </div>
                            <input type="text" class="form-control" name="country" value="<?= set_value('country', 'Oman') ?>">
                        </div>
                        <?= validation_show_error('country') ?>
                    </div>

                    <div class="form-group">
                        <label><?= tr("job_location") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            </div>
                            <input type="text" class="form-control" name="location" value="<?= set_value('location') ?>">
                        </div>
                        <?= validation_show_error('location') ?>
                    </div>

                    <div class="form-group">
                        <label required><?= tr("Status") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-toggle-on"></i></span>
                            </div>
                            <select class="form-control" name="status" required>
                                <?php foreach ($job_statuses as $key => $status): ?>
                                    <option value="<?= $status ?>" <?= set_select('status', $status) ?>>
                                        <?= tr(ucfirst($status)) ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                        <small class="form-text text-muted">
                            <strong><?= tr("Open") ?>:</strong> <?= tr("Visible to all users and can be applied to") ?><br>
                            <strong><?= tr("Closed") ?>:</strong> <?= tr("Visible only to administrators for review") ?><br>
                            <strong><?= tr("Archived") ?>:</strong> <?= tr("No longer active, kept for historical purposes") ?>
                        </small>
                        <?= validation_show_error('status') ?>
                    </div>
                </div>

                <div class="col-md-8">
                    <div class="form-group">
                        <label required><?= tr("job_description") ?></label>
                        <textarea class="form-control summernote" id="job-description" name="description" rows="10" required><?= set_value('description') ?></textarea>
                        <?= validation_show_error('description') ?>
                    </div>

                    <div class="mt-4">
                        <h5 class="text-primary"><?= tr("Interviewers") ?></h5>
                        <div class="row justify-content-between align-items-center">
                            <div class="col-md-5">
                                <label><?= tr("Available") ?></label>
                                <select id="availableUsers-new" class="form-control" multiple style="height:150px;">
                                    <?php foreach ($users as $id => $name): ?>
                                        <option value="<?= $id?>"><?= $name ?></option>
                                    <?php endforeach ?>
                                </select>
                            </div>

                            <div class="col-md-2 text-center">
                                <button type="button" class="btn " onclick="moveUsers('availableUsers-new', 'selectedUsers-new')">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                                <br><br>
                                <button type="button" class="btn " onclick="moveUsers('selectedUsers-new', 'availableUsers-new')">
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>

                            <div class="col-md-5">
                                <label><?= tr("Selected") ?></label>
                                <select id="selectedUsers-new" name="user_id[]" class="form-control" multiple style="height:150px;">
                                    <!-- Selected users will appear here -->
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="">
                
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> <?= tr("Save") ?>
                </button>
            </div>
        </div>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section("script") ?>
<script type="text/javascript">
    $(document).ready(function() {
        // Initialize Summernote rich text editor
        $('#job-description').summernote({
            height: 300,
            minHeight: 200,
            maxHeight: 500,
            placeholder: '<?= tr("Enter job description with rich formatting...") ?>',
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['fontname', ['fontname']],
                ['fontsize', ['fontsize']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'hr']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks: {
                onInit: function() {
                    console.log('Summernote initialized for job description');
                },
                onChange: function(contents, $editable) {
                    // Trigger validation on content change
                    $('#job-description').trigger('input');
                }
            }
        });

        // Handle form submission - ensure Summernote content is synced
        $('form.ajax').on('submit', function(e) {
            // Sync Summernote content to textarea before submission
            $('#job-description').summernote('code', $('#job-description').summernote('code'));
        });

        // Handle successful form submission
        $(document).on('submit_complete', function(event, data) {
            if (data.success && data.action === 'redirect') {
                window.location.href = data.url;
            }
        });
    });

    function moveUsers(sourceId, destinationId) {
        var source = document.getElementById(sourceId);
        var destination = document.getElementById(destinationId);
        for (var i = 0; i < source.options.length; i++) {
            if (source.options[i].selected) {
                var option = source.options[i];
                destination.add(option.cloneNode(true));
                source.remove(i);
                i--;
            }
        }
    }

    function selectAllOptions(selectId) {
        var select = document.getElementById(selectId);
        for (var i = 0; i < select.options.length; i++) {
            select.options[i].selected = true;
        }
    }
</script>
<?= $this->endSection() ?>
