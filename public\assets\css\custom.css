	.print-show{
		display: none ;
	}
	@media print {
	  .print-show {
	    display: block;
	  }
	  .print-hide {
	    display: none;
	  }
	}

	/* ========================================
	   BOOTSTRAP 4 COLOR VARIABLES & OVERRIDES
	   ======================================== */
	:root {
		/* Primary Brand Colors */
		--primary: #27374D;
		--secondary: #526D82;
		--body: #EEEEEE;
		--primary_transparent: rgba(var(--primary), 0.1);
		--secondary_transparent: rgba(var(--secondary), 0.1);
		
		/* Bootstrap 4 Complete Color Palette */
		--blue: #007bff;
		--indigo: #6610f2;
		--purple: #6f42c1;
		--pink: #e83e8c;
		--red: #dc3545;
		--orange: #fd7e14;
		--yellow: #ffc107;
		--green: #28a745;
		--teal: #20c997;
		--cyan: #17a2b8;
		--white: #fff;
		--gray: #6c757d;
		--gray-dark: #343a40;
		--light: #EEEEEE;
		--dark: #343a40;
		--success: #28a745;
		--info: #17a2b8;
		--warning: #ffc107;
		--danger: #dc3545;
		--muted: #6c757d;
		
		/* Border Radius Variables */
		--border-radius: 10px;
		--border-radius-sm: 0.2rem;
		--border-radius-lg: 0.3rem;
		--border-radius-xl: 0.5rem;
		--border-radius-pill: 50rem;
		--border-radius-circle: 50%;
		
		/* Custom Border Radius Override (flat design) */
		--border-radius-override: 10px;
		--border-radius-card: 10px;
		
		/* Typography */
		--font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
		--font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
		
		/* Spacing */
		--spacer: 1rem;
		
		/* Shadows */
		--box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
		--box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
		--box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
	}

	/* ========================================
	   BUTTON OVERRIDES
	   ======================================== */
	.btn {
		padding: 0.2375rem 0.575rem;
		font-size: 0.6125rem;
		line-height: 1.5385;
		border-radius: var(--border-radius-override);
		border: 0;
		transition: all 0.3s ease;
	}

	.btn.btn-sm {
		font-size: .9rem !important;
		padding: 0.2125rem 0.75rem;
	}

	/* Primary Button */
	.btn-primary {
		background: var(--primary) !important;
		border-color: var(--primary) !important;
		color: #fff;
	}
	.btn-primary:hover, .btn-primary:focus, .btn-primary:active {
		background-color: color-mix(in srgb, var(--primary) 85%, black) !important;
		border-color: color-mix(in srgb, var(--primary) 85%, black) !important;
		color: #fff;
	}

	/* Secondary Button */
	.btn-secondary {
		background: var(--secondary) !important;
		border-color: var(--secondary) !important;
		color: #fff;
	}
	.btn-secondary:hover, .btn-secondary:focus, .btn-secondary:active {
		background-color: color-mix(in srgb, var(--secondary) 85%, black) !important;
		border-color: color-mix(in srgb, var(--secondary) 85%, black) !important;
		color: #fff;
	}

	/* Bootstrap Color Buttons */
	.btn-success { background-color: var(--success) !important; border-color: var(--success) !important; }
	.btn-danger { background-color: var(--danger) !important; border-color: var(--danger) !important; }
	.btn-warning { background-color: var(--warning) !important; border-color: var(--warning) !important; color: #212529; }
	.btn-info { background-color: var(--info) !important; border-color: var(--info) !important; }
	.btn-light { background-color: var(--light) !important; border-color: var(--light) !important; color: #212529; }
	.btn-dark { background-color: var(--dark) !important; border-color: var(--dark) !important; }

	/* Outline Buttons */
	.btn-outline-primary { color: var(--primary); border-color: var(--primary); }
	.btn-outline-primary:hover { background-color: var(--primary); border-color: var(--primary); }
	.btn-outline-secondary { color: var(--secondary); border-color: var(--secondary); }
	.btn-outline-secondary:hover { background-color: var(--secondary); border-color: var(--secondary); }
	.btn-outline-light { color: var(--light); border-color: var(--light); border: 1px solid var(--light) !important;}
	.btn-outline-light:hover { background-color: var(--light); border-color: var(--light); color: #212529; }
	/* Button Labeled */
	.btn-labeled > b {
		position: absolute;
		top: -1px;
		background-color: rgba(0, 0, 0, 0.15);
		display: block;
		line-height: 0.5;
		padding: 0.52503rem;
	}

	/* ========================================
	   BACKGROUND COLOR OVERRIDES
	   ======================================== */
	.bg-primary { background-color: var(--primary) !important; }
	.bg-secondary { background-color: var(--secondary) !important; }
	.bg-success { background-color: var(--success) !important; }
	.bg-danger { background-color: var(--danger) !important; }
	.bg-warning { background-color: var(--warning) !important; }
	.bg-info { background-color: var(--info) !important; }
	.bg-light { background-color: var(--light) !important; }
	.bg-dark { background-color: var(--dark) !important; }
	.bg-white { background-color: var(--white) !important; }
	.bg-transparent { background-color: transparent !important; }

	/* ========================================
	   TEXT COLOR OVERRIDES
	   ======================================== */
	.text-primary { color: var(--primary) !important; }
	.text-secondary { color: var(--secondary) !important; }
	.text-success { color: var(--success) !important; }
	.text-danger { color: var(--danger) !important; }
	.text-warning { color: var(--warning) !important; }
	.text-info { color: var(--info) !important; }
	.text-light { color: var(--light) !important; }
	.text-dark { color: var(--dark) !important; }
	.text-body { color: #212529 !important; }
	.text-muted { color: var(--muted) !important; }
	.text-white { color: var(--white) !important; }
	.text-black-50 { color: rgba(0, 0, 0, 0.5) !important; }
	.text-white-50 { color: rgba(255, 255, 255, 0.5) !important; }

	/* ========================================
	   BORDER COLOR OVERRIDES
	   ======================================== */
	.border-primary { border-color: var(--primary) !important; }
	.border-secondary { border-color: var(--secondary) !important; }
	.border-success { border-color: var(--success) !important; }
	.border-danger { border-color: var(--danger) !important; }
	.border-warning { border-color: var(--warning) !important; }
	.border-info { border-color: var(--info) !important; }
	.border-light { border-color: var(--light) !important; }
	.border-dark { border-color: var(--dark) !important; }
	.border-white { border-color: var(--white) !important; }

	/* ========================================
	   BADGE OVERRIDES
	   ======================================== */
	.badge {
		border-radius: var(--border-radius-override);
		font-size: 0.75em;
		font-weight: 700;
		padding: 0.35em 0.65em;
	}

	.badge-primary { background-color: var(--primary) !important; }
	.badge-secondary { background-color: var(--secondary) !important; }
	.badge-success { background-color: var(--success) !important; }
	.badge-danger { background-color: var(--danger) !important; }
	.badge-warning { background-color: var(--warning) !important; color: #212529; }
	.badge-info { background-color: var(--info) !important; }
	.badge-light { background-color: var(--light) !important; color: #212529; }
	.badge-dark { background-color: var(--dark) !important; }

	/* Badge Pills */
	.badge-pill {
		border-radius: var(--border-radius-pill);
	}

	/* ========================================
	   ALERT OVERRIDES
	   ======================================== */
	.alert {
		border-radius: var(--border-radius-override);
		border: 1px solid transparent;
	}

	.alert-primary {
		color: color-mix(in srgb, var(--primary) 80%, black);
		background-color: color-mix(in srgb, var(--primary) 15%, white);
		border-color: color-mix(in srgb, var(--primary) 25%, white);
	}

	.alert-secondary {
		color: color-mix(in srgb, var(--secondary) 80%, black);
		background-color: color-mix(in srgb, var(--secondary) 15%, white);
		border-color: color-mix(in srgb, var(--secondary) 25%, white);
	}

	.alert-success {
		color: #155724;
		background-color: #d4edda;
		border-color: #c3e6cb;
	}

	.alert-danger {
		color: #721c24;
		background-color: #f8d7da;
		border-color: #f5c6cb;
	}

	.alert-warning {
		color: #856404;
		background-color: #fff3cd;
		border-color: #ffeaa7;
	}

	.alert-info {
		color: #0c5460;
		background-color: #d1ecf1;
		border-color: #bee5eb;
	}

	/* ========================================
	   PROGRESS BAR OVERRIDES
	   ======================================== */
	.progress {
		background-color: #e9ecef;
		border-radius: var(--border-radius-override);
		overflow: hidden;
	}

	.progress-bar {
		background-color: var(--primary);
		transition: width 0.6s ease;
	}

	.progress-bar-striped {
		background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
		background-size: 1rem 1rem;
	}

	/* ========================================
	   FORM CONTROL OVERRIDES
	   ======================================== */
	.form-control {
		border: 1px solid #d8d8d8;
		border-radius: var(--border-radius-override);
		padding: 0.3375rem 0.875rem;
		height: calc(1.3385em + 0.875rem + 2px);
		line-height: 1.5385;
		box-shadow: none;
		transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
	}

	.form-control:focus {
		/* border-color: var(--primary); */
		background-color: #ffffff;
		/* box-shadow: 0 0 0 0.2rem var(--primary); */
		/* outline: 0; */
	}

	.input-group-prepend .input-group-text{
		border-top-right-radius: var(--border-radius-override) !important;
		border-bottom-right-radius: var(--border-radius-override) !important;
	}

	.input-group-append .input-group-text{
		border-top-left-radius: var(--border-radius-override) !important;
		border-bottom-left-radius: var(--border-radius-override) !important;
	}

	/* ========================================
	   CHECKBOX & RADIO OVERRIDES
	   ======================================== */
	.form-check-input[type="checkbox"],
	.form-check-input[type="radio"] {
		border-radius: var(--border-radius-override);
		border: 1px solid #d8d8d8;
	}

	.form-check-input:checked[type="checkbox"],
	.form-check-input:checked[type="radio"] {
		background-color: var(--primary);
		border-color: var(--primary);
	}

	.form-check-input:focus {
		border-color: var(--primary);
		box-shadow: 0 0 0 0.2rem var(--primary_transparent);
	}

	/* Custom Radio Buttons */
	.custom-radio .custom-control-input:checked ~ .custom-control-label::before {
		background-color: var(--primary);
		border-color: var(--primary);
	}

	.custom-radio .custom-control-input:focus ~ .custom-control-label::before {
		box-shadow: 0 0 0 0.2rem var(--primary_transparent);
	}

	/* Custom Checkboxes */
	.custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
		background-color: var(--primary);
		border-color: var(--primary);
	}

	.custom-checkbox .custom-control-input:focus ~ .custom-control-label::before {
		box-shadow: 0 0 0 0.2rem var(--primary_transparent);
	}

	.rounded{
		border-radius: var(--border-radius) !important;
	}

	/* ========================================
	   SELECT2 OVERRIDES
	   ======================================== */
	.select2-container--default .select2-selection--single {
		border: 1px solid #d8d8d8;
		border-radius: var(--border-radius-override);
		height: calc(1.3385em + 0.875rem + 2px);
		padding: 6px 12px;
	}

	.select2-container--default .select2-selection--single .select2-selection__rendered {
		line-height: inherit;
		color: #333;
	}

	.select2-container--default .select2-selection--single .select2-selection__arrow {
		height: 28px;
	}

	.select2-container--default.select2-container--focus .select2-selection--single {
		border-color: var(--primary);
		background-color: #ffffff;
		box-shadow: 0 0 0 0.2rem var(--primary_transparent);
	}

	/* ========================================
	   NAVIGATION OVERRIDES
	   ======================================== */
	.nav-pills .nav-link.active,
	.nav-pills .show > .nav-link {
		background: var(--primary) !important;
		border-radius: var(--border-radius-override);
	}

	.sidebar a.active {
		border-radius: 50px !important;

		background-color: var(--light) !important;
		margin: 0 5px;
		color: var(--primary) !important;
	}
	.sidebar{
		/* background-color: var(--primary) !important; */
		padding: 0 8px 8px 0 !important;
		box-shadow: none !important;
	}

	@media (max-width: 768px) {
		.sidebar{
			background-color: var(--primary) !important;
			background-image: url(../images/ts.png) !important
		}
	}

	.sidebar .card{
		box-shadow: none !important;
		background-color: var(--transparent) !important;
	}
	.sidebar .sidebar-content{
		overflow-y: none !important;
	}
	.ts {
		background-image: url(../images/ts.png) !important
	}

	
	/* ========================================
	   CARD & MODAL OVERRIDES
	   ======================================== */
	.card, .modal-content {
		border-radius: var(--border-radius-override);
		box-shadow: var(--box-shadow);
		border: 0;
	}

	.card-header:first-child, .modal-header {
		border-top-right-radius: var(--border-radius-card) !important;
		border-top-left-radius: var(--border-radius-card) !important;
	}

	.card-footer {
		border-bottom-right-radius: var(--border-radius-card) !important;
		border-bottom-left-radius: var(--border-radius-card) !important;
	}

	.card.card-hover {
		/* cursor: pointer; */
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.card-header-badge {
		font-size: 0.8rem;
		padding: 0.4rem 0.5rem;
		position: absolute;
		top: 0;
		left: 0;

		border-top-right-radius: 0 !important;
		border-bottom-left-radius: 0 !important;
	}

	.card.gradient-card {
		background: linear-gradient(to top, var(--body), white) !important;
	}

	.card.card-hover:hover {
		transform: translateY(-4px);
		box-shadow: var(--box-shadow-lg);
	}

	.card:not([class*='bg-']):not(.sidebar .card, .sidebar li a) {
		background: linear-gradient(to top, var(--body), white);
	}

	/* ========================================
	   DROPDOWN OVERRIDES
	   ======================================== */
	.dropdown-menu {
		border-radius: var(--border-radius-override);
		border: 0px;
		box-shadow: var(--box-shadow);
	}

	.dropdown-item:hover,
	.dropdown-item:focus {
		background-color: var(--light);
	}

	.dropdown-item.active,
	.dropdown-item:active {
		background-color: var(--primary);
	}


	.nav-pills .nav-link {
		
		border-radius: var(--border-radius-override);
		padding: 0.5rem 1rem;
		
	}



	.nav-pills .nav-link.active {
		background: var(--secondary) !important;
		border-radius: var(--border-radius-override);
	}

	/* ========================================
	   TABLE OVERRIDES
	   ======================================== */
	.table-actions {
		display: none;
	}

	tr:hover .table-actions {
		display: block;
	}

	tr:has(.table-actions) {
		height: 3rem;
		padding: 0;
	}

	tr:has(.table-actions) td {
		padding: 0 !important;
	}

	.table-primary { background-color: color-mix(in srgb, var(--primary) 15%, white); }
	.table-secondary { background-color: color-mix(in srgb, var(--secondary) 15%, white); }
	.table-success { background-color: #d4edda; }
	.table-danger { background-color: #f8d7da; }
	.table-warning { background-color: #fff3cd; }
	.table-info { background-color: #d1ecf1; }
	.table-light { background-color: var(--light); }
	.table-dark { background-color: var(--dark); color: white; }

	.table thead th:first-child{
		border-top-right-radius: var(--border-radius-card) !important;
	}
	.table thead th:last-child{
		border-top-left-radius: var(--border-radius-card) !important;	
	}

	/* ========================================
	   PAGINATION OVERRIDES
	   ======================================== */
	.page-link {
		border-radius: var(--border-radius-override);
		border: 1px solid #dee2e6;
		color: var(--primary);
	}

	.page-link:hover {
		background-color: var(--light);
		border-color: var(--primary);
		color: var(--primary);
	}

	.page-item.active .page-link {
		background-color: var(--primary);
		border-color: var(--primary);
		color: white;
	}

	.page-item.disabled .page-link {
		color: var(--muted);
		background-color: var(--white);
		border-color: #dee2e6;
	}

	/* ========================================
	   BREADCRUMB OVERRIDES
	   ======================================== */
	.breadcrumb {
		background-color: var(--light);
		border-radius: var(--border-radius-override);
	}

	.breadcrumb-item.active {
		color: var(--muted);
	}

	.breadcrumb-item + .breadcrumb-item::before {
		color: var(--muted);
	}

	/* ========================================
	   TOOLTIP & POPOVER OVERRIDES
	   ======================================== */
	.tooltip .tooltip-inner {
		background-color: var(--dark);
		border-radius: var(--border-radius-override);
	}

	.popover {
		border: 1px solid rgba(0, 0, 0, 0.2);
		border-radius: var(--border-radius);
	}

	.popover-header {
		background-color: var(--light);
		border-bottom: 1px solid #dee2e6;
	}

	/* ========================================
	   CALENDAR & EVENT OVERRIDES
	   ======================================== */
	.fc-event, .fc-event-dot {
		background-color: var(--primary);
		border-color: var(--primary);
	}

	/* ========================================
	   UTILITY CLASSES
	   ======================================== */
	img.fit {
		height: 100%;
		width: 100%;
		-o-object-fit: contain;
		object-fit: contain;
	}

	.text-end {
		text-align: end;
	}

	.text-start {
		text-align: start;
	}

	label[required]::after {
		content: "* ";
		color: var(--danger);
	}

	/* ========================================
	   GLOBAL OVERRIDES
	   ======================================== */
	body {
		background: var(--body);
	}

	* :not(i, span, h4, h3, h2, h5, h1, small) {
		font-size: 1.1rem !important;
	}