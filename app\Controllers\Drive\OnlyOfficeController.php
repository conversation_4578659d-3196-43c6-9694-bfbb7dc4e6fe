<?php

namespace App\Controllers\Drive;

use App\Controllers\BaseController;
use App\Models\Drive\File;
use App\Models\Drive\ActivityLog;
use App\Libraries\Storage;

class OnlyOfficeController extends BaseController
{
    private $onlyOfficeUrl;
    private $onlyOfficeSecret;

    public function __construct()
    {
        $this->onlyOfficeUrl = "";
        $this->onlyOfficeSecret = "";

  
    }

    /**
     * Open file in OnlyOffice editor
     */
    public function edit($fileId)
    {
        // Get OnlyOffice configuration from settings
        $this->onlyOfficeUrl = get_option('onlyoffice_url', 'http://localhost:8080');
        $this->onlyOfficeSecret = get_option('onlyoffice_secret', '');

  
        if (!auth()) {
            return redirect()->to(base_url('auth/login'));
        }

        $user = auth();
        $file = File::find($fileId);

        if (!$file || !$file->canEdit($user->id)) {
            
            $html = "<div class='alert alert-danger'>".tr("You are not authorized to edit this file")."</div>";
            return _error_code(404, $html);
        }

        if (!$file->canEditWithOnlyOffice()) {
            return _error_code(400, 'File type not supported for editing');
        }

        // Generate document configuration
        $config = $this->generateDocumentConfig($file, $user);

        // Log activity
        ActivityLog::logFileEdit($file->id, $user->id, "Opened file in OnlyOffice editor");

        $this->d['page']['title'] = tr("Edit Document");
        $this->d['nav'] = [
            "active" => "drive",
            "breadcrumb" => [
                tr("Dashboard") => base_url("dashboard"),
                tr("Drive") => base_url("drive"),
                tr("Edit Document") => ""
            ]
        ];

        $this->d['file'] = $file;
        $this->d['config'] = $config;
        $this->d['onlyOfficeUrl'] = $this->onlyOfficeUrl;

        return view('drive/onlyoffice_editor', $this->d);
    }

    /**
     * Generate OnlyOffice document configuration
     */
    private function generateDocumentConfig($file, $user)
    {
        $documentUrl = base_url("drive/onlyoffice/download/{$file->id}");
        $callbackUrl = base_url("drive/onlyoffice/callback/{$file->id}");

        $config = [
            'document' => [
                'fileType' => $file->extension,
                'key' => $this->generateDocumentKey($file),
                'title' => $file->name,
                'url' => $documentUrl,
                'permissions' => [
                    'comment' => true,
                    'download' => true,
                    'edit' => true,
                    'fillForms' => true,
                    'modifyFilter' => true,
                    'modifyContentControl' => true,
                    'review' => true
                ]
            ],
            'documentType' => $this->getDocumentType($file->extension),
            'editorConfig' => [
                'callbackUrl' => $callbackUrl,
                'lang' => 'en',
                'mode' => 'edit',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name
                ],
                'customization' => [
                    'autosave' => true,
                    'comments' => true,
                    'compactToolbar' => false,
                    'help' => true,
                    'hideRightMenu' => false,
                    'logo' => [
                        'image' => base_url('assets/img/logo.png'),
                        'imageEmbedded' => base_url('assets/img/logo.png'),
                        'url' => base_url()
                    ],
                    'zoom' => 100
                ]
            ],
            'height' => '100%',
            'width' => '100%'
        ];

        // Add JWT token if secret is configured
        if (!empty($this->onlyOfficeSecret)) {
            $config['token'] = $this->generateJWT($config);
            log_message('debug', 'OnlyOffice JWT token generated for file: ' . $file->id);
        } else {
            log_message('debug', 'OnlyOffice running without JWT token (no secret configured)');
        }

        return $config;
    }

    /**
     * Generate unique document key for OnlyOffice
     */
    private function generateDocumentKey($file)
    {
        // Create a stable document key based on file ID and hash
        // The key should remain the same for the same version of the document
        $timestamp = $file->updated_at ? $file->updated_at->getTimestamp() : time();
        return md5($file->id . '_' . $timestamp . '_' . ($file->hash_sha256 ?? ''));
    }

    /**
     * Get document type for OnlyOffice
     */
    private function getDocumentType($extension)
    {
        $extension = strtolower($extension);
        
        $wordTypes = ['doc', 'docx', 'docm', 'dot', 'dotx', 'dotm', 'odt', 'fodt', 'ott', 'rtf', 'txt'];
        $cellTypes = ['xls', 'xlsx', 'xlsm', 'xlt', 'xltx', 'xltm', 'ods', 'fods', 'ots', 'csv'];
        $slideTypes = ['ppt', 'pptx', 'pptm', 'pot', 'potx', 'potm', 'odp', 'fodp', 'otp'];

        if (in_array($extension, $wordTypes)) {
            return 'word';
        } elseif (in_array($extension, $cellTypes)) {
            return 'cell';
        } elseif (in_array($extension, $slideTypes)) {
            return 'slide';
        }

        return 'word'; // Default
    }

    /**
     * Download file for OnlyOffice
     */
    public function download($fileId)
    {
        // Get OnlyOffice configuration from settings
        $this->onlyOfficeUrl = get_option('onlyoffice_url', 'http://localhost:8080');
        $this->onlyOfficeSecret = get_option('onlyoffice_secret', '');

        $file = File::find($fileId);

        if (!$file) {
            return _error_code(404);
        }

        // Use storage library to serve file
        $fileContent = file_get_contents($file->file_path);

        if ($fileContent === false) {
            return _error_code(404);
        }

        // Set headers for OnlyOffice
        $this->response->setHeader('Content-Type', $file->mime_type);
        $this->response->setHeader('Content-Length', strlen($fileContent));
        $this->response->setHeader('Content-Disposition', 'inline; filename="' . $file->name . '"');
        $this->response->setBody($fileContent);

        return $this->response;
    }

    /**
     * Handle OnlyOffice callback for document changes
     */
    public function callback($fileId)
    {
        // Set CORS headers for OnlyOffice access
        $this->response->setHeader('Access-Control-Allow-Origin', '*');
        $this->response->setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        $this->response->setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
        
        // Handle OPTIONS request for CORS preflight
        if ($this->request->getMethod() === 'options') {
            return $this->response->setStatusCode(200);
        }

        // Get OnlyOffice configuration from settings
        $this->onlyOfficeUrl = get_option('onlyoffice_url', 'http://localhost:8080');
        $this->onlyOfficeSecret = get_option('onlyoffice_secret', '');

        // Log callback attempt with more details
        log_message('info', 'OnlyOffice callback received for file: ' . $fileId);
        log_message('debug', 'OnlyOffice callback method: ' . $this->request->getMethod());
        log_message('debug', 'OnlyOffice callback IP: ' . $this->request->getIPAddress());
        log_message('debug', 'OnlyOffice callback headers: ' . json_encode($this->request->headers()));

        try {
            $file = File::find($fileId);

            if (!$file) {
                log_message('error', 'OnlyOffice callback: File not found: ' . $fileId);
                return $this->response->setJSON(['error' => 1, 'message' => 'File not found']);
            }

            // Get callback data
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            // Log the callback data for debugging
            log_message('debug', 'OnlyOffice callback raw body: ' . $input);
            log_message('debug', 'OnlyOffice callback parsed data: ' . json_encode($data));

            if (!$data) {
                log_message('error', 'OnlyOffice callback: Invalid JSON data received');
                return $this->response->setJSON(['error' => 1, 'message' => 'Invalid JSON data']);
            }

            // Verify callback signature if secret is configured
            if (!empty($this->onlyOfficeSecret)) {
                $signature = $this->request->getHeaderLine('Authorization');
                log_message('debug', 'OnlyOffice callback signature check - Secret configured, signature: ' . $signature);
                
                if (!$this->verifySignature($input, $signature)) {
                    log_message('error', 'OnlyOffice callback: Signature verification failed');
                    return $this->response->setJSON(['error' => 1, 'message' => 'Invalid JWT signature']);
                }
                log_message('debug', 'OnlyOffice callback: Signature verification passed');
            } else {
                log_message('debug', 'OnlyOffice callback: No secret configured, skipping signature verification');
            }

        $status = $data['status'] ?? 0;
        log_message('info', 'OnlyOffice callback status: ' . $status . ' for file: ' . $fileId);

        switch ($status) {
            case 1: // Document is being edited
                log_message('debug', 'OnlyOffice: Document is being edited');
                break;
                
            case 2: // Document is ready for saving
                log_message('info', 'OnlyOffice: Document is ready for saving');
                if (isset($data['url'])) {
                    $saveResult = $this->saveDocumentFromCallback($file, $data['url'], $data);
                    if (!$saveResult) {
                        log_message('error', 'OnlyOffice: Failed to save document from callback');
                        return $this->response->setJSON(['error' => 1]);
                    }
                } else {
                    log_message('error', 'OnlyOffice: No download URL provided for saving');
                    return $this->response->setJSON(['error' => 1]);
                }
                break;
                
            case 3: // Document saving error has occurred
                log_message('error', 'OnlyOffice: Document saving error occurred');
                if (isset($data['url'])) {
                    $this->saveDocumentFromCallback($file, $data['url'], $data);
                }
                break;
                
            case 4: // Document is closed with no changes
                log_message('debug', 'OnlyOffice: Document closed with no changes');
                break;
                
            case 6: // Document is being edited, but the current document state is saved
                log_message('info', 'OnlyOffice: Document state saved (status 6)');
                if (isset($data['url'])) {
                    $this->saveDocumentFromCallback($file, $data['url'], $data);
                }
                break;
                
            case 7: // Error has occurred while force saving the document
                log_message('error', 'OnlyOffice: Error occurred while force saving (status 7)');
                if (isset($data['url'])) {
                    $this->saveDocumentFromCallback($file, $data['url'], $data);
                }
                break;
                
            default:
                log_message('warning', 'OnlyOffice: Unknown callback status: ' . $status);
                break;
        }

            log_message('debug', 'OnlyOffice callback completed successfully for file: ' . $fileId);
            return $this->response->setJSON(['error' => 0]);
            
        } catch (\Exception $e) {
            log_message('error', 'OnlyOffice callback exception: ' . $e->getMessage());
            log_message('error', 'OnlyOffice callback stack trace: ' . $e->getTraceAsString());
            return $this->response->setJSON(['error' => 1, 'message' => 'Internal server error']);
        }
    }

    /**
     * Save document from OnlyOffice callback
     */
    private function saveDocumentFromCallback($file, $downloadUrl, $callbackData)
    {
        try {
            log_message('info', 'OnlyOffice: Starting document save from URL: ' . $downloadUrl);
            
            // Create context for file_get_contents with timeout and proper headers
            $context = stream_context_create([
                'http' => [
                    'timeout' => 30,
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: CodeIgniter-OnlyOffice-Client/1.0',
                        'Accept: */*',
                        'Connection: close'
                    ]
                ]
            ]);
            
            // Download the updated document with context
            $updatedContent = file_get_contents($downloadUrl, false, $context);
            
            if ($updatedContent === false) {
                log_message('error', 'OnlyOffice: Failed to download updated document from URL: ' . $downloadUrl);
                log_message('error', 'OnlyOffice: Download error details: ' . print_r(error_get_last(), true));
                return false;
            }

            log_message('info', 'OnlyOffice: Downloaded document, size: ' . strlen($updatedContent) . ' bytes');

            // Ensure the directory exists
            $directory = dirname($file->file_path);
            if (!is_dir($directory)) {
                if (!mkdir($directory, 0755, true)) {
                    log_message('error', 'OnlyOffice: Failed to create directory: ' . $directory);
                    return false;
                }
            }

            // Save updated content directly to file
            $storageResult = file_put_contents($file->file_path, $updatedContent);
            
            if ($storageResult === false) {
                log_message('error', 'OnlyOffice: Failed to save updated document to file path: ' . $file->file_path);
                return false;
            }

            log_message('info', 'OnlyOffice: Successfully saved document to: ' . $file->file_path);

            // Update file metadata
            $newHash = hash('sha256', $updatedContent);
            $file->size = strlen($updatedContent);
            $file->hash_sha256 = $newHash;
            $file->updated_at = date('Y-m-d H:i:s');
            $file->save();

            log_message('info', 'OnlyOffice: Updated file metadata - new hash: ' . $newHash);

            // Create version notes
            $changeNotes = 'Updated via OnlyOffice';
            
            if (isset($callbackData['users']) && is_array($callbackData['users'])) {
                $userNames = array_map(function($user) {
                    return $user['name'] ?? 'Unknown';
                }, $callbackData['users']);
                $changeNotes = 'Updated by: ' . implode(', ', $userNames);
            }

            // Try to create version if method exists
            try {
                if (method_exists($file, 'createVersion')) {
                    $file->createVersion(
                        $file->file_path,
                        strlen($updatedContent),
                        $newHash,
                        $changeNotes,
                        null // No user ID for callback saves
                    );
                    log_message('info', 'OnlyOffice: Created new file version');
                } else {
                    log_message('debug', 'OnlyOffice: createVersion method not available on file model');
                }
            } catch (\Exception $e) {
                log_message('warning', 'OnlyOffice: Failed to create version: ' . $e->getMessage());
            }

            // Log activity if ActivityLog class exists
            try {
                if (class_exists('App\Models\Drive\ActivityLog')) {
                    ActivityLog::logFileEdit($file->id, null, "Document saved from OnlyOffice");
                    log_message('info', 'OnlyOffice: Logged activity');
                }
            } catch (\Exception $e) {
                log_message('warning', 'OnlyOffice: Failed to log activity: ' . $e->getMessage());
            }

            log_message('info', 'OnlyOffice: Document save completed successfully');
            return true;

        } catch (\Exception $e) {
            log_message('error', 'OnlyOffice: Error saving document: ' . $e->getMessage());
            log_message('error', 'OnlyOffice: Stack trace: ' . $e->getTraceAsString());
            return false;
        }
    }

    /**
     * Verify OnlyOffice callback signature
     */
    private function verifySignature($data, $signature)
    {
        if (empty($this->onlyOfficeSecret) || empty($signature)) {
            return false;
        }

        $expectedSignature = base64_encode(hash_hmac('sha256', $data, $this->onlyOfficeSecret, true));
        
        // Remove "Bearer " prefix if present
        $signature = str_replace('Bearer ', '', $signature);
        
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Generate JWT token for OnlyOffice document configuration
     */
    private function generateJWT($payload)
    {
        if (empty($this->onlyOfficeSecret)) {
            return null;
        }

        // JWT Header
        $header = [
            'alg' => 'HS256',
            'typ' => 'JWT'
        ];

        // Encode header and payload
        $headerEncoded = $this->base64UrlEncode(json_encode($header));
        $payloadEncoded = $this->base64UrlEncode(json_encode($payload));

        // Create signature
        $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, $this->onlyOfficeSecret, true);
        $signatureEncoded = $this->base64UrlEncode($signature);

        // Return JWT token
        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }

    /**
     * Base64 URL encode (URL-safe base64 encoding for JWT)
     */
    private function base64UrlEncode($data)
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    /**
     * Get OnlyOffice configuration for admin
     */
    public function config()
    {
        if (!auth() || !_can('admin')) {
            return _error_code(403);
        }

        $this->d['page']['title'] = tr("OnlyOffice Configuration");
        $this->d['nav'] = [
            "active" => "settings",
            "breadcrumb" => [
                tr("Dashboard") => base_url("dashboard"),
                tr("Settings") => base_url("settings"),
                tr("OnlyOffice") => ""
            ]
        ];

        $this->d['onlyOfficeUrl'] = $this->onlyOfficeUrl;
        $this->d['onlyOfficeSecret'] = $this->onlyOfficeSecret;

        return view('drive/onlyoffice_config', $this->d);
    }

    /**
     * Save OnlyOffice configuration
     */
    public function saveConfig()
    {
        if (!auth() || !_can('admin')) {
            return _response([
                'success' => false,
                'message' => ['Access denied']
            ]);
        }

        $validation = validate([
            tr("OnlyOffice URL") => ["onlyoffice_url", "required|valid_url"],
            tr("OnlyOffice Secret") => ["onlyoffice_secret", "permit_empty"]
        ]);

        if (!$validation->passes()) {
            return _response([
                'success' => false,
                'message' => $validation->errors()
            ]);
        }

        // Save configuration
        set_option('onlyoffice_url', input('onlyoffice_url'));
        set_option('onlyoffice_secret', input('onlyoffice_secret'));

        return _response([
            'success' => true,
            'message' => ['OnlyOffice configuration saved successfully']
        ]);
    }

    /**
     * Test callback URL accessibility
     */
    public function testCallback()
    {
        // Set CORS headers for OnlyOffice access
        $this->response->setHeader('Access-Control-Allow-Origin', '*');
        $this->response->setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        $this->response->setHeader('Access-Control-Allow-Headers', 'Authorization, Content-Type');
        
        // Handle OPTIONS request for CORS preflight
        if ($this->request->getMethod() === 'options') {
            return $this->response->setStatusCode(200);
        }
        
        // Return simple response to verify callback URL is accessible
        log_message('info', 'OnlyOffice: Callback test accessed from: ' . $this->request->getIPAddress());
        
        return $this->response->setJSON([
            'status' => 'ok',
            'message' => 'Callback URL is accessible',
            'timestamp' => time(),
            'ip' => $this->request->getIPAddress(),
            'method' => $this->request->getMethod(),
            'user_agent' => $this->request->getUserAgent()
        ]);
    }

    /**
     * Debug document configuration
     */
    public function debugConfig($fileId)
    {
        if (!auth() || !_can('admin')) {
            return _error_code(403);
        }

        $this->onlyOfficeUrl = get_option('onlyoffice_url', 'http://localhost:8080');
        $this->onlyOfficeSecret = get_option('onlyoffice_secret', '');

        $file = File::find($fileId);
        if (!$file) {
            return _error_code(404);
        }

        $user = auth();
        $config = $this->generateDocumentConfig($file, $user);

        $debugInfo = [
            'onlyoffice_url' => $this->onlyOfficeUrl,
            'onlyoffice_secret_configured' => !empty($this->onlyOfficeSecret),
            'file_path' => $file->file_path,
            'file_exists' => file_exists($file->file_path),
            'file_readable' => is_readable($file->file_path),
            'file_writable' => is_writable($file->file_path),
            'directory_writable' => is_writable(dirname($file->file_path)),
            'document_config' => $config,
            'callback_url' => base_url("drive/onlyoffice/callback/{$file->id}"),
            'download_url' => base_url("drive/onlyoffice/download/{$file->id}"),
            'test_callback_url' => base_url("drive/onlyoffice/test-callback")
        ];

        return $this->response->setJSON($debugInfo);
    }
}
