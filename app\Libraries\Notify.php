<?php
namespace App\Libraries;

use Illuminate\Database\Capsule\Manager as DB;

class Notify
{

	private $_txt="";
	private $_tmp_dir="email/";
	private $_template="default";
	private $_phones=[];
	private $_emails=[];
	public  $title="";
	private $_variables=[];
	private $db=[];
	

	public function __set($key,$val)
    {
        $this->_variables[$key] = $val;
    }

    public function setTemplate($template) {
	    $this->_template = $template;
	    return $this;
	}



	public function user($users){

		if (is_array($users)) {
			$list = DB::select("SELECT * FROM users WHERE id in('".implode(",", $users)."')")->get();

			foreach ($list as $key => $item) {
				array_push($this->_phones, $item->phone);
				array_push($this->_emails, [$item->name,$item->email]);
			}
		}

		if (is_numeric($users)) {
			$item = DB::select("SELECT * FROM users WHERE id = ?",[$users])->first();

			array_push($this->_phones, $item->phone);
			array_push($this->_emails, [$item->name,$item->email]);
		}
		
		return $this;
	}

	public function title($title){

		$this->title=$title;
		
		
		return $this;
	}

	public function email($email){

		if (is_array($email)) {
			array_push($this->_emails, [_cr($email[0]),$email[1]]);
		}

		if (is_string($email)) {
			array_push($this->_emails, [_cr($email),"User"]);
		}
		
		
		return $this;
	}

	public function phone($phone){

		array_push($this->_phones, _cr($phone));
		
		return $this;
	}

	public function can($access=""){

		$access = explode("|", $access);

		$roles_list = DB::table("roles")->get();


		foreach ($roles_list as $key => $role) {
			foreach ($access as $key => $ac) {
				if (in_array($ac, json_decode($role->roles))) {

					$list = DB::select("SELECT * FROM users WHERE role_id =?",[$role->id])->get();

					foreach ($list as $key => $item) {
						array_push($this->_phones, $item->phone);
						array_push($this->_emails, [$item->name,$item->email]);
					}
				}
			}
		}

		return $this;
	}


	public function text($txt=""){
		$this->_variables['email_text'] = $txt;
		$this->_txt = $txt;

		return $this;
	}

	public function send_email(){

		$content = view($this->_tmp_dir . $this->_template, $this->_variables);

        DB::insert(
            "INSERT INTO email_q(template, send_to, title) VALUES (?,?,?)",
            [
                $content,
                json_encode($this->_emails),
                $this->title
            ]
        );

        return $this;
	}

	public function send_sms(){
		$this->_phones=array_unique($this->_phones);

		foreach ($this->_phones as $key => $phone) {
			if (strlen($this->_txt??'')>0) {
				DB::table("sms_q")->insert([
					"sms"=>$this->_txt??'',
					"phone"=>$phone
				]);
			}
		}

		
		return $this;
	}

	public function all(){
		$this->send_sms();
		$this->send_email();
		return $this;
	}

}
