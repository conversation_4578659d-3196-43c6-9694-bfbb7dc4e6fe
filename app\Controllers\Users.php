<?php

namespace App\Controllers;


use App\Models\User;
use Illuminate\Database\Capsule\Manager as DB;
use App\Libraries\Notify;
use Carbon\Carbon;
use App\Models\Log;
use RobThree\Auth\TwoFactorAuth;
use <PERSON>Three\Auth\Providers\Qr\QRServerProvider;

class Users extends BaseController
{



    function __construct(){

    }

    public function index()
    {   

        if (!_can("admins")) {
            return _error_code(403);
        }
        
        $data=[];

        $this->d['page']['title']=tr("Users");
        $this->d['nav']=[
            "active"=>"Admins",
            "breadcrumb"=>[
                tr("Dashboard")=>base_url("dashboard"),
                tr("Users")=>"",
            ]
        ];

        $this->d['list']=User::all();

        return view("users/index",$this->d);


    }


    public function new()
    {   

        if (!_can("admins")) {
            return _error_code(403);
        }
        if (is_post()) {
            $v = validate([
                tr("Card ID")=>["log_name","required|unique:users,log_name"],
                tr("Name")=>["name","required"],
                tr("Phone")=>["phone","required|unique:users,phone"],
                tr("Email")=>["email","required|email|unique:users,email"],
 
                tr("Role")=>["role_id","required|in_table:roles,id"],
                 tr("Password")=>["password","required"],
            ]);


            if ($v->passes()) {
                $user = User::create($_POST);

                if ($user) {

                    $user->password = password_hash(input("password"), PASSWORD_DEFAULT);
                    
                    $user->save();

                    Log::new("User created [id:$user->id]");

                    hooks()->do("after_user_created",$user->id);

                    display()->success(tr("User created successfully."));
                    return reload();
                }
            }

            return _response([
                "success"=>false,
                "message"=>$v->errors()->all()
            ]);

        }
        
        $data=[];

        $this->d['page']['title']="New User";
        $this->d['nav']=[
            "active"=>"Admins",
            "breadcrumb"=>[

                tr("users")=>base_url("users"),
                tr("New User")=>"",

            ]
        ];

        $this->d['roles'] = DB::table("roles")->get();


     

        return view("users/new",$this->d);


    }

    public function view($id)
    {   
        if (!_can("admins")) {
            return _error_code(403);
        }

        if (is_post()) {
            $v = validate([
                tr("Card Id")=>["log_name","required|unique:users,log_name,$id"],
                tr("Name")=>["name","required"],
                tr("Phone")=>["phone","required|unique:users,phone,$id"],
                tr("Email")=>["email","required|email|unique:users,email,$id"],
                
                tr("Role")=>["role_id","required|in_table:roles,id"],
            ]);


            if ($v->passes()) {
                $user = User::find($id)->update($_POST);

                if ($user) {
                    $user= User::find($id);

                    $user->save();

                    Log::new("User updated [id:$user->id]");

                    // hooks()->do("after_user_updated",$user->id);

                    return display()->ajax()->success(tr("User updated successfully."));
                    // return reload();
                }
            }

            return _response([
                "success"=>false,
                "message"=>$v->errors()->all()
            ]);
        }
        
        $data=[];

        $user = User::find($id);
        

        $this->d['page']['title']="User";
        $this->d['nav']=[
            "active"=>"Admins",
            "breadcrumb"=>[
                tr("users")=>base_url("users"),
                $user->name=>"",
            ]
        ];

        $this->d['data'] = $user;
        $this->d['roles'] = DB::table("roles")->get();
    


   

        return view("users/view",$this->d);


    }

    private function password($id){

        die();
        if (!_can("admins")) {
            return _error_code(403);
        }

        $v = validate([
            
            tr("Password")=>["password","required"],
            tr("Confirm Password")=>["cpassword","required|same:password"],
        ]);

        if ($v->passes()) {
            $user = User::find($id);

            $user->password = password_hash(input("password"), PASSWORD_DEFAULT);
            $user->save();

            return display()->ajax()->success(tr("User password updated successfully."));

            
        }

        return _response([
            "success"=>false,
            "message"=>$v->errors()->all()
        ]);
    }

    public function reset_password($id){

        if (!_can("admins")) {
            return _error_code(403);
        }



        $user = User::find($id);

        if (!$user) {
            return _error_code(404);
        }


        $token = bin2hex(random_bytes(50));

        $user->reset_password_token = $token;

        $resetLink = base_url() . 'auth/reset_password?token=' . $token;



        $notify = new Notify();

        $notify->email([$user->email,$user->name]);
        $notify->phone($user->phone);

        $template = template(get_option("email_reset_password"));

        $template->set($user);

        $template->reset_url = $resetLink;
        $template->email_url = $resetLink;
        $template->email_url_name = tr("Reset password");

        $notify->email_url = $resetLink;
        $notify->email_url_name = tr("Reset password");

        $user->reset_token_expiry = Carbon::now()->addDays(1)->toDateTimeString();
        $user->save();

        $notify->text($template->render());
        // $notify->send_email();
        $notify->send_sms();

        Log::new("Password reset request [id:$user->id]");

        display()->success(tr("Reset password request sent successfully"));

        return redirect()->to(base_url("users/view/".$user->id));
        



    }

    public function delete($id){

        if (!_can("admins")) {
            return _error_code(403);
        }

        $user = User::find($id);

        if ($user) {

            $user->save();
            $user->delete();
            
            hooks()->do("after_user_deleted",$user->id);

            Log::new("User delete [id:$user->id]");

            display()->success(tr("User deleted successfully."));

            return redirect()->to(base_url("users"));
        }
    }



    public function profile(){

   

        $user = User::find(auth()->id);

        if (!$user) {
            return _error_code(404);
        }


        $this->d['nav']['breadcrumb']=[
            tr("Dashboard")=>base_url(),
            tr("Profile")=>base_url(),
        ];

        $this->d['user'] = $user;




        return view("users/profile",$this->d);
    }


    public function setup_2fa()
    {
        $user = User::find(auth()->id);

        if (!$user) {
            return _error_code(404);
        }

        // Check if this is a request for JSON data
        if (input('format') === 'json') {
            $tfa = new TwoFactorAuth(new QRServerProvider(), 'GSC_Careers');
            $secret = $tfa->createSecret();
            session()->set('temp_2fa_secret', $secret);
            $qrCodeUrl = $tfa->getQRCodeImageAsDataUri($user->email, $secret);
            
            return _response([
                "success"=>true,
                "data"=>[
                    "secret"=>$secret,
                    "qrCodeUrl"=>$qrCodeUrl,
                ],
            ]);
        }

        // Return HTML directly
        $tfa = new TwoFactorAuth(new QRServerProvider(), 'GSC_Careers');
        $secret = $tfa->createSecret();
        session()->set('temp_2fa_secret', $secret);
        $qrCodeUrl = $tfa->getQRCodeImageAsDataUri($user->email, $secret);

        $viewData = [
            'secret' => $secret,
            'qrCodeUrl' => $qrCodeUrl,
        ];

        return view('users/setup_2fa', $viewData);
    }

    public function disable_2fa()
    {
        $user = User::find(auth()->id);

        if (!$user) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        $user->two_factor_secret = null;
        $user->two_factor_enabled = false;
        $user->save();

        Log::new("Disable 2fa [id:$user->id]");

        return redirect()->to(base_url("users/profile"));
    }


    public function send_reset_password(){



        $user = User::find(auth()->id);

        if (!$user) {
            return _error_code(404);
        }


        $token = bin2hex(random_bytes(50));

        $user->reset_password_token = $token;

        $resetLink = base_url() . 'auth/reset_password?token=' . $token;



        $notify = new Notify();

        $notify->email([$user->email,$user->name]);
        $notify->phone($user->phone);

        $template = template(get_option("email_reset_password"));

        $template->set($user);

        $template->reset_url = $resetLink;
        $template->email_url = $resetLink;
        $template->email_url_name = tr("Reset password");

        $notify->email_url = $resetLink;
        $notify->email_url_name = tr("Reset password");

        $user->reset_token_expiry = Carbon::now()->addDays(1)->toDateTimeString();
        $user->save();

        $notify->text($template->render());
        $notify->send_email();
        $notify->send_sms();

        Log::new("Reset password request [id:$user->id]");



        display()->success(tr("Reset password request sent successfully"));

        return redirect()->to(base_url("users/profile/"));
        



    }


    public function confirm_2fa()
    {
        $user = User::find(auth()->id);

        if (!$user) {
            return _error_code(404);
        }

        $v = validate([

            tr("2fa_code")=>['2fa_code','required']
        ]);

        if (!$v->passes()) {

            return _response([
                "success"=>false,
                "message"=>$v->errors()->all(),
                "action"=>''

            ]);
        }

        $tfa = new TwoFactorAuth(new QRServerProvider(), 'GSC_Careers');

        $secret = session()->get('temp_2fa_secret');

        if (!$secret) {
            // return redirect()->to('auth/setup_2fa')->with('error', 'Secret key not found, please try again.');

            return _response([
                "success"=>false,
                "message"=>[tr('Secret key not found, please try again.')],
                "action"=>''

            ]);
        }

        $code = input('2fa_code');

        if ($tfa->verifyCode($secret, $code)) {
            $user->two_factor_secret = $secret;
            $user->two_factor_enabled = true;
            $user->save();

            Log::new("2FA Enabled [id:$user->id]");

            session()->remove('temp_2fa_secret');


            return _response([
                "success"=>false,
                "message"=>[tr('Two-factor authentication has been enabled.')],
                "action"=>'reload'

            ]);

            return redirect()->to('auth/profile')->with('success', 'Two-factor authentication has been enabled.');
        } else {

            return _response([
                "success"=>false,
                "message"=>[tr('Invalid code, please try again.')],
                "action"=>''

            ]);

            return redirect()->to('auth/setup_2fa')->with('error', 'Invalid code, please try again.');
        }
    }

    
}