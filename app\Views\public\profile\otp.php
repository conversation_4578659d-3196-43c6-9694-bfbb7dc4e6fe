<?= $this->extend('layouts/auth') ?>

<?= $this->section('content') ?>
<div class="content d-flex justify-content-center align-items-center px-1">
    <form class="login-form wmin-sm-400 ajax" method="post" autocomplete="off" enctype="multipart/form-data">
        <?php display()->messages() ?>
        <div class="message"></div>
        <div class="card mb-0">
            <div class="card-body text-dark">
                <div class="tab-pane fade show active" id="otp-tab">
                    <div class="text-center pt-5 pb-3">
                        <i class="far fa-sms fa-4x text-primary"></i>
                    </div>

                    <div class="text-center mb-4">
                        <h5><?= tr("Enter Verification Code") ?></h5>
                        <p class="text-muted">
                            <?= tr("We have sent a verification code to") ?><br>
                            <strong dir="ltr">+968 <?= esc($phone) ?></strong>
                        </p>
                    </div>

                
                    <div class="form-group">
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text">
                                    <i class="fas fa-key"></i>
                                </span>
                            </div>
                            <input type="text" 
                                   class="form-control text-center" 
                                   name="otp" 
                                   dir="ltr"
                                   placeholder="<?= tr("Enter 4-digit code") ?>" 
                                   maxlength="4"
                                   pattern="[0-9]{4}"
                                   autocomplete="one-time-code">
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" 
                                name="verify_otp" 
                                class="btn btn-primary btn-large py-2 btn-block" 
                                id="verify-btn">
                            <?= tr("Verify Code") ?>
                        </button>
                    </div>

                    <div class="text-center">
                        <div class="mb-3">
                            <span class="text-muted"><?= tr("Did not receive the code?") ?></span>
                        </div>
                        
                        <div class="row">
                            <div class="col-6">
                                <button type="button" 
                                        class="btn btn-link btn-sm btn-block ajax-btn" 
                                        onclick="resendOtp()" 
                                        id="resend-btn">
                                    <i class="fas fa-redo-alt"></i> <?= tr("Resend") ?>
                                </button>
                                <small id="resend-timer" class="text-muted d-none">
                                    <?= tr("Resend in") ?> <span id="countdown">40</span>s
                                </small>
                            </div>
                            <div class="col-6">
                                <a href="<?= base_url('profile/login') ?>" 
                                   class="btn btn-link btn-sm btn-block">
                                     <?= tr("Change Number") ?> <i class="fas fa-arrow-left"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </form>
</div>

<script>
let countdownInterval;
let countdownTime = 0;

function resendOtp() {
    $('#resend-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> <?= tr("Sending...") ?>');
    
    $.ajax({
        url: '<?= base_url("profile/resend_otp") ?>',
        method: 'POST',
        data: {
            csrf_test_name: $('input[name="csrf_test_name"]').val()
        },
        success: function(response) {
            if (response.success) {
                showMessage(response.message, 'success');
                
                if (response.data && response.data.delay) {
                    startCountdown(response.data.delay);

                    $('#resend-btn').prop('disabled', true).html('<i class="fas fa-redo-alt"></i> <?= tr("Resend") ?>');
                } else {
                    $('#resend-btn').prop('disabled', false).html('<i class="fas fa-redo-alt"></i> <?= tr("Resend") ?>');
                }
            } else {
                showMessage(response.message, 'error');
                
                $('#resend-btn').prop('disabled', false).html('<i class="fas fa-redo-alt"></i> <?= tr("Resend") ?>');
                
                if (response.action && response.action.includes('location.href')) {
                    eval(response.action);
                }
            }
        },
        error: function() {
            showMessage(['<?= tr("An error occurred. Please try again.") ?>'], 'error');
            $('#resend-btn').prop('disabled', false).html('<i class="fas fa-redo-alt"></i> <?= tr("Resend") ?>');
        }
    });
}

function startCountdown(seconds) {
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }
    
    countdownTime = seconds;
    
    if (countdownTime > 0) {
        $('#resend-btn').prop('disabled', true);
        $('#resend-timer').removeClass('d-none');
        
        countdownInterval = setInterval(function() {
            $('#countdown').text(countdownTime);
            countdownTime--;
            
            if (countdownTime < 0) {
                clearInterval(countdownInterval);
                countdownInterval = null;
                $('#resend-btn').prop('disabled', false).html('<i class="fas fa-redo-alt"></i> <?= tr("Resend") ?>');
                $('#resend-timer').addClass('d-none');
            }
        }, 1000);
    } else {
        $('#resend-btn').prop('disabled', false).html('<i class="fas fa-redo-alt"></i> <?= tr("Resend") ?>');
        $('#resend-timer').addClass('d-none');
    }
}

function showMessage(messages, type) {
    let alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    let messageHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">';
    
    if (Array.isArray(messages)) {
        messages.forEach(function(message) {
            messageHtml += '<div>' + message + '</div>';
        });
    } else {
        messageHtml += '<div>' + messages + '</div>';
    }
    
    messageHtml += '<button type="button" class="close" data-dismiss="alert" aria-label="Close">';
    messageHtml += '<span aria-hidden="true">&times;</span>';
    messageHtml += '</button></div>';
    
    $('.message').html(messageHtml);
}

$(document).ready(function() {
    $('input[name="otp"]').focus();
    
    $('input[name="otp"]').on('input', function() {
        if (this.value.length === 4) {
            setTimeout(() => {
                $('#verify-btn').click();
            }, 200);
        }
    });
    
    $('input[name="otp"]').on('keypress', function(e) {
        if (!/[0-9]/.test(String.fromCharCode(e.which))) {
            e.preventDefault();
        }
    });
});
</script>

<?= $this->endSection() ?> 