<?= $this->extend('layouts/auth') ?>
<?= $this->section('content') ?>
<div class="content d-flex justify-content-center align-items-center px-1 ">

    <form class="login-form wmin-sm-400 ajax"  method="post" autocomplete="off" enctype="multipart/form-data">
        <?php display()->messages() ?>
        <div class="message"></div>
        <div class="card mb-0   shadow " >
            
            <div class=" card-body text-dark">
                <div class="tab-pane fade show active" id="login-tab1">
                    <div class="text-center mb-3 py-5">
                        <i class="far fa-lock-alt fa-4x text-primary"></i>
 
                    </div>
                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link <?= (url()->get() === base_url("auth/otp_login")) ? 'active' : '' ?>" href="<?= base_url("auth/otp_login") ?>"><?= tr("OTP Login") ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= (url()->get() === base_url("auth/login")) ? 'active' : '' ?>" href="<?= base_url("auth/login") ?>"><?= tr("Credentials") ?></a>
                        </li>
                    </ul>
                    <div class="form-group form-group-feedback form-group-feedback-left">
                        <input type="text" class="form-control" name="username" value="<?= old('username') ?>" placeholder="Username">
                        <div class="form-control-feedback">
                            <i class="icon-user text-muted"></i>
                        </div>
                    </div>
                    <div class="form-group form-group-feedback form-group-feedback-left">
                        <input type="password" class="form-control" placeholder="Password" name="password">
                        <div class="form-control-feedback">
                            <i class="icon-lock2 text-muted"></i>
                        </div>
                    </div>
                    
                    
                    
                    <?php if (get_option('recaptcha_active')): ?>
                    <p>
                        <div class="g-recaptcha form-field" style="width: 100%" data-sitekey="<?= get_option('recaptcha_site') ?>"></div>
                        
                    </p>
                    <br>
                    <?php endif ?>
                    
                    <div class="form-group">
                        <button type="submit" name="signin" class="btn btn-primary btn-large py-2  btn-block"><?= tr("Sign In") ?></button>
                    </div>
                    
                    
                    
                </div>
                
            </div>
        </div>
    </form>
</div>

<?php if (get_option('recaptcha_active')): ?>
<script type="text/javascript" src="https://www.google.com/recaptcha/api.js?hl=<?= current_lang() ?>"></script>
<?php endif ?>


<form class="ajax" method="post" action="<?= base_url("auth/verify_2fa") ?>">
    <div class="modal fade" id="verify_2fa-modal" tabindex="-1" role="dialog" aria-labelledby="verify_2fa-modalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="verify_2fa-modalLabel"><?= tr("Enter 2FA code") ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="input-group">
                        <input type="text" class="form-control text-center" name="2fa_code" placeholder="<?= tr("Enter 2FA code") ?>">
                        
                    </div>
                    <br>
                    
                    
                    
                </div>
                <div class="modal-footer">
                    <button type="submit"  class="btn btn-primary btn-block py-2"><?= tr("Submit") ?></button>
                </div>
            </div>
        </div>
    </div>
</form>
<?= $this->endSection() ?>