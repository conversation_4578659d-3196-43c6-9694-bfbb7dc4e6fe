<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<div class=" d-flex">

    <h3><?= $data->name ?></h3>


</div>
<div class="card">

    <div class="card-body">
        <form method="post" class="ajax" action="<?= base_url("roles/update/".$data->id) ?>">
            <label for=""><?= tr("Role name") ?>*</label>
            <input type="text" class="form-control" name="name" value="<?= $data->name ?>"><br>
            <label for=""><?= tr("Description") ?></label>
            <textarea class="form-control" name="description"><?= $data->description ?></textarea>
            <br>
            <label for=""><?= tr("Role rights") ?></label>
            <?php foreach ($rights as $name => $value): ?>
            <div class="form-check">
                <input <?= in_array($value, ($data->roles)??[] )?'checked':'' ?> class="form-check-input"
                    type="checkbox" name="roles[]" value="<?= $value ?>" id="cehckbox-<?= $value ?>">
                <label class="form-check-label" for="cehckbox-<?= $value ?>">
                    <?= tr($name)?>
                </label>
            </div>
            <?php endforeach ?>
            <br>
            <button class="btn btn-primary"><i class="fas fa-save"></i> <?= tr("Save") ?></button>
        </form>


    </div>

</div>
<?= $this->endSection() ?>