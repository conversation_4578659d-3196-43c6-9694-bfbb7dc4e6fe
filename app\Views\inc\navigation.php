<?php if (auth()): ?>


<div class=" sidebar sidebar-dark  sidebar-main  sidebar-expand-md  print-hide  shadow border-0 rounded-0 bg-transparent"  >
	<!-- Sidebar mobile toggler -->
	<div class="sidebar-mobile-toggler  text-center bg-transparent border-0 rounded-0" >
		<a href="#" class="sidebar-mobile-main-toggle">
			<i class="fas fa-times"></i>
		</a>
		
		<a href="#" class="sidebar-mobile-expand">
			<i class="icon-screen-full"></i>
			<i class="icon-screen-normal"></i>
		</a>
	</div>
	<!-- /sidebar mobile toggler -->
	<!-- Sidebar content -->
	<div class="sidebar-content" >
		
		

		<div class="card d-flex justify-content-center pt-3 text-center shadow-0">
			
			<img src="<?= base_url("assets/images/logo.png") ?>" class="mx-auto" style="width:90%; max-width: 180px;" alt="">
			
			
		</div>

		 
		<!-- Main navigation -->
		<div class="card card-sidebar-mobile">
			<ul class="nav nav-sidebar" data-nav-type="accordion">
				
				
		
				
				
				<?php foreach (navigation()->list() as $key => $item) : ?>
				<?php if (_can($item['role'])) : ?>
				<li class="nav-item <?= count($item['links'])>1?'nav-item-submenu':'' ?> <?= (($nav['active']??'') && ($nav['active'] ?? '')==$item['name'])?'active  nav-item-expanded nav-item-open':'' ?> ">
					<?php if (count($item['links'])==1): ?>
					
					<a class="nav-link <?= (isset($nav['active']) && $nav['active']==$item['name'])?'active':'' ?>" href="<?= ($item['links'][0]['attr']) ?>">
						<?php if (strlen($item['icon'])): ?>
						<i class="<?= $item['icon'] ?>"></i>
						<?php endif ?> <span><?= tr($item['title']) ?></span>
						<?php if (isset($item['links'][0]['badge']) && $item['links'][0]['badge']>0): ?>
						<span class="badge bg-primary px-2  align-self-center ml-auto"><?= $item['links'][0]['badge'] ?></span>
						<?php endif ?>
					</a>
					
					
					<?php else: ?>
					<a href="#" class="nav-link    <?= (isset($nav['active']) && $nav['active']==$item['name'])?'active':'collapsed' ?>"  >
						<i class="<?= escap($item['icon']) ?>"></i>
						<span><?= tr($item['title']) ?></span>
						<?php if (isset($item['badge']) && $item['badge']>0): ?>
						<span class="badge bg-primary px-2  align-self-center ml-auto"><?= $item['badge'] ?></span>
						<?php endif ?>
						
					</a>
					
					<ul class="nav nav-group-sub " data-submenu-title="<?= tr($item['title']) ?>">
						<?php foreach ($item['links'] as $key => $link) : ?>
						
						<?php if (_can($link['role'])) : ?>
						<li class="nav-item  ">
							
							<a class="nav-link pl-1 " href="<?= ($link['attr']) ?>">
								<?php if (strlen($link['icon'])): ?>
								<i class="<?= escap($link['icon']) ?>"></i>
								<?php endif ?> <span><?= tr($link['title']) ?></span>
								<?php if (isset($link['badge']) && $link['badge']>0): ?>
								<span class="badge bg-primary px-2  align-self-center ml-auto"><?= $link['badge'] ?></span>
								<?php endif ?>
							</a>
							
						</li>
						<?php endif ?>
						
						
						<?php endforeach ?>
					</ul>
					
					<?php endif ?>
					
				</li>
				<?php endif ?>
				<?php endforeach ?>
				
				
				
				<!-- /layout -->
				
			</ul>
		</div>
		<!-- /main navigation -->
		
		
	</div>
	<!-- /sidebar content -->
</div>
<?php endif ?>