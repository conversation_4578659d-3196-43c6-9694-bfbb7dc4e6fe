<?= $this->extend('layouts/auth') ?>

<?= $this->section('content') ?>
     <div class="content d-flex justify-content-center align-items-center px-1 ">
         <!-- Tab Links -->
    
                <!-- Login form -->
                <form class="login-form wmin-sm-400 ajax"  method="post" autocomplete="off" enctype="multipart/form-data">
                    <?php display()->messages() ?>
                    <div class="message"></div>
                    <div class="card mb-0   shadow " >
                    

                        <div class=" card-body text-dark">

                            <div class="tab-pane fade show active" id="login-tab1">
                                <div class="text-center mb-3 py-5">
                                   <i class="far fa-lock-alt fa-4x "></i>
                                    

                                    <!-- <span class="d-block text-muted">Your credentials</span> -->
                                </div>

                                <ul class="nav nav-tabs">
                                    <li class="nav-item">
                                        <a class="nav-link <?= (url()->get() === base_url("auth/otp_login")) ? 'active' : '' ?>" href="<?= base_url("auth/otp_login") ?>"><?= tr("OTP Login") ?></a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link <?= (url()->get() === base_url("auth/login")) ? 'active' : '' ?>" href="<?= base_url("auth/login") ?>"><?= tr("Credentials") ?></a>
                                    </li>
                                </ul>

                                <div class="form-group form-group-feedback form-group-feedback-left">
                                    <input type="text" class="form-control" name="username" value="<?= old('username') ?>" placeholder="Username">
                                    <div class="form-control-feedback">
                                        <i class="icon-user text-muted"></i>
                                    </div>
                                </div>

                                <div class="form-group form-group-feedback form-group-feedback-left">
                                    <input type="password" class="form-control" placeholder="Password" name="password">
                                    <div class="form-control-feedback">
                                        <i class="icon-lock2 text-muted"></i>
                                    </div>
                                </div>

                    

                

                                
                    <?php if (env('recaptcha.active')): ?>
                    <p>
                        <div class="g-recaptcha form-field" style="width: 100%" data-sitekey="<?= env('recaptcha.site') ?>"></div>
                        
                    </p>
                    <br>
                    <?php endif ?>

                            

                                <div class="form-group">
                                    <button type="submit" name="signin" class="btn btn-primary btn-large py-2 rounded-pill btn-block"><?= tr("Sign In") ?></button>
                                </div>


              
                                



                                
                            </div>

                        
                        </div>
                    </div>
                </form>
                <!-- /login form -->

            </div>

             
                <?php if (env('recaptcha.active')): ?>
    <script type="text/javascript" src="https://www.google.com/recaptcha/api.js?hl=<?= current_lang() ?>"></script>
  <?php endif ?>
<?= $this->endSection() ?>