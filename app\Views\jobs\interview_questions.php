<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<div class="d-flex justify-content-between mb-1 mt-3">
    <h3><?= tr("Edit Job") ?> - <?= esc($job->name) ?></h3>
    <div>
        <a class="btn btn-danger after-confirm" href="<?= base_url("jobs/delete/$job->id") ?>">
                <i class="fas fa-trash"></i> <?= tr("Delete Job") ?>
        </a>
    </div>
</div>

<!-- Tab Navigation -->
<ul class="nav nav-pills mb-3" id="jobEditTabs">
    <li class="nav-item">
        <a class="nav-link" href="<?= base_url("jobs/edit/{$job->id}") ?>">
            <i class="fas fa-edit"></i> <?= tr("Job Details") ?>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link active" href="<?= base_url("jobs/edit/{$job->id}/interview-questions") ?>">
            <i class="fas fa-question-circle"></i> <?= tr("Interview Questions") ?>
        </a>
    </li>
</ul>

<div class="card shadow border-0">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="text-primary mb-0"><?= tr("Interview Questions") ?></h5>
        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addQuestionModal">
            <i class="fas fa-plus"></i> <?= tr("Add Question") ?>
        </button>
    </div>
    <div class="">
        <div class="table-responsive">
            <table class="table table-hover" id="questionsTable">
                <thead class="bg-primary text-white">
                    <tr>
                        <th width="5%">#</th>
                        <th width="50%"><?= tr("Question") ?></th>
                        <th width="35%"><?= tr("Sample Answer") ?></th>
                        <th width="10%"><?= tr("Actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (count($questions)==0): ?>
                        <tr>
                            <td colspan="4" class="text-center text-muted">
                                <i class="fas fa-question-circle fa-3x mb-3"></i>
                                <p><?= tr("No interview questions added yet") ?></p>
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addQuestionModal">
                                    <i class="fas fa-plus"></i> <?= tr("Add First Question") ?>
                                </button>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($questions as $index => $question): ?>
                            <tr>
                                <td><?= $index + 1 ?></td>
                                <td>
                                    <div class="question-text">
                                        <?= esc($question->question) ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="answer-text">
                                        <?php if (!empty($question->answer)): ?>
                                            <small class="text-muted"><?= nl2br(esc($question->answer)) ?></small>
                                        <?php else: ?>
                                            <small class="text-muted"><?= tr("No sample answer provided") ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="">
                                        <button type="button" class="btn btn-primary edit-question" 
                                                data-id="<?= $question->id ?>"
                                                data-question="<?= esc($question->question) ?>"
                                                data-answer="<?= esc($question->answer) ?>"
                                                data-toggle="modal" 
                                                data-target="#editQuestionModal">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger delete-question" 
                                                data-id="<?= $question->id ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Question Modal -->
<div class="modal fade" id="addQuestionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form class="ajax" method="post" action="<?= base_url("jobs/interview-questions/create/{$job->id}") ?>">
           
                <div class="modal-header">
                    <h5 class="modal-title text-primary">
                        <i class="fas fa-plus"></i> <?= tr("Add Interview Question") ?>
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="question" class="required"><?= tr("Question") ?></label>
                        <input type="text" class="form-control" id="question" name="question" 
                                  placeholder="<?= tr("Enter the interview question...") ?>">
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="form-group">
                        <label for="answer"><?= tr("Answer") ?> <small class="text-muted">(<?= tr("Optional") ?>)</small></label>
                        <textarea class="form-control" id="answer" name="answer" rows="4" 
                                  placeholder="<?= tr("Enter a sample answer or key points to look for...") ?>"></textarea>
                        <small class="form-text text-muted"><?= tr("This will help interviewers evaluate candidate responses") ?></small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> <?= tr("Cancel") ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> <?= tr("Save") ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Question Modal -->
<div class="modal fade" id="editQuestionModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <form class="ajax" method="post" action="<?= base_url("jobs/interview-questions/update/{$job->id}") ?>">
               
                <input type="hidden" name="question_id" id="edit_question_id">
                <div class="modal-header">
                    <h5 class="modal-title text-primary">
                        <i class="fas fa-edit"></i> <?= tr("Edit Interview Question") ?>
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="edit_question" class="required"><?= tr("Question") ?></label>
                        <input type="text" class="form-control" id="edit_question" name="question"  >
                        <div class="invalid-feedback"></div>
                    </div>
                    <div class="form-group">
                        <label for="edit_answer"><?= tr("Sample Answer") ?> <small class="text-muted">(<?= tr("Optional") ?>)</small></label>
                        <textarea class="form-control" id="edit_answer" name="answer" rows="4"></textarea>
                        <small class="form-text text-muted"><?= tr("This will help interviewers evaluate candidate responses") ?></small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> <?= tr("Cancel") ?>
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> <?= tr("Save") ?>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>



<?= $this->endSection() ?>

<?= $this->section("script") ?>
<script type="text/javascript">
    $(document).ready(function() {
        // Edit question modal
        $('.edit-question').on('click', function() {
            const questionId = $(this).data('id');
            const question = $(this).data('question');
            const answer = $(this).data('answer');
            
            $('#edit_question_id').val(questionId);
            $('#edit_question').val(question);
            $('#edit_answer').val(answer);
        });

        // Delete question
        $('.delete-question').on('click', function() {
            const questionId = $(this).data('id');
            $('#delete_question_id').val(questionId);
            $('#deleteQuestionModal').modal('show');
        });

        

        // Clear form when modal is closed
        $('.modal').on('hidden.bs.modal', function() {
            $(this).find('form')[0].reset();
            $(this).find('.is-invalid').removeClass('is-invalid');
            $(this).find('.invalid-feedback').text('');
        });
    });
</script>
<?= $this->endSection() ?> 