<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>



<div class="d-flex justify-content-between mb-3">
    <h3><?= tr("Settings") ?></h3>
</div>

<!-- Navigation Tabs -->
<ul class="nav nav-tabs" id="settingsTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <a class="nav-link active" id="app-config-tab" data-toggle="tab" href="#app-config" role="tab" aria-controls="app-config" aria-selected="true">
            <i class="fas fa-cog"></i> <?= tr("App Configuration") ?>
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link" id="sms-tab" data-toggle="tab" href="#sms" role="tab" aria-controls="sms" aria-selected="false">
            <i class="fas fa-sms"></i> <?= tr("SMS") ?>
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link" id="templates-tab" data-toggle="tab" href="#templates" role="tab" aria-controls="templates" aria-selected="false">
            <i class="fas fa-envelope"></i> <?= tr("Templates") ?>
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link" id="recaptcha-tab" data-toggle="tab" href="#recaptcha" role="tab" aria-controls="recaptcha" aria-selected="false">
            <i class="fas fa-shield-alt"></i> <?= tr("reCAPTCHA") ?>
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link" id="storage-tab" data-toggle="tab" href="#storage" role="tab" aria-controls="storage" aria-selected="false">
            <i class="fas fa-hdd"></i> <?= tr("Storage") ?>
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link" id="colors-tab" data-toggle="tab" href="#colors" role="tab" aria-controls="colors" aria-selected="false">
            <i class="fas fa-palette"></i> <?= tr("Colors") ?>
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link" id="system-status-tab" data-toggle="tab" href="#system-status" role="tab" aria-controls="system-status" aria-selected="false">
            <i class="fas fa-server"></i> <?= tr("System Status") ?>
        </a>
    </li>
</ul>

<form class="ajax" method="post">
    <?= csrf_field() ?>
    
    <!-- Tab Content -->
    <div class="tab-content" id="settingsTabContent">
        
        <!-- App Configuration Tab -->
        <div class="tab-pane fade show active" id="app-config" role="tabpanel" aria-labelledby="app-config-tab">
            <div class="card shadow border-0 mt-3">
                <div class="card-header">
                    <h5 class="text-primary mb-0"><?= tr("Application Configuration") ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label required><?= tr("Application Name") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                    </div>
                                    <input type="text" class="form-control" name="app_name" value="<?= get_option("app_name") ?: env('app.name') ?>" required>
                                </div>
                                <small class="form-text text-muted"><?= tr("The name of your application") ?></small>
                                <?= validation_show_error('app_name') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label required><?= tr("Application Status") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-toggle-on"></i></span>
                                    </div>
                                    <select name="application_status" class="form-control" required>
                                        <option value="active" <?= get_option("application_status")=="active"?'selected':'' ?>><?= tr("Active") ?></option>
                                        <option value="inactive" <?= get_option("application_status")=="inactive"?'selected':'' ?>><?= tr("Inactive") ?></option>
                                    </select>
                                </div>
                                <small class="form-text text-muted"><?= tr("Controls whether the application accepts new submissions") ?></small>
                                <?= validation_show_error('application_status') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label required><?= tr("Public Status") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-eye"></i></span>
                                    </div>
                                    <select name="public_status" class="form-control" required>
                                        <option value="active" <?= get_option("public_status")=="active"?'selected':'' ?>><?= tr("Active") ?></option>
                                        <option value="inactive" <?= get_option("public_status")=="inactive"?'selected':'' ?>><?= tr("Inactive") ?></option>
                                    </select>
                                </div>
                                <small class="form-text text-muted"><?= tr("Controls public visibility of the application") ?></small>
                                <?= validation_show_error('public_status') ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SMS Configuration Tab -->
        <div class="tab-pane fade" id="sms" role="tabpanel" aria-labelledby="sms-tab">
            <div class="card shadow border-0 mt-3">
                <div class="card-header">
                    <h5 class="text-primary mb-0"><?= tr("SMS Configuration") ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?= tr("SMS User ID") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    </div>
                                    <input type="text" class="form-control" name="sms_user_id" value="<?= get_option("sms_user_id") ?>">
                                </div>
                                <small class="form-text text-muted"><?= tr("SMS service user ID") ?></small>
                                <?= validation_show_error('sms_user_id') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?= tr("SMS Password") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-key"></i></span>
                                    </div>
                                    <input type="password" class="form-control" name="sms_password" value="<?= get_option("sms_password") ?>">
                                </div>
                                <small class="form-text text-muted"><?= tr("SMS service password") ?></small>
                                <?= validation_show_error('sms_password') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-12">
                            <div class="form-group">
                                <label><?= tr("OTP SMS Template") ?></label>
                                <textarea rows="4" class="form-control" name="sms_otp" placeholder="<?= tr("Enter OTP SMS template...") ?>"><?= get_option("sms_otp") ?></textarea>
                                <small class="form-text text-muted"><?= tr("Available variables: {otp}") ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Templates Tab -->
        <div class="tab-pane fade" id="templates" role="tabpanel" aria-labelledby="templates-tab">
            <div class="card shadow border-0 mt-3">
                <div class="card-header">
                    <h5 class="text-primary mb-0"><?= tr("SMS Templates") ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?= tr("Reset Password SMS") ?></label>
                                <textarea rows="4" class="form-control" name="email_reset_password" placeholder="<?= tr("Enter reset password SMS template...") ?>"><?= get_option("email_reset_password") ?></textarea>
                                <small class="form-text text-muted"><?= tr("SMS sent when password is reset") ?></small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?= tr("Password Updated SMS") ?></label>
                                <textarea rows="4" class="form-control" name="email_update_password" placeholder="<?= tr("Enter password updated SMS template...") ?>"><?= get_option("email_update_password") ?></textarea>
                                <small class="form-text text-muted"><?= tr("SMS sent when password is updated") ?></small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?= tr("Login Attempt SMS") ?></label>
                                <textarea rows="4" class="form-control" name="login_attemp_sms" placeholder="<?= tr("Enter login attempt SMS template...") ?>"><?= get_option("login_attemp_sms") ?></textarea>
                                <small class="form-text text-muted"><?= tr("SMS sent for suspicious login attempts") ?></small>
                            </div>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    <h6 class="text-primary"><?= tr("Application Status SMS Templates") ?></h6>
                    <p class="text-muted"><?= tr("Available variables: {name}, {card_id}, {job_name}") ?></p>
                    
                    <div class="row">
                        <?php foreach ($application_statuses as $key => $status): ?>
                            <div class="col-md-6 mb-3">
                                <div class="form-group">
                                    <label><?= esc($status) ?> (<?= tr("SMS") ?>)</label>
                                    <textarea rows="4" class="form-control" name="sms_<?= $key ?>" placeholder="<?= tr("Enter SMS template for") ?> <?= esc($status) ?>..."><?= get_option("sms_".$key) ?></textarea>
                                </div>
                            </div>
                        <?php endforeach ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- reCAPTCHA Tab -->
        <div class="tab-pane fade" id="recaptcha" role="tabpanel" aria-labelledby="recaptcha-tab">
            <div class="card shadow border-0 mt-3">
                <div class="card-header">
                    <h5 class="text-primary mb-0"><?= tr("reCAPTCHA Configuration") ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label required><?= tr("reCAPTCHA Status") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-shield-alt"></i></span>
                                    </div>
                                    <select name="recaptcha_active" class="form-control" required>
                                        <option value="1" <?= get_option("recaptcha_active")=="1"?'selected':'' ?>><?= tr("Active") ?></option>
                                        <option value="0" <?= get_option("recaptcha_active")=="0"?'selected':'' ?>><?= tr("Inactive") ?></option>
                                    </select>
                                </div>
                                <small class="form-text text-muted"><?= tr("Enable or disable reCAPTCHA protection") ?></small>
                                <?= validation_show_error('recaptcha_active') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?= tr("reCAPTCHA Site Key") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-key"></i></span>
                                    </div>
                                    <input type="text" class="form-control" name="recaptcha_site" value="<?= get_option("recaptcha_site") ?>" placeholder="<?= tr("Enter reCAPTCHA site key") ?>">
                                </div>
                                <small class="form-text text-muted"><?= tr("Public site key from Google reCAPTCHA") ?></small>
                                <?= validation_show_error('recaptcha_site') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?= tr("reCAPTCHA Secret Key") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    </div>
                                    <input type="password" class="form-control" name="recaptcha_secret" value="<?= get_option("recaptcha_secret") ?>" placeholder="<?= tr("Enter reCAPTCHA secret key") ?>">
                                </div>
                                <small class="form-text text-muted"><?= tr("Secret key from Google reCAPTCHA") ?></small>
                                <?= validation_show_error('recaptcha_secret') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong><?= tr("How to get reCAPTCHA keys:") ?></strong><br>
                                1. <?= tr("Visit") ?> <a href="https://www.google.com/recaptcha/admin" target="_blank">Google reCAPTCHA Admin</a><br>
                                2. <?= tr("Register your domain") ?><br>
                                3. <?= tr("Copy the Site Key and Secret Key") ?><br>
                                4. <?= tr("Paste them in the fields above") ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Storage Configuration Tab -->
        <div class="tab-pane fade" id="storage" role="tabpanel" aria-labelledby="storage-tab">
            <div class="card shadow border-0 mt-3">
                <div class="card-header">
                    <h5 class="text-primary mb-0"><?= tr("Storage Configuration") ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Storage Method Selection -->
                        <div class="col-md-12">
                            <div class="form-group">
                                <label required><?= tr("Storage Method") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-toggle-on"></i></span>
                                    </div>
                                    <select name="storage_use_ftp" class="form-control" required id="storage_use_ftp">
                                        <option value="0" <?= ($storage_config['storage_use_ftp'] ?? '0') == '0' ? 'selected' : '' ?>><?= tr("Local Storage") ?></option>
                                        <option value="1" <?= ($storage_config['storage_use_ftp'] ?? '0') == '1' ? 'selected' : '' ?>><?= tr("FTP Storage") ?></option>
                                    </select>
                                </div>
                                <small class="form-text text-muted"><?= tr("Choose between local file storage or remote FTP storage") ?></small>
                                <?= validation_show_error('storage_use_ftp') ?>
                            </div>
                        </div>

                        <!-- Local Storage Configuration -->
                        <div class="col-md-12">
                            <h6 class="text-primary border-bottom pb-2"><?= tr("Local Storage Settings") ?></h6>
                        </div>
                        
                        <div class="col-md-12">
                            <div class="form-group">
                                <label required><?= tr("Storage Path") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-folder"></i></span>
                                    </div>
                                    <input type="text" class="form-control" name="storage_path" value="<?= $storage_config['storage_path'] ?? '' ?>" required>
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            <i class="fas fa-<?= ($storage_config['path_status']['class'] ?? 'secondary') == 'success' ? 'check text-success' : 'times text-danger' ?>"></i>
                                        </span>
                                    </div>
                                </div>
                                <small class="form-text text-muted">
                                    <?= tr("Absolute path to the directory where files will be stored") ?>
                                    <?php if (isset($storage_config['path_status'])): ?>
                                        - <strong class="text-<?= $storage_config['path_status']['class'] ?>"><?= $storage_config['path_status']['status'] ?></strong>
                                    <?php endif ?>
                                </small>
                                <?= validation_show_error('storage_path') ?>
                            </div>
                        </div>

                        <!-- FTP Storage Configuration -->
                        <div class="col-md-12 mt-3">
                            <h6 class="text-primary border-bottom pb-2"><?= tr("FTP Storage Settings") ?></h6>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?= tr("FTP Server") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-server"></i></span>
                                    </div>
                                    <input type="text" class="form-control" name="storage_ftp_server" value="<?= $storage_config['storage_ftp_server'] ?? '' ?>" placeholder="<?= tr("FTP server hostname or IP") ?>">
                                </div>
                                <small class="form-text text-muted"><?= tr("FTP server hostname or IP address") ?></small>
                                <?= validation_show_error('storage_ftp_server') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?= tr("FTP Username") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    </div>
                                    <input type="text" class="form-control" name="storage_ftp_username" value="<?= $storage_config['storage_ftp_username'] ?? '' ?>" placeholder="<?= tr("FTP username") ?>">
                                </div>
                                <small class="form-text text-muted"><?= tr("FTP authentication username") ?></small>
                                <?= validation_show_error('storage_ftp_username') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?= tr("FTP Password") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-key"></i></span>
                                    </div>
                                    <input type="password" class="form-control" name="storage_ftp_password" value="<?= $storage_config['storage_ftp_password'] ?? '' ?>" placeholder="<?= tr("FTP password") ?>">
                                </div>
                                <small class="form-text text-muted"><?= tr("FTP authentication password") ?></small>
                                <?= validation_show_error('storage_ftp_password') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label><?= tr("FTP Connection Status") ?></label>
                                <div class="alert alert-<?= $storage_config['ftp_status']['class'] ?? 'secondary' ?> py-2 mb-0">
                                    <i class="fas fa-network-wired"></i> <?= $storage_config['ftp_status']['status'] ?? tr('Unknown') ?>
                                </div>
                                <small class="form-text text-muted"><?= tr("Real-time FTP connection status") ?></small>
                            </div>
                        </div>

                        <!-- File Configuration -->
                        <div class="col-md-12 mt-3">
                            <h6 class="text-primary border-bottom pb-2"><?= tr("File Settings") ?></h6>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label required><?= tr("Allowed Extensions") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-file-alt"></i></span>
                                    </div>
                                    <input type="text" class="form-control" name="storage_extensions" value="<?= $storage_config['storage_extensions'] ?? '' ?>" required placeholder="pdf,jpeg,png,jpg">
                                </div>
                                <small class="form-text text-muted"><?= tr("Comma-separated list of allowed file extensions (without dots)") ?></small>
                                <?= validation_show_error('storage_extensions') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                <label required><?= tr("Maximum File Size") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-weight"></i></span>
                                    </div>
                                    <input type="text" class="form-control" name="storage_max_size" value="<?= $storage_config['storage_max_size'] ?? '' ?>" required placeholder="2M">
                                    <div class="input-group-append">
                                        <span class="input-group-text"><?= tr("MB") ?></span>
                                    </div>
                                </div>
                                <small class="form-text text-muted"><?= tr("Maximum file size (e.g., 2M, 10MB). Server limit: ") ?><?= ini_get('upload_max_filesize') ?></small>
                                <?= validation_show_error('storage_max_size') ?>
                            </div>
                        </div>

                        <!-- Storage Information -->
                        <div class="col-md-12 mt-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong><?= tr("Storage Configuration Notes:") ?></strong><br>
                                • <?= tr("Local Storage: Files are stored on the web server") ?><br>
                                • <?= tr("FTP Storage: Files are uploaded to a remote FTP server") ?><br>
                                • <?= tr("Changes take effect immediately upon saving") ?><br>
                                • <?= tr("Ensure proper permissions are set for the storage directory") ?><br>
                                • <?= tr("Test FTP connections before switching to FTP storage") ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Colors Configuration Tab -->
        <div class="tab-pane fade" id="colors" role="tabpanel" aria-labelledby="colors-tab">
            <div class="card shadow border-0 mt-3">
                <div class="card-header">
                    <h5 class="text-primary mb-0"><?= tr("Color Configuration") ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong><?= tr("Color Customization") ?>:</strong><br>
                                <?= tr("Customize the primary and secondary colors of your application theme. Changes will be applied immediately after saving.") ?>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group">
                                <label required><?= tr("Primary Color") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-palette"></i></span>
                                    </div>
                                    <input type="color" class="form-control" name="color_primary" value="<?= $color_config['color_primary'] ?>" required id="color_primary_input">
                                    <input type="text" class="form-control ml-2" name="color_primary_text" value="<?= $color_config['color_primary'] ?>" required pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$" placeholder="#27374D" id="color_primary_text">
                                </div>
                                <small class="form-text text-muted"><?= tr("Used for buttons, links, and primary elements") ?></small>
                                <div class="mt-2">
                                    <div class="color-preview" style="background-color: <?= $color_config['color_primary'] ?>; width: 100%; height: 30px; border-radius: 5px; border: 1px solid #ddd;" id="primary_preview"></div>
                                </div>
                                <?= validation_show_error('color_primary') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group">
                                <label required><?= tr("Secondary Color") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-palette"></i></span>
                                    </div>
                                    <input type="color" class="form-control" name="color_secondary" value="<?= $color_config['color_secondary'] ?>" required id="color_secondary_input">
                                    <input type="text" class="form-control ml-2" name="color_secondary_text" value="<?= $color_config['color_secondary'] ?>" required pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$" placeholder="#526D82" id="color_secondary_text">
                                </div>
                                <small class="form-text text-muted"><?= tr("Used for secondary buttons and accents") ?></small>
                                <div class="mt-2">
                                    <div class="color-preview" style="background-color: <?= $color_config['color_secondary'] ?>; width: 100%; height: 30px; border-radius: 5px; border: 1px solid #ddd;" id="secondary_preview"></div>
                                </div>
                                <?= validation_show_error('color_secondary') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-group">
                                <label required><?= tr("Body Background") ?></label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-fill"></i></span>
                                    </div>
                                    <input type="color" class="form-control" name="color_body" value="<?= $color_config['color_body'] ?>" required id="color_body_input">
                                    <input type="text" class="form-control ml-2" name="color_body_text" value="<?= $color_config['color_body'] ?>" required pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$" placeholder="#EEEEEE" id="color_body_text">
                                </div>
                                <small class="form-text text-muted"><?= tr("Background color for the application body") ?></small>
                                <div class="mt-2">
                                    <div class="color-preview" style="background-color: <?= $color_config['color_body'] ?>; width: 100%; height: 30px; border-radius: 5px; border: 1px solid #ddd;" id="body_preview"></div>
                                </div>
                                <?= validation_show_error('color_body') ?>
                            </div>
                        </div>
                        
                        <div class="col-md-12 mt-3">
                            <h6 class="text-primary"><?= tr("Color Presets") ?></h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-secondary btn-block" onclick="applyColorPreset('#27374D', '#526D82', '#EEEEEE')">
                                        <?= tr("Default") ?>
                                        <div class="d-flex mt-1">
                                            <div style="background: #27374D; width: 20px; height: 15px; margin-right: 2px;"></div>
                                            <div style="background: #526D82; width: 20px; height: 15px; margin-right: 2px;"></div>
                                            <div style="background: #EEEEEE; width: 20px; height: 15px; border: 1px solid #ccc;"></div>
                                        </div>
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-secondary btn-block" onclick="applyColorPreset('#007bff', '#6c757d', '#f8f9fa')">
                                        <?= tr("Blue") ?>
                                        <div class="d-flex mt-1">
                                            <div style="background: #007bff; width: 20px; height: 15px; margin-right: 2px;"></div>
                                            <div style="background: #6c757d; width: 20px; height: 15px; margin-right: 2px;"></div>
                                            <div style="background: #f8f9fa; width: 20px; height: 15px; border: 1px solid #ccc;"></div>
                                        </div>
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-secondary btn-block" onclick="applyColorPreset('#28a745', '#20c997', '#f1f3f4')">
                                        <?= tr("Green") ?>
                                        <div class="d-flex mt-1">
                                            <div style="background: #28a745; width: 20px; height: 15px; margin-right: 2px;"></div>
                                            <div style="background: #20c997; width: 20px; height: 15px; margin-right: 2px;"></div>
                                            <div style="background: #f1f3f4; width: 20px; height: 15px; border: 1px solid #ccc;"></div>
                                        </div>
                                    </button>
                                </div>
                                <div class="col-md-3">
                                    <button type="button" class="btn btn-outline-secondary btn-block" onclick="applyColorPreset('#dc3545', '#fd7e14', '#fff5f5')">
                                        <?= tr("Red") ?>
                                        <div class="d-flex mt-1">
                                            <div style="background: #dc3545; width: 20px; height: 15px; margin-right: 2px;"></div>
                                            <div style="background: #fd7e14; width: 20px; height: 15px; margin-right: 2px;"></div>
                                            <div style="background: #fff5f5; width: 20px; height: 15px; border: 1px solid #ccc;"></div>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-12 mt-4">
                            <h6 class="text-primary"><?= tr("Live Preview") ?></h6>
                            <div class="card" id="color_preview_card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><?= tr("Preview Card") ?></h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-2"><?= tr("This is how your theme colors will look:") ?></p>
                                    <button type="button" class="btn btn-primary mr-2"><?= tr("Primary Button") ?></button>
                                    <button type="button" class="btn btn-secondary mr-2"><?= tr("Secondary Button") ?></button>
                                    <span class="badge badge-primary mr-2"><?= tr("Primary Badge") ?></span>
                                    <span class="badge badge-secondary"><?= tr("Secondary Badge") ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status Tab -->
        <div class="tab-pane fade" id="system-status" role="tabpanel" aria-labelledby="system-status-tab">
            <div class="card shadow border-0 mt-3">
                <div class="card-header">
                    <h5 class="text-primary mb-0"><?= tr("System Status") ?></h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- System Information -->
                        <div class="col-md-6">
                            <h6 class="text-primary"><?= tr("System Information") ?></h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong><?= tr("PHP Version") ?>:</strong></td>
                                    <td><?= $system_status['php_version'] ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?= tr("CodeIgniter Version") ?>:</strong></td>
                                    <td><?= $system_status['codeigniter_version'] ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?= tr("Server Software") ?>:</strong></td>
                                    <td><?= $system_status['server_software'] ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?= tr("Memory Limit") ?>:</strong></td>
                                    <td><?= $system_status['memory_limit'] ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?= tr("Max Execution Time") ?>:</strong></td>
                                    <td><?= $system_status['max_execution_time'] ?> <?= tr("seconds") ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?= tr("Upload Max Filesize") ?>:</strong></td>
                                    <td><?= $system_status['upload_max_filesize'] ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?= tr("Post Max Size") ?>:</strong></td>
                                    <td><?= $system_status['post_max_size'] ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?= tr("Free Disk Space") ?>:</strong></td>
                                    <td><?= $system_status['disk_space'] ?></td>
                                </tr>
                            </table>
                        </div>

                        <!-- Database & Extensions -->
                        <div class="col-md-6">
                            <h6 class="text-primary"><?= tr("Database Connection") ?></h6>
                            <div class="alert alert-<?= $system_status['database_connection']['class'] ?> py-2">
                                <i class="fas fa-database"></i> <?= $system_status['database_connection']['status'] ?>
                            </div>

                            <h6 class="text-primary"><?= tr("PHP Extensions") ?></h6>
                            <div class="row">
                                <?php foreach ($system_status['extensions'] as $ext => $info): ?>
                                    <div class="col-6 mb-2">
                                        <span class="badge badge-<?= $info['class'] ?> w-100">
                                            <?= $ext ?>: <?= $info['loaded'] ? tr('Loaded') : tr('Missing') ?>
                                        </span>
                                    </div>
                                <?php endforeach ?>
                            </div>

                            <h6 class="text-primary mt-3"><?= tr("Writable Directories") ?></h6>
                            <?php foreach ($system_status['writable_directories'] as $dir => $info): ?>
                                <div class="mb-2">
                                    <span class="badge badge-<?= $info['class'] ?> w-100">
                                        <?= $dir ?>: <?= ($info['writable'] && $info['exists']) ? tr('Writable') : tr('Not Writable') ?>
                                    </span>
                                </div>
                            <?php endforeach ?>
                        </div>
                    </div>
                    
                    <!-- Storage Status Section -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h6 class="text-primary"><?= tr("Storage Configuration Status") ?></h6>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-secondary"><?= tr("Storage Settings") ?></h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong><?= tr("Storage Method") ?>:</strong></td>
                                    <td>
                                        <?php if (($storage_config['storage_use_ftp'] ?? '0') == '1'): ?>
                                            <span class="badge badge-info"><?= tr("FTP Storage") ?></span>
                                        <?php else: ?>
                                            <span class="badge badge-success"><?= tr("Local Storage") ?></span>
                                        <?php endif ?>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong><?= tr("Storage Path") ?>:</strong></td>
                                    <td class="small"><?= $storage_config['storage_path'] ?? tr('Not set') ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?= tr("Allowed Extensions") ?>:</strong></td>
                                    <td class="small"><?= $storage_config['storage_extensions'] ?? tr('Not set') ?></td>
                                </tr>
                                <tr>
                                    <td><strong><?= tr("Max File Size") ?>:</strong></td>
                                    <td><?= $storage_config['storage_max_size'] ?? tr('Not set') ?></td>
                                </tr>
                                <?php if (($storage_config['storage_use_ftp'] ?? '0') == '1'): ?>
                                <tr>
                                    <td><strong><?= tr("FTP Server") ?>:</strong></td>
                                    <td class="small"><?= $storage_config['storage_ftp_server'] ?? tr('Not set') ?></td>
                                </tr>
                                <?php endif ?>
                            </table>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="text-secondary"><?= tr("Storage Health") ?></h6>
                            
                            <!-- Local Storage Status -->
                            <div class="mb-3">
                                <label class="small font-weight-bold"><?= tr("Local Storage Path") ?></label>
                                <div class="alert alert-<?= $storage_config['path_status']['class'] ?? 'secondary' ?> py-2 mb-0">
                                    <i class="fas fa-folder"></i> <?= $storage_config['path_status']['status'] ?? tr('Unknown') ?>
                                </div>
                            </div>
                            
                            <!-- FTP Storage Status -->
                            <div class="mb-3">
                                <label class="small font-weight-bold"><?= tr("FTP Connection") ?></label>
                                <div class="alert alert-<?= $storage_config['ftp_status']['class'] ?? 'secondary' ?> py-2 mb-0">
                                    <i class="fas fa-network-wired"></i> <?= $storage_config['ftp_status']['status'] ?? tr('Unknown') ?>
                                </div>
                            </div>
                            
                            <!-- Storage Quick Actions -->
                            <div class="mt-3">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="testStorageConnection()">
                                    <i class="fas fa-check-circle"></i> <?= tr("Test Storage") ?>
                                </button>
                                <a href="#storage" class="btn btn-outline-secondary btn-sm" onclick="$('#storage-tab').tab('show');">
                                    <i class="fas fa-cog"></i> <?= tr("Configure") ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Save Button -->
    <div class="mt-4 mb-4">
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="fas fa-save"></i> <?= tr("Save") ?>
        </button>
        <button type="button" class="btn btn-secondary btn-lg ml-2" onclick="location.reload()">
            <i class="fas fa-undo"></i> <?= tr("Reset") ?>
        </button>
    </div>
</form>

<?= $this->endSection() ?>

<?= $this->section("script") ?>
<script type="text/javascript">
    $(document).ready(function() {
        // Handle successful form submission
        $(document).on('submit_complete', function(event, data) {
            if (data.success) {
                // Show success message and optionally reload
                setTimeout(function() {
                    location.reload();
                }, 1500);
            }
        });

        // Remember active tab
        var activeTab = localStorage.getItem('activeSettingsTab');
        if (activeTab) {
            $('#settingsTabs a[href="' + activeTab + '"]').tab('show');
        }

        // Save active tab to localStorage
        $('#settingsTabs a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
            localStorage.setItem('activeSettingsTab', $(e.target).attr('href'));
        });

        // Auto-refresh system status every 30 seconds
        setInterval(function() {
            if ($('#system-status-tab').hasClass('active')) {
                // Only refresh if system status tab is active
                $.get(window.location.href + '?refresh_status=1', function(data) {
                    // Update system status content if needed
                });
            }
        }, 30000);

        // Storage configuration handlers
        $('#storage_use_ftp').on('change', function() {
            var useFtp = $(this).val() == '1';
            toggleStorageFields(useFtp);
        });

        // Initialize storage field visibility
        toggleStorageFields($('#storage_use_ftp').val() == '1');

        // Color picker event handlers
        setupColorPickers();

        // Color picker event handlers
        setupColorPickers();
    });

    // Setup color picker functionality
    function setupColorPickers() {
        // Sync color picker with text input
        $('#color_primary_input').on('change', function() {
            $('#color_primary_text').val($(this).val());
            $('input[name="color_primary"]').val($(this).val());
            $('#primary_preview').css('background-color', $(this).val());
            updateLivePreview();
        });

        $('#color_primary_text').on('input', function() {
            if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test($(this).val())) {
                $('#color_primary_input').val($(this).val());
                $('input[name="color_primary"]').val($(this).val());
                $('#primary_preview').css('background-color', $(this).val());
                updateLivePreview();
            }
        });

        $('#color_secondary_input').on('change', function() {
            $('#color_secondary_text').val($(this).val());
            $('input[name="color_secondary"]').val($(this).val());
            $('#secondary_preview').css('background-color', $(this).val());
            updateLivePreview();
        });

        $('#color_secondary_text').on('input', function() {
            if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test($(this).val())) {
                $('#color_secondary_input').val($(this).val());
                $('input[name="color_secondary"]').val($(this).val());
                $('#secondary_preview').css('background-color', $(this).val());
                updateLivePreview();
            }
        });

        $('#color_body_input').on('change', function() {
            $('#color_body_text').val($(this).val());
            $('input[name="color_body"]').val($(this).val());
            $('#body_preview').css('background-color', $(this).val());
            updateLivePreview();
        });

        $('#color_body_text').on('input', function() {
            if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test($(this).val())) {
                $('#color_body_input').val($(this).val());
                $('input[name="color_body"]').val($(this).val());
                $('#body_preview').css('background-color', $(this).val());
                updateLivePreview();
            }
        });
    }

    // Apply color preset
    function applyColorPreset(primary, secondary, body) {
        // Update all inputs
        $('#color_primary_input, input[name="color_primary"], #color_primary_text').val(primary);
        $('#color_secondary_input, input[name="color_secondary"], #color_secondary_text').val(secondary);
        $('#color_body_input, input[name="color_body"], #color_body_text').val(body);
        
        // Update previews
        $('#primary_preview').css('background-color', primary);
        $('#secondary_preview').css('background-color', secondary);
        $('#body_preview').css('background-color', body);
        
        // Update live preview
        updateLivePreview();
    }

    // Update live preview
    function updateLivePreview() {
        var primary = $('input[name="color_primary"]').val();
        var secondary = $('input[name="color_secondary"]').val();
        var body = $('input[name="color_body"]').val();
        
        // Update preview card
        $('#color_preview_card .card-header').css('background-color', primary);
        $('#color_preview_card .btn-primary').css('background-color', primary);
        $('#color_preview_card .btn-secondary').css('background-color', secondary);
        $('#color_preview_card .badge-primary').css('background-color', primary);
        $('#color_preview_card .badge-secondary').css('background-color', secondary);
        
        // Update CSS custom properties for live preview
        document.documentElement.style.setProperty('--primary', primary);
        document.documentElement.style.setProperty('--secondary', secondary);
        document.documentElement.style.setProperty('--body', body);
    }

    // Setup color picker functionality
    function setupColorPickers() {
        // Sync color picker with text input
        $('#color_primary_input').on('change', function() {
            $('#color_primary_text').val($(this).val());
            $('input[name="color_primary"]').val($(this).val());
            $('#primary_preview').css('background-color', $(this).val());
            updateLivePreview();
        });

        $('#color_primary_text').on('input', function() {
            if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test($(this).val())) {
                $('#color_primary_input').val($(this).val());
                $('input[name="color_primary"]').val($(this).val());
                $('#primary_preview').css('background-color', $(this).val());
                updateLivePreview();
            }
        });

        $('#color_secondary_input').on('change', function() {
            $('#color_secondary_text').val($(this).val());
            $('input[name="color_secondary"]').val($(this).val());
            $('#secondary_preview').css('background-color', $(this).val());
            updateLivePreview();
        });

        $('#color_secondary_text').on('input', function() {
            if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test($(this).val())) {
                $('#color_secondary_input').val($(this).val());
                $('input[name="color_secondary"]').val($(this).val());
                $('#secondary_preview').css('background-color', $(this).val());
                updateLivePreview();
            }
        });

        $('#color_body_input').on('change', function() {
            $('#color_body_text').val($(this).val());
            $('input[name="color_body"]').val($(this).val());
            $('#body_preview').css('background-color', $(this).val());
            updateLivePreview();
        });

        $('#color_body_text').on('input', function() {
            if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test($(this).val())) {
                $('#color_body_input').val($(this).val());
                $('input[name="color_body"]').val($(this).val());
                $('#body_preview').css('background-color', $(this).val());
                updateLivePreview();
            }
        });
    }

    // Apply color preset
    function applyColorPreset(primary, secondary, body) {
        // Update all inputs
        $('#color_primary_input, input[name="color_primary"], #color_primary_text').val(primary);
        $('#color_secondary_input, input[name="color_secondary"], #color_secondary_text').val(secondary);
        $('#color_body_input, input[name="color_body"], #color_body_text').val(body);
        
        // Update previews
        $('#primary_preview').css('background-color', primary);
        $('#secondary_preview').css('background-color', secondary);
        $('#body_preview').css('background-color', body);
        
        // Update live preview
        updateLivePreview();
    }

    // Update live preview
    function updateLivePreview() {
        var primary = $('input[name="color_primary"]').val();
        var secondary = $('input[name="color_secondary"]').val();
        var body = $('input[name="color_body"]').val();
        
        // Update preview card
        $('#color_preview_card .card-header').css('background-color', primary);
        $('#color_preview_card .btn-primary').css('background-color', primary);
        $('#color_preview_card .btn-secondary').css('background-color', secondary);
        $('#color_preview_card .badge-primary').css('background-color', primary);
        $('#color_preview_card .badge-secondary').css('background-color', secondary);
        
        // Update CSS custom properties for live preview
        document.documentElement.style.setProperty('--primary', primary);
        document.documentElement.style.setProperty('--secondary', secondary);
        document.documentElement.style.setProperty('--body', body);
    }

    // Toggle storage field visibility based on storage method
    function toggleStorageFields(useFtp) {
        // You can add conditional showing/hiding of fields here if needed
        // For now, all fields are always visible for better user experience
    }

    // Test storage connection function
    function testStorageConnection() {
        var button = $('button[onclick="testStorageConnection()"]');
        var originalText = button.html();
        
        button.html('<i class="fas fa-spinner fa-spin"></i> ' + '<?= tr("Testing...") ?>');
        button.prop('disabled', true);
        
        // Get current form values
        var formData = {
            storage_use_ftp: $('#storage_use_ftp').val(),
            storage_path: $('input[name="storage_path"]').val(),
            storage_ftp_server: $('input[name="storage_ftp_server"]').val(),
            storage_ftp_username: $('input[name="storage_ftp_username"]').val(),
            storage_ftp_password: $('input[name="storage_ftp_password"]').val(),
            test_storage: 1,
            <?= csrf_token() ?>: '<?= csrf_hash() ?>'
        };
        
        $.post(window.location.href, formData)
            .done(function(response) {
                if (response.success) {
                    toastr.success('<?= tr("Storage connection test successful") ?>');
                    // Optionally reload to update status indicators
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    toastr.error('<?= tr("Storage connection test failed") ?>: ' + (response.message || '<?= tr("Unknown error") ?>'));
                }
            })
            .fail(function() {
                toastr.error('<?= tr("Failed to test storage connection") ?>');
            })
            .always(function() {
                button.html(originalText);
                button.prop('disabled', false);
            });
    }
</script>
<?= $this->endSection() ?>
