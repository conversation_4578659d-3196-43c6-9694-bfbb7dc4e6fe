<?php
namespace App\Libraries;


class Navigation
{
    private $items = [];

    public function add($item)
    {
        $this->items[] = $item;
        return $this;
    }

    public function add_sub($name, $sub_item)
    {
        for ($i = 0; $i < count($this->items); $i++) {
            if ($this->items[$i]['name'] == $name) {
                $this->items[$i]['links'][] = $sub_item;
                break;
            }
        }
        return $this;
    }

    public function list()
    {
        $sorted_items = $this->items;
        
        // Sort the main items based on the "order" property
        usort($sorted_items, function($a, $b) {
            return $a['order'] <=> $b['order'];
        });

        // Sort the sub-items based on the "order" property
        foreach ($sorted_items as &$item) {
            if (!empty($item['links'])) {
                usort($item['links'], function($a, $b) {
                    return $a['order'] <=> $b['order'];
                });
            }
        }

        return $sorted_items;
    }
}


