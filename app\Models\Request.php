<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Request extends Model
{
    use SoftDeletes;

    protected $table = 'requests';
    protected $fillable = ['name', 'phone', 'email', 'gender', 'birth', 'major', 'status',"interests","city_id","reg_id","updated_by","request_status","reason","qualification","card_id","confirm_expiry","is_disabled"];


    protected function phone(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => _dr($value),
            set: fn (string $value) => _cr($value),
        );
    }

    protected function email(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => _dr($value),
            set: fn (string $value) => _cr($value),
        );
    }

    protected function cardId(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => _dr($value),
            set: fn (string $value) => _cr($value),
        );
    }

    // public function getAttributeValue($key)
    // {
    //     $value = parent::getAttributeValue($key);
    //     if (in_array($key, $this->encryptable)) {
    //         $value = _dr($value); 
    //     }
    //     return $value;
    // }
  

    
}