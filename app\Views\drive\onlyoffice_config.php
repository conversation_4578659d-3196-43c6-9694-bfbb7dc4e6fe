<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<!-- OnlyOffice Configuration Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-cog mr-2"></i><?= tr("OnlyOffice Configuration") ?>
    </h1>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Configuration Form -->
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary"><?= tr("OnlyOffice Settings") ?></h6>
            </div>
            <div class="card-body">
                <form id="onlyofficeConfigForm">
                    <div class="form-group">
                        <label for="onlyoffice_url"><?= tr("OnlyOffice Document Server URL") ?></label>
                        <input type="url" class="form-control" id="onlyoffice_url" name="onlyoffice_url" 
                               value="<?= esc($onlyOfficeUrl) ?>" required>
                        <small class="form-text text-muted">
                            <?= tr("The URL of your OnlyOffice Document Server (e.g., http://localhost:8080)") ?>
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="onlyoffice_secret"><?= tr("OnlyOffice Secret Key") ?></label>
                        <input type="password" class="form-control" id="onlyoffice_secret" name="onlyoffice_secret"
                               value="<?= esc($onlyOfficeSecret) ?>">
                        <small class="form-text text-muted">
                            <?= tr("Optional secret key for secure communication with OnlyOffice Document Server") ?>
                        </small>
                    </div>

                    <div class="form-group">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="bypass_signature" name="bypass_signature"
                                   <?= get_option('onlyoffice_bypass_signature') === 'true' ? 'checked' : '' ?>>
                            <label class="form-check-label" for="bypass_signature">
                                <?= tr("Bypass JWT Signature Verification (Debug Mode)") ?>
                            </label>
                        </div>
                        <small class="form-text text-muted text-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <?= tr("Only enable this for debugging. Disable in production for security.") ?>
                        </small>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save mr-1"></i><?= tr("Save Configuration") ?>
                        </button>
                        <button type="button" class="btn btn-secondary ml-2" onclick="testConnection()">
                            <i class="fas fa-plug mr-1"></i><?= tr("Test Basic Connection") ?>
                        </button>
                        <button type="button" class="btn btn-info ml-2" onclick="runComprehensiveTest()">
                            <i class="fas fa-cogs mr-1"></i><?= tr("Run Full Test") ?>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Test Results -->
        <div class="card mt-4" id="testResults" style="display: none;">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary"><?= tr("Connection Test Results") ?></h6>
            </div>
            <div class="card-body" id="testResultsBody">
                <!-- Test results will be displayed here -->
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Information Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary"><?= tr("About OnlyOffice") ?></h6>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    <?= tr("OnlyOffice Document Server enables collaborative editing of documents, spreadsheets, and presentations directly in your browser.") ?>
                </p>
                
                <h6><?= tr("Supported File Types") ?></h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-file-word text-primary mr-2"></i><?= tr("Documents: DOC, DOCX, ODT, RTF, TXT") ?></li>
                    <li><i class="fas fa-file-excel text-success mr-2"></i><?= tr("Spreadsheets: XLS, XLSX, ODS, CSV") ?></li>
                    <li><i class="fas fa-file-powerpoint text-warning mr-2"></i><?= tr("Presentations: PPT, PPTX, ODP") ?></li>
                </ul>

                <h6 class="mt-3"><?= tr("Features") ?></h6>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success mr-2"></i><?= tr("Real-time collaboration") ?></li>
                    <li><i class="fas fa-check text-success mr-2"></i><?= tr("Version history") ?></li>
                    <li><i class="fas fa-check text-success mr-2"></i><?= tr("Comments and reviews") ?></li>
                    <li><i class="fas fa-check text-success mr-2"></i><?= tr("Auto-save") ?></li>
                </ul>
            </div>
        </div>

        <!-- Installation Guide -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary"><?= tr("Installation Guide") ?></h6>
            </div>
            <div class="card-body">
                <p class="text-muted">
                    <?= tr("To use OnlyOffice integration, you need to install OnlyOffice Document Server.") ?>
                </p>
                
                <h6><?= tr("Docker Installation") ?></h6>
                <pre class="bg-light p-2 rounded"><code>docker run -i -t -d -p 8080:80 \
  --restart=always \
  onlyoffice/documentserver</code></pre>

                <h6 class="mt-3"><?= tr("Manual Installation") ?></h6>
                <p class="text-muted small">
                    <?= tr("Visit the") ?> 
                    <a href="https://helpcenter.onlyoffice.com/installation/docs-developer-configuring.aspx" target="_blank">
                        <?= tr("OnlyOffice documentation") ?>
                    </a> 
                    <?= tr("for detailed installation instructions.") ?>
                </p>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('script') ?>
<script>
$(document).ready(function() {
    // Handle form submission
    $('#onlyofficeConfigForm').on('submit', function(e) {
        e.preventDefault();
        saveConfiguration();
    });
});

function saveConfiguration() {
    const formData = new FormData(document.getElementById('onlyofficeConfigForm'));
    
    $.ajax({
        url: base_url + 'drive/onlyoffice/config/save',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                showMessage(response.message, 'success');
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage(['Failed to save configuration. Please try again.'], 'error');
        }
    });
}

function testConnection() {
    const url = document.getElementById('onlyoffice_url').value;
    
    if (!url) {
        showMessage(['Please enter OnlyOffice URL first.'], 'error');
        return;
    }
    
    const testResults = document.getElementById('testResults');
    const testResultsBody = document.getElementById('testResultsBody');
    
    testResults.style.display = 'block';
    testResultsBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="sr-only">Testing...</span>
            </div>
            <p class="mt-2">Testing connection to OnlyOffice Document Server...</p>
        </div>
    `;
    
    // Test connection by trying to load the API script
    const testScript = document.createElement('script');
    testScript.src = url + '/web-apps/apps/api/documents/api.js';
    
    testScript.onload = function() {
        testResultsBody.innerHTML = `
            <div class="alert alert-success">
                <i class="fas fa-check-circle mr-2"></i>
                <strong>Connection successful!</strong><br>
                OnlyOffice Document Server is accessible and ready to use.
            </div>
        `;
        document.head.removeChild(testScript);
    };
    
    testScript.onerror = function() {
        testResultsBody.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-times-circle mr-2"></i>
                <strong>Connection failed!</strong><br>
                Unable to connect to OnlyOffice Document Server at the specified URL.
                <ul class="mt-2 mb-0">
                    <li>Check if the URL is correct</li>
                    <li>Ensure OnlyOffice Document Server is running</li>
                    <li>Verify network connectivity</li>
                    <li>Check firewall settings</li>
                </ul>
            </div>
        `;
        document.head.removeChild(testScript);
    };
    
    document.head.appendChild(testScript);
}

function runComprehensiveTest() {
    const testResults = document.getElementById('testResults');
    const testResultsBody = document.getElementById('testResultsBody');

    testResults.style.display = 'block';
    testResultsBody.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="sr-only">Running comprehensive tests...</span>
            </div>
            <p class="mt-2">Running comprehensive OnlyOffice tests...</p>
        </div>
    `;

    $.ajax({
        url: base_url + 'drive/onlyoffice/test-connection',
        type: 'POST',
        success: function(response) {
            if (response.success) {
                displayComprehensiveTestResults(response.data);
            } else {
                testResultsBody.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-times-circle mr-2"></i>
                        <strong>Test failed!</strong><br>
                        ${response.message ? response.message.join('<br>') : 'Unknown error'}
                    </div>
                `;
            }
        },
        error: function() {
            testResultsBody.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle mr-2"></i>
                    <strong>Test failed!</strong><br>
                    Unable to run comprehensive tests. Please check your configuration.
                </div>
            `;
        }
    });
}

function displayComprehensiveTestResults(data) {
    const testResultsBody = document.getElementById('testResultsBody');
    const tests = data.tests;
    const config = data.configuration;

    let html = `
        <div class="row">
            <div class="col-md-6">
                <h6>Configuration Status</h6>
                <ul class="list-unstyled">
                    <li><strong>OnlyOffice URL:</strong> ${config.onlyoffice_url}</li>
                    <li><strong>Secret Configured:</strong> ${config.secret_configured ? 'Yes' : 'No'}</li>
                    <li><strong>Bypass Signature:</strong> ${config.bypass_signature ? 'Yes (Debug Mode)' : 'No'}</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Test Results</h6>
                <ul class="list-unstyled">
    `;

    for (const [testName, result] of Object.entries(tests)) {
        const icon = result.success ? 'fas fa-check-circle text-success' : 'fas fa-times-circle text-danger';
        const testLabel = testName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        html += `<li><i class="${icon} mr-2"></i>${testLabel}: ${result.message}</li>`;
    }

    html += `
                </ul>
            </div>
        </div>
    `;

    // Add detailed test information
    html += '<hr><h6>Detailed Test Information</h6>';
    for (const [testName, result] of Object.entries(tests)) {
        const alertClass = result.success ? 'alert-success' : 'alert-danger';
        html += `
            <div class="alert ${alertClass}">
                <strong>${testName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:</strong><br>
                ${result.message}
                ${result.url ? `<br><small>URL: ${result.url}</small>` : ''}
                ${result.response ? `<br><small>Response: ${JSON.stringify(result.response)}</small>` : ''}
            </div>
        `;
    }

    testResultsBody.innerHTML = html;
}

function showMessage(messages, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const messageHtml = messages.map(msg => `<div class="alert ${alertClass}">${msg}</div>`).join('');

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert
    $('.content').prepend(messageHtml);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
<?= $this->endSection() ?>
