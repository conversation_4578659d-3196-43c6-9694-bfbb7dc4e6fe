<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateDriveSystemTables extends Migration
{
    public function up()
    {
        // 1. Create directories table
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'parent_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'user_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'path' => [
                'type' => 'TEXT',
            ],
            'level' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'size_bytes' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'default' => 0,
            ],
            'security_classification' => [
                'type' => 'ENUM',
                'constraint' => ['PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'SECRET', 'TOP_SECRET'],
                'default' => 'INTERNAL',
            ],
            'is_shared' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'is_encrypted' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'encryption_key_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'permissions' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'metadata' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            ],
            'deleted_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'created_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'updated_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('parent_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey(['path'], false, false, 'idx_path');
        $this->forge->addKey('security_classification');
        $this->forge->addKey('created_at');
        $this->forge->createTable('directories');

        // 2. Create encryption_keys table (needed before files table)
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'key_identifier' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'unique' => true,
            ],
            'algorithm' => [
                'type' => 'ENUM',
                'constraint' => ['AES-256-GCM', 'AES-256-CBC', 'ChaCha20-Poly1305'],
            ],
            'key_hash' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'key_derivation_salt' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'created_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['ACTIVE', 'RETIRED', 'COMPROMISED'],
                'default' => 'ACTIVE',
            ],
            'rotation_schedule_days' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 90,
            ],
            'last_rotated_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'next_rotation_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'usage_count' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'default' => 0,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'retired_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('key_identifier');
        $this->forge->addKey('status');
        $this->forge->addKey('next_rotation_at');
        $this->forge->createTable('encryption_keys');

        // 3. Create files table
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'original_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'directory_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'user_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'file_path' => [
                'type' => 'TEXT',
            ],
            'mime_type' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'size_bytes' => [
                'type' => 'BIGINT',
                'constraint' => 20,
            ],
            'hash_sha256' => [
                'type' => 'VARCHAR',
                'constraint' => 64,
            ],
            'hash_md5' => [
                'type' => 'VARCHAR',
                'constraint' => 32,
            ],
            'version_number' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 1,
            ],
            'current_version_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'security_classification' => [
                'type' => 'ENUM',
                'constraint' => ['PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'SECRET', 'TOP_SECRET'],
                'default' => 'INTERNAL',
            ],
            'is_encrypted' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'encryption_key_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'is_locked' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'locked_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'locked_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'lock_expires_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'download_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'view_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'is_shared' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'watermark_enabled' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'retention_policy_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'expires_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'tags' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'metadata' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'thumbnail_path' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
            ],
            'preview_path' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['ACTIVE', 'PROCESSING', 'QUARANTINED', 'ARCHIVED'],
                'default' => 'ACTIVE',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            ],
            'deleted_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'created_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'updated_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('directory_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('hash_sha256');
        $this->forge->addKey('security_classification');
        $this->forge->addKey('created_at');
        $this->forge->addKey('status');
        $this->forge->createTable('files');

        // 4. Create file_versions table
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'file_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'version_number' => [
                'type' => 'INT',
                'constraint' => 11,
            ],
            'file_path' => [
                'type' => 'TEXT',
            ],
            'size_bytes' => [
                'type' => 'BIGINT',
                'constraint' => 20,
            ],
            'hash_sha256' => [
                'type' => 'VARCHAR',
                'constraint' => 64,
            ],
            'hash_md5' => [
                'type' => 'VARCHAR',
                'constraint' => 32,
            ],
            'mime_type' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'change_summary' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'is_major_version' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'diff_path' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'compression_type' => [
                'type' => 'ENUM',
                'constraint' => ['NONE', 'GZIP', 'LZ4'],
                'default' => 'NONE',
            ],
            'encryption_key_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'created_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'restored_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'restored_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey(['file_id', 'version_number'], false, true, 'unique_file_version');
        $this->forge->addKey('file_id');
        $this->forge->addKey('created_at');
        $this->forge->createTable('file_versions');

        // 5. Create file_shares table
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'file_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'directory_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'shared_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'shared_with_user_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'shared_with_group_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'shared_with_external_email' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'permission_level' => [
                'type' => 'ENUM',
                'constraint' => ['VIEW', 'COMMENT', 'EDIT', 'FULL_CONTROL'],
            ],
            'can_download' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'can_print' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'can_copy' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'can_share' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'watermark_required' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'access_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'max_access_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
            ],
            'share_token' => [
                'type' => 'VARCHAR',
                'constraint' => 64,
                'unique' => true,
                'null' => true,
            ],
            'password_hash' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'requires_approval' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'approval_status' => [
                'type' => 'ENUM',
                'constraint' => ['PENDING', 'APPROVED', 'REJECTED'],
                'null' => true,
            ],
            'approved_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'approved_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'expires_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'notify_on_access' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'ip_restrictions' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'domain_restrictions' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            ],
            'accessed_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'revoked_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'revoked_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('file_id');
        $this->forge->addKey('directory_id');
        $this->forge->addKey('shared_by');
        $this->forge->addKey('shared_with_user_id');
        $this->forge->addKey('share_token');
        $this->forge->addKey('expires_at');
        $this->forge->createTable('file_shares');

        // 6. Create file_access_logs table
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'file_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'directory_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'user_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'session_id' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'action' => [
                'type' => 'ENUM',
                'constraint' => ['VIEW', 'DOWNLOAD', 'EDIT', 'DELETE', 'SHARE', 'COPY', 'PRINT', 'PREVIEW', 'UPLOAD', 'MOVE', 'RENAME'],
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 45,
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'geolocation' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'device_fingerprint' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'success' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'failure_reason' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'file_version_accessed' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
            ],
            'bytes_transferred' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'null' => true,
            ],
            'duration_seconds' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
            ],
            'risk_score' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'metadata' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('file_id');
        $this->forge->addKey('directory_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('action');
        $this->forge->addKey('ip_address');
        $this->forge->addKey('created_at');
        $this->forge->addKey('risk_score');
        $this->forge->createTable('file_access_logs');

        // 7. Create additional tables for comprehensive functionality
        $this->createFilePermissionsTable();
        $this->createFileTagsTables();
        $this->createFileCommentsTable();
        $this->createFileTrashTable();
    }

    private function createFilePermissionsTable()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'file_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'directory_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'user_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'group_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'permission_type' => [
                'type' => 'ENUM',
                'constraint' => ['OWNER', 'READ', 'WRITE', 'DELETE', 'SHARE', 'ADMIN'],
            ],
            'granted_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'inherited_from_directory' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'conditions' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'expires_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'revoked_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'revoked_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('file_id');
        $this->forge->addKey('directory_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('group_id');
        $this->forge->addKey('permission_type');
        $this->forge->createTable('file_permissions');
    }

    private function createFileTagsTables()
    {
        // File tags table
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'color' => [
                'type' => 'VARCHAR',
                'constraint' => 7,
                'null' => true,
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'category' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'is_system_tag' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'created_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'usage_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey(['name'], false, true, 'unique_tag_name');
        $this->forge->addKey('category');
        $this->forge->addKey('usage_count');
        $this->forge->createTable('file_tags');

        // File tag assignments table
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'file_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'tag_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'assigned_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey(['file_id', 'tag_id'], false, true, 'unique_file_tag');
        $this->forge->addKey('file_id');
        $this->forge->addKey('tag_id');
        $this->forge->createTable('file_tag_assignments');
    }

    private function createFileCommentsTable()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'file_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'file_version_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'user_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'parent_comment_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'content' => [
                'type' => 'TEXT',
            ],
            'content_type' => [
                'type' => 'ENUM',
                'constraint' => ['TEXT', 'ANNOTATION', 'REVIEW'],
                'default' => 'TEXT',
            ],
            'position_data' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['ACTIVE', 'RESOLVED', 'DELETED'],
                'default' => 'ACTIVE',
            ],
            'is_internal' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'security_classification' => [
                'type' => 'ENUM',
                'constraint' => ['PUBLIC', 'INTERNAL', 'CONFIDENTIAL'],
                'default' => 'INTERNAL',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            ],
            'resolved_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'resolved_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('file_id');
        $this->forge->addKey('file_version_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('parent_comment_id');
        $this->forge->addKey('status');
        $this->forge->addKey('created_at');
        $this->forge->createTable('file_comments');
    }

    private function createFileTrashTable()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'original_file_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'original_directory_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'original_path' => [
                'type' => 'TEXT',
            ],
            'original_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'deleted_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'deletion_reason' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'file_data' => [
                'type' => 'JSON',
            ],
            'auto_delete_at' => [
                'type' => 'TIMESTAMP',
            ],
            'restored_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'restored_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'permanently_deleted_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'permanently_deleted_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('deleted_by');
        $this->forge->addKey('auto_delete_at');
        $this->forge->addKey('original_file_id');
        $this->forge->addKey('original_directory_id');
        $this->forge->createTable('file_trash');
    }

    public function down()
    {
        // Drop tables in reverse order to handle foreign key constraints
        $this->forge->dropTable('file_trash', true);
        $this->forge->dropTable('file_comments', true);
        $this->forge->dropTable('file_tag_assignments', true);
        $this->forge->dropTable('file_tags', true);
        $this->forge->dropTable('file_permissions', true);
        $this->forge->dropTable('file_access_logs', true);
        $this->forge->dropTable('file_shares', true);
        $this->forge->dropTable('file_versions', true);
        $this->forge->dropTable('files', true);
        $this->forge->dropTable('encryption_keys', true);
        $this->forge->dropTable('directories', true);
    }
} 