# UI Layout Patterns & Components

## Overview
The Career Application System uses a sophisticated Bootstrap-based layout with custom styling, responsive design, and consistent component patterns. This document covers card creation, button placement, navigation structure, and view organization.

## Layout Architecture

### Main Layout Structure
**File**: `app/Views/layouts/main.php`

```html
<!DOCTYPE html>
<html lang="<?= strtolower(get_local()) ?>" dir="<?= strtolower(get_local())=="ar"?'rtl':'ltr' ?>">
    <?= view("inc/head.php") ?>
    <body class="<?= $page['class']??'' ?>">
        <?= view("inc/header.php") ?>
        
        <div class="content pt-0 mb-5">
            <?php display()->messages() ?>
            <?= $this->renderSection('content') ?>
        </div>
        
        <?= view("inc/foot.php") ?>
        <?= $this->renderSection('script') ?>
    </body>
</html>
```

### Layout Components
1. **Header**: Navigation bar with user menu and branding
2. **Sidebar**: Collapsible navigation menu (authenticated users only)
3. **Content Area**: Main content with breadcrumbs and page actions
4. **Footer**: JavaScript includes and custom scripts

## Card Layout Patterns

### Basic Card Structure
Cards are the primary content containers with consistent styling:

```html
<div class="d-flex">
    <h3>Card Title</h3>
    <div>
        buttons
    </div>
</div>
<div class="card">
  
    <div class="card-body">
        <!-- Card content -->
    </div>
    <div class="card-footer">
        <!-- Action buttons -->
    </div>
</div>
```

### Card Styling Classes
- `shadow`: Adds drop shadow effect
- `border-0`: Removes default border
- `rounded-0`: Square corners (custom styling)
- `card-hover`: Hover effect with transform animation

### Application Form Card
**File**: `app/Views/my/form.php`

```html
<div class="card shadow border-0 rounded-0">

    <div class="card-body">
        <!-- Multi-section form content -->
        <div class="row">
            <div class="col-md-6">
                <!-- Form fields with input groups -->
            </div>
        </div>
    </div>
    <div class="card-footer">
        <button class="btn btn-primary rounded-0 border-0 btn-lg py-2 px-3">
            <?= tr("Submit") ?> <i class="fas fa-check"></i>
        </button>
    </div>
</div>
```

### Data Table Card
**File**: `app/Views/jobs/index.php`

```html
<div class="card shadow border-0">
    <table class="table table-striped dt" id="vipTable">
        <thead>
            <tr>
                <td><?= tr("ID") ?></td>
                <th><?= tr("job_name") ?></th>
                <th><?= tr("Applications") ?></th>
                <th><?= tr("Status") ?></th>
            </tr>
        </thead>
        <tbody></tbody>
    </table>
</div>
```

## Button Placement & Patterns

### Page Header Actions
Buttons are consistently placed in the top-right of page headers:

```html
<div class="d-flex justify-content-between mb-1 mt-3">
    <h3><?= tr("Page Title") ?></h3>
    <div>
        <button class="btn btn-secondary btn-sm" data-toggle="modal" data-target="#import-modal">
            <i class="fas fa-plus"></i> <?= tr("Import") ?>
        </button>
        <button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#new-modal">
            <i class="fas fa-plus"></i> <?= tr("Add new") ?>
        </button>
    </div>
</div>
```

### Button Styling Classes
- `btn-primary`: Primary action (custom red color: #920808)
- `btn-secondary`: Secondary action (custom blue: #475273)
- `btn-sm`: Small button size
- `btn-lg`: Large button size
- `rounded-0`: Square corners
- `border-0`: No border

### Icon + Text Pattern
All buttons follow the icon + text pattern:

```html
<button class="btn btn-primary">
    <i class="fas fa-save"></i> <?= tr("Save") ?>
</button>
```

### Modal Footer Buttons
Standard modal footer button arrangement:

```html
<div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-dismiss="modal">
        <?= tr("Close") ?>
    </button>
    <button type="submit" class="btn btn-primary">
        <?= tr("Submit") ?>
    </button>
</div>
```

## Form Layout Patterns

### Input Groups with Icons
Consistent input styling with prepended icons:

```html
<div class="col-md-6">
    <label required><?= tr("Full name Arabic") ?></label>
    <div class="input-group">
        <div class="input-group-prepend">
            <span class="input-group-text"><i class="far fa-user"></i></span>
        </div>
        <input type="text" name="name" class="form-control" value="<?= esc($application->name) ?>">
    </div>
    <br>
</div>
```

### Phone Validation Pattern
Special pattern for phone number validation:

```html
<div class="input-group">
    <div class="input-group-prepend">
        <span class="input-group-text"><i class="far fa-phone"></i></span>
    </div>
    <input type="tel" name="primary_phone" class="form-control text-center" id="primary_phone-input">
    <div class="input-group-prepend" id="phone-validate-status">
        <button type="button" class="btn btn-danger border-0 rounded-0" onclick="validate_phone()">
            <?= tr("Validate") ?>
        </button>
    </div>
</div>
```

### Table-Based Form Sections
Complex forms use tables for structured data entry:

```html
<div class="row mt-4">
    <div class="col table-responsive">
        <h3 class="text-primary"><?= tr("Qualifications") ?> <span class="text-danger">*</span></h3>
        <table class="table table-sm" id="qualifications-table">
            <thead>
                <tr class="bg-primary">
                    <th class="border-2"><?= tr('Major') ?></th>
                    <th class="border-2"><?= tr('Qualification') ?></th>
                    <th class="text-center border-2"></th>
                </tr>
            </thead>
            <tbody id="qualifications-tbody">
                <!-- Dynamic content -->
            </tbody>
            <tfoot id="qualification-form">
                <!-- Add new form -->
            </tfoot>
        </table>
    </div>
</div>
```

## Navigation Patterns

### Header Navigation
**File**: `app/Views/inc/header.auth.php`

```html
<div class="navbar navbar-expand-md navbar-dark bg-primary shadow border-0 rounded-0 m-0">
    <div class="navbar-header navbar-dark d-none d-md-flex align-items-md-center bg-primary">
        <div class="navbar-brand navbar-brand-md m-0">
            <?= env("app.name") ?>
        </div>
    </div>
    
    <!-- User dropdown -->
    <ul class="navbar-nav ml-auto">
        <li class="nav-item dropdown dropdown-user">
            <a href="#" class="navbar-nav-link d-flex align-items-center dropdown-toggle" data-toggle="dropdown">
                <i class="fas fa-user px-2"></i>
                <span><?= esc(auth()->name) ?></span>
            </a>
            <div class="dropdown-menu dropdown-menu-right">
                <a href="<?= base_url("users/profile") ?>" class="dropdown-item">
                    <i class="far fa-user-cog"></i> <?= tr("My profile") ?>
                </a>
                <div class="dropdown-divider"></div>
                <a href="<?= base_url("auth/logout") ?>" class="dropdown-item">
                    <i class="icon-switch2"></i> <?= tr("Logout") ?>
                </a>
            </div>
        </li>
    </ul>
</div>
```

### Sidebar Navigation
**File**: `app/Views/inc/navigation.php`

```html
<div class="sidebar sidebar-light sidebar-main sidebar-expand-md print-hide shadow border-0 rounded-0">
    <div class="sidebar-content">
        <!-- Logo -->
        <div class="card d-flex justify-content-center pt-3 text-center">
            <img src="<?= base_url("assets/images/logo.png") ?>" class="mx-auto" style="width:90%;" alt="">
        </div>
        
        <!-- Navigation menu -->
        <div class="card card-sidebar-mobile">
            <ul class="nav nav-sidebar" data-nav-type="accordion">
                <?php foreach (navigation()->list() as $key => $item) : ?>
                    <?php if (_can($item['role'])) : ?>
                        <li class="nav-item <?= count($item['links'])>1?'nav-item-submenu':'' ?>">
                            <a class="nav-link" href="<?= $item['links'][0]['attr'] ?>">
                                <i class="<?= $item['icon'] ?>"></i>
                                <span><?= tr($item['title']) ?></span>
                                <?php if (isset($item['badge']) && $item['badge']>0): ?>
                                    <span class="badge bg-primary px-2 align-self-center ml-auto">
                                        <?= $item['badge'] ?>
                                    </span>
                                <?php endif ?>
                            </a>
                        </li>
                    <?php endif ?>
                <?php endforeach ?>
            </ul>
        </div>
    </div>
</div>
```

### Breadcrumb Navigation
Dynamic breadcrumb generation in page headers:

```html
<div class="page-title py-0 d-flex">
    <h4>
        <?php $n=1; foreach ($nav['breadcrumb'] as $key => $value): ?>
            <?php if ($n!=count($nav['breadcrumb'])): ?>
                <a href="<?= $value ?>" class="text-primary">
                    <span class="font-weight-semibold" style="font-size: 1.2rem;"><?= esc($key) ?></span>
                </a> /
            <?php else: ?>
                <span class="font-weight-semibold text-muted"><?= esc($key) ?></span>
            <?php endif ?>
        <?php $n++; endforeach ?>
    </h4>
</div>
```

## Modal Patterns

### Standard Modal Structure
All modals follow a consistent structure:

```html
<div class="modal fade" id="modal-id" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?= tr("Modal Title") ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- Modal content -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <?= tr("Close") ?>
                </button>
                <button type="submit" class="btn btn-primary">
                    <?= tr("Submit") ?>
                </button>
            </div>
        </div>
    </div>
</div>
```

### Modal Size Classes
- `modal-xl`: Extra large modal (job creation/editing)
- `modal-lg`: Large modal
- `modal-md`: Medium modal (default)
- `modal-sm`: Small modal (confirmations)

### Form Modal Pattern
Forms within modals use AJAX submission:

```html
<form class="ajax" method="post" onsubmit="selectAllOptions('selectedUsers-new')">
    <div class="modal fade" id="new-modal">
        <!-- Modal structure -->
    </div>
</form>
```

## Tab Navigation Patterns

### Application Status Tabs
**File**: `app/Views/applications/count.php`

```html
<div class="d-flex justify-content-between mb-0">
    <ul class="nav nav-tabs mb-0">
        <?php foreach ($application_statuses as $key => $status): ?>
            <li class="nav-item">
                <a class="nav-link border-0 <?= ($tab == $status) ? 'active text-primary' : '' ?>"
                   href="<?= url(base_url("applications/$selected_job"))->add("status",$status) ?>">
                    <?= tr($status) ?>
                    <span class="badge badge-light rounded-pill px-2">
                        <?= $status_count[$status]??0 ?>
                    </span>
                </a>
            </li>
        <?php endforeach ?>
    </ul>
</div>
```

### Group Filter Buttons
Dynamic button groups for filtering:

```html
<div class="d-flex justify-content-between">
    <div>
        <a href="<?= url()->add("group",0) ?>"
           class="btn btn-<?= $selected_group==0?'primary':'secondary' ?> mx-1">
            غير معرف (<?= $default_test_count ?>)
        </a>
        <?php foreach ($test_groups as $key => $tg): ?>
            <a href="<?= url()->add("group",$tg->id) ?>"
               class="btn btn-<?= $selected_group==$tg->id?'primary':'secondary' ?> mx-1">
                <?= $tg->name ?> (<?= $tg->applications()->count() ?>/<?= $tg->application_limit ?>)
            </a>
        <?php endforeach ?>
    </div>
    <div>
        <a href="<?= base_url("applications/test_group/$selected_job") ?>" class="btn btn-link">
            <i class="fas fa-sync"></i> <?= tr("Reorder") ?>
        </a>
    </div>
</div>
```

## Dropdown Patterns

### Job Selection Dropdown
**File**: `app/Views/applications/index.php`

```html
<div class="dropdown">
    <button class="btn btn-primary text-start" type="button" style="min-width: 250px;"
            id="dropdownMenuButton" data-toggle="dropdown">
        <?php if (isset($job)): ?>
            <div class="row align-items-center" style="width: 100%;">
                <div class="col-8">
                    <h4><?= esc($job->name) ?></h4>
                    <p><small class="text-muted">GSC000<?= $job->id ?></small></p>
                </div>
                <div class="col-4 text-end"><i class="fas fa-angle-down"></i></div>
            </div>
        <?php endif ?>
    </button>
    <div class="dropdown-menu border-0 shadow rounded-0 py-0"
         style="min-width: 350px; max-height: 600px; overflow-y: scroll;">
        <?php foreach ($jobs as $key => $job): ?>
            <a class="dropdown-item <?= ($selected_job == $job->id) ? 'bg-primary' : '' ?>"
               href="<?= base_url("applications/".$job->id) ?>">
                <div class="row align-items-center" style="width: 100%;">
                    <div class="col-8">
                        <h4><?= $job->name ?></h4>
                        <p><small class="text-muted">GSC000<?= $job->id ?></small></p>
                    </div>
                    <div class="col-4 text-end">
                        <span class="badge bg-<?= ($selected_job == $job->id) ? 'light text-dark' : 'primary' ?> px-2">
                            <?= $job->applications->where("status","!=","Incomplete")->count() ?>
                        </span>
                    </div>
                </div>
            </a>
        <?php endforeach ?>
    </div>
</div>
```

## Data Table Patterns

### DataTable Configuration
Standard DataTable setup with AJAX:

```javascript
$(document).ready(function() {
    createDatatable("#vipTable", true, {
        ajax: "<?= base_url("jobs/datatable") ?>"
    });
});
```

### Filtered DataTable
DataTable with dynamic filtering:

```javascript
createDatatable("#Datatable", true, {
    ajax: {
        url: "<?= base_url('applications/datatable/'.$selected_job.'/'.$tab) ?>?group=<?= input("group",0) ?>",
        type: 'GET',
        data: function(d) {
            return $.extend({}, d, getFilterData());
        }
    }
});

function getFilterData() {
    return {
        gender: $('.gender').val(),
    };
}
```

### Table Action Buttons
Hover-based action buttons in table rows:

```css
.table-actions {
    display: none;
}

tr:hover .table-actions {
    display: block;
}
```

## Responsive Design Patterns

### Grid Layout
Bootstrap grid system with responsive breakpoints:

```html
<div class="row">
    <div class="col-md-4">
        <!-- Left column content -->
    </div>
    <div class="col-md-8">
        <!-- Right column content -->
    </div>
</div>
```

### Mobile Navigation
Collapsible navigation for mobile devices:

```html
<div class="d-flex flex-1 d-md-none">
    <div class="navbar-brand mr-auto py-1 align-items-center">
        <a href="<?= base_url() ?>" class="d-flex text-light align-items-center">
            <?= env("app.name") ?>
        </a>
    </div>
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-mobile">
        <i class="fas fa-angle-down"></i>
    </button>
</div>
```

### Responsive Tables
Table responsiveness with horizontal scrolling:

```html
<div class="col table-responsive">
    <table class="table table-sm" id="qualifications-table">
        <!-- Table content -->
    </table>
</div>
```

## Color Scheme & Theming

### CSS Custom Properties
**File**: `public/assets/css/custom.css`

```css
:root {
    --primary: #920808;
    --secondary: #475273;
    --primary_transparent: rgba(28,95,218,0.5);
    --secondary_transparent: rgba(238, 131, 37, 0.5);
}

.btn-primary, .bg-primary {
    background: var(--primary) !important;
}

.btn-secondary, .bg-secondary {
    background: var(--secondary) !important;
}

.text-primary {
    color: var(--primary) !important;
}
```

### Card Styling
Custom card appearance:

```css
.card, .modal-content {
    border-radius: 0px;
    border: 0;
}

.card-header:first-child, .modal-header {
    border-top-right-radius: 10px !important;
    border-top-left-radius: 10px !important;
}

.card-footer {
    border-bottom-right-radius: 10px !important;
    border-bottom-left-radius: 10px !important;
}
```

### Form Control Styling
Consistent form element appearance:

```css
.form-control {
    border: 1px solid #d8d8d8;
    border-radius: 0px;
    padding: 0.3375rem 0.875rem;
    height: calc(1.3385em + 0.875rem + 2px);
    box-shadow: none;
}

.form-control:focus {
    border-color: var(--primary);
    background-color: #ffffff;
    box-shadow: 0 0 0 0.2rem var(--primary_transparent);
}
```

## RTL Support

### Language Direction
Automatic RTL support based on locale:

```html
<html lang="<?= strtolower(get_local()) ?>" dir="<?= strtolower(get_local())=="ar"?'rtl':'ltr' ?>">
```

### RTL-Specific Styling
Conditional CSS for Arabic language:

```php
<?php if (current_lang()=="ar"): ?>
    <link href="<?= base_url() ?>assets/css/rtl/bootstrap.min.css" rel="stylesheet">
    <link href="<?= base_url() ?>assets/css/rtl/bootstrap_limitless.min.css" rel="stylesheet">
    <link href="<?= base_url() ?>assets/css/rtl/layout.min.css" rel="stylesheet">
    <link href="<?= base_url() ?>assets/css/rtl/components.min.css" rel="stylesheet">
    <link href="<?= base_url() ?>assets/css/rtl/colors.min.css" rel="stylesheet">
<?php else: ?>
    <link href="<?= base_url() ?>assets/css/style.css" rel="stylesheet">
<?php endif ?>
```

## Print Styles

### Print-Specific Classes
Show/hide elements for printing:

```css
.print-show {
    display: none;
}

@media print {
    .print-show {
        display: block;
    }
    .print-hide {
        display: none;
    }
}
```

### Print-Friendly Layout
Elements marked for print visibility:

```html
<div class="sidebar print-hide">
    <!-- Hidden in print -->
</div>

<div class="print-show">
    <!-- Only visible in print -->
</div>
```
