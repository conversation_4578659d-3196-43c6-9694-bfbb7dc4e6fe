<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>
<?php if ($data->id!=1 ): ?>
	<div>
		<a class="btn btn-danger after-confirm mb-2" href="<?= base_url("users/delete/".$data->id) ?>" ><?= tr("Delete") ?></a>
		
	
	</div>
	<?php endif ?>
	<a class="btn btn-secondary after-confirm mb-2" href="<?= base_url("users/reset_password/".$data->id) ?>" ><?= tr("Request set password") ?></a>
<div class="row justify-content-center" autocomplete="off">
	<div class="col-md-10">
		<div class="card shadow">
			
			<div class="card-body">
				<div class="d-lg-flex justify-content-lg-between">
					<div class="col-md-4">
						<ul class="nav nav-pills flex-column mr-lg-3 wmin-lg-250 mb-lg-0">
							<li class="nav-item">
								<a href="#stacked-left-pill1" class="nav-link active" data-toggle="tab">
									
									<?= tr("Profile") ?>
								</a>
							</li>
							<li class="nav-item">
								<a href="#stacked-left-pill2" class="nav-link " data-toggle="tab">
									
									<?= tr("Password") ?>
									
								</a>
							</li> 
							
						</ul>
					</div>
					<div class="col-md-8">
						<div class="tab-content">
							<div class="tab-pane fade active show" id="stacked-left-pill1">
								<form method="post" autocomplete="off" class="ajax">
									
									
									

									<div class="row">
										<div class="col-md-6">
											<label for=""><?= tr("Name") ?> *</label>
											<input type="text" name="name" class="form-control" required value="<?= $data->name ?>"><br>
										</div>
										<div class="col-md-6">
											<label for=""><?= tr("Card Id") ?> *</label>
											<input type="text" name="log_name" class="form-control" required value="<?= $data->log_name ?>"><br>
										</div>
										
									</div>
						
									
									<div class="row">
										<div class="col-md-6">
											<label for=""><?= tr("Phone") ?> *</label>
											<input type="text" name="phone" class="form-control"  value="<?= $data->phone ?>"><br>
										</div>
										<div class="col-md-6">
											<label for=""><?= tr("Email") ?> *</label>
											<input type="email" name="email" class="form-control" required value="<?= $data->email ?>"><br>
										</div>
										
									</div>
									
									
									
									
									
									
									<div <?= $data->id==1?'style="display:none"':'' ?>>
										<label for=""><?= tr("Role") ?> *</label>
										<select name="role_id" class="form-control">
											<?php foreach ($roles as $key => $role): ?>
											<option <?= $data->role_id==$role->id?'selected':'' ?> value="<?= $role->id ?>"><?= tr($role->name) ?></option>
											<?php endforeach ?>
										</select>
									</div>


							
									<br>
									
									<button class="btn btn-primary" name="update_admin"><?= tr("Save") ?></button>
									
								</form>
							</div>
							 <div class="tab-pane fade  " id="stacked-left-pill2">
								<form method="post" autocomplete="off" action="<?= base_url("users/password/".$data->id) ?>" class="ajax">
									
									
									<label for=""><?= tr("Password") ?></label>
									<input autocomplete="off"  type="password" class="form-control" name="password" dir="auto" ><br>
									<label for=""><?= tr("Password again") ?></label>
									<input autocomplete="off"  type="password" class="form-control" name="cpassword" dir="auto" ><br>
									
									<button class="btn btn-primary" name="update_password"><?= tr("Save") ?></button>
									
								</form>
							</div> 
							
						</div>
					</div>
					
				</div>
			</div>
		</div>
		
	</div>
</div>
<?= $this->endSection() ?>