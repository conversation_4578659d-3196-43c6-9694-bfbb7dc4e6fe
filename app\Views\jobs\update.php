<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<div class="d-flex justify-content-between mb-1 mt-3">
    <h3><?= tr("Edit Job") ?> - <?= esc($job->name) ?></h3>
    <div>
        <a class="btn btn-danger after-confirm" href="<?= base_url("jobs/delete/$job->id") ?>">
                <i class="fas fa-trash"></i> <?= tr("Delete Job") ?>
        </a>
    </div>
</div>

<!-- Tab Navigation -->
<ul class="nav nav-pills mb-3" id="jobEditTabs">
    <li class="nav-item">
        <a class="nav-link active" href="<?= base_url("jobs/edit/{$job->id}") ?>">
            <i class="fas fa-edit"></i> <?= tr("Job Details") ?>
        </a>
    </li>
    <li class="nav-item">
        <a class="nav-link" href="<?= base_url("jobs/edit/{$job->id}/interview-questions") ?>">
            <i class="fas fa-question-circle"></i> <?= tr("Interview Questions") ?>
        </a>
    </li>
</ul>

<form class="ajax" method="post" action="<?= base_url("jobs/update") ?>" onsubmit="selectAllOptions('selectedUsers')">
    <?= csrf_field() ?>
    <input type="hidden" name="id" value="<?= $job->id ?>">
    
    <div class="card shadow border-0">
        <div class="card-header">
            <h5 class="text-primary mb-0"><?= tr("Job Information") ?></h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label required><?= tr("job_name") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                            </div>
                            <input type="text" class="form-control" name="name" value="<?= set_value('name', $job->name) ?>" required>
                        </div>
                        <?= validation_show_error('name') ?>
                    </div>

                    <div class="form-group">
                        <label required><?= tr("job_end_date") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                            </div>
                            <input type="date" class="form-control" name="end_date" value="<?= set_value('end_date', $job->end_date) ?>" required>
                        </div>
                        <?= validation_show_error('end_date') ?>
                    </div>

                    <div class="form-group">
                        <label><?= tr("job_count") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-users"></i></span>
                            </div>
                            <input type="number" class="form-control" name="number" value="<?= set_value('number', $job->number) ?>" min="1">
                        </div>
                        <?= validation_show_error('number') ?>
                    </div>

                    <div class="form-group">
                        <label><?= tr("job_grade") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-graduation-cap"></i></span>
                            </div>
                            <input type="text" class="form-control" name="grade" value="<?= set_value('grade', $job->grade) ?>">
                        </div>
                        <?= validation_show_error('grade') ?>
                    </div>

                    <div class="form-group">
                        <label><?= tr("job_gender") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-venus-mars"></i></span>
                            </div>
                            <select class="form-control" name="gender">
                                <option value="Both" <?= set_select('gender', 'Both', $job->gender == 'Both') ?>><?= tr("Both") ?></option>
                                <option value="Male" <?= set_select('gender', 'Male', $job->gender == 'Male') ?>><?= tr("Male") ?></option>
                                <option value="Female" <?= set_select('gender', 'Female', $job->gender == 'Female') ?>><?= tr("Female") ?></option>
                            </select>
                        </div>
                        <?= validation_show_error('gender') ?>
                    </div>

                    <div class="form-group">
                        <label><?= tr("Nationality") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-flag"></i></span>
                            </div>
                            <input type="text" class="form-control" name="country" value="<?= set_value('country', $job->country) ?>">
                        </div>
                        <?= validation_show_error('country') ?>
                    </div>

                    <div class="form-group">
                        <label><?= tr("job_location") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                            </div>
                            <input type="text" class="form-control" name="location" value="<?= set_value('location', $job->location) ?>">
                        </div>
                        <?= validation_show_error('location') ?>
                    </div>

                    <div class="form-group">
                        <label required><?= tr("Status") ?></label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-toggle-on"></i></span>
                            </div>
                            <select class="form-control" name="status" required>
                                <?php foreach ($job_statuses as $key => $status): ?>
                                    <option value="<?= $status ?>" <?= set_select('status', $status, $job->status == $status) ?>>
                                        <?= tr(ucfirst($status)) ?>
                                    </option>
                                <?php endforeach ?>
                            </select>
                        </div>
                        <small class="form-text text-muted">
                            <strong><?= tr("Open") ?>:</strong> <?= tr("Visible to all users and can be applied to") ?><br>
                            <strong><?= tr("Closed") ?>:</strong> <?= tr("Visible only to administrators for review") ?><br>
                            <strong><?= tr("Archived") ?>:</strong> <?= tr("No longer active, kept for historical purposes") ?>
                        </small>
                        <?= validation_show_error('status') ?>
                    </div>
                </div>

                <div class="col-md-8">
                    <div class="form-group">
                        <label required><?= tr("job_description") ?></label>
                        <textarea class="form-control summernote" id="job-description-edit" name="description" rows="8" required><?= set_value('description', $job->description) ?></textarea>
                        <?= validation_show_error('description') ?>
                    </div>

                    <div class="mt-3">
                        <h5 class="text-primary"><?= tr("Interviewers") ?></h5>
                        <div class="row justify-content-between align-items-center">
                            <div class="col-md-5">
                                <label><?= tr("Available") ?></label>
                                <select id="availableUsers" class="form-control" multiple style="height:120px;">
                                    <?php 
                                    $selectedInterviewers = $job->interviewers->pluck('user_id')->toArray();
                                    foreach ($users as $id => $name): 
                                        if (!in_array($id, $selectedInterviewers)):
                                    ?>
                                        <option value="<?= $id ?>"><?= $name ?></option>
                                    <?php 
                                        endif;
                                    endforeach; 
                                    ?>
                                </select>
                            </div>

                            <div class="col-md-2 text-center">
                                <button type="button" class="btn " onclick="moveUsers('availableUsers', 'selectedUsers')">
                                    <i class="fas fa-arrow-left"></i>
                                </button>
                                <br><br>
                                <button type="button" class="btn " onclick="moveUsers('selectedUsers', 'availableUsers')">
                                    <i class="fas fa-arrow-right"></i>
                                </button>
                            </div>

                            <div class="col-md-5">
                                <label><?= tr("Selected") ?></label>
                                <select id="selectedUsers" name="user_id[]" class="form-control" multiple style="height:120px;">
                                    <?php foreach ($job->interviewers as $interviewer): ?>
                                        <option value="<?= $interviewer->user_id ?>" selected>
                                            <?= $users[$interviewer->user_id] ?? "User #{$interviewer->user_id}" ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6" id="edit-test-group">
                            <h5 class="text-primary"><?= tr("Test groups") ?></h5>
                            <table class="table table-sm table-borderless">
                                <?php foreach ($job->TestGroups as $group): ?>
                                    <tr>
                                        <td class="px-0">
                                            <input type="text" name="test_group_name[]" class="form-control" value="<?= esc($group->name) ?>">
                                            <input type="hidden" name="test_group_id[]" value="<?= $group->id ?>">
                                        </td>
                                        <td class="px-0">
                                            <input type="number" min="0" value="<?= $group->application_limit ?>" name="test_group_application_limit[]" class="form-control">
                                        </td>
                                        <td>
                                            <button class="btn btn-link text-danger delete-btn" type="button">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </table>
                            <button type="button" class="btn btn-link add-btn">
                                <i class="fa fa-plus"></i> <?= tr("Add Test Group") ?>
                            </button>
                            <input type="hidden" name="deleted_test_group" id="deleted_test_group">
                        </div>

                        <div class="col-md-6" id="edit-interview-group">
                            <h5 class="text-primary"><?= tr("Interview groups") ?></h5>
                            <table class="table table-sm table-borderless">
                                <?php foreach ($job->InterviewGroups as $group): ?>
                                    <tr>
                                        <td class="px-0">
                                            <input type="text" name="interview_group_name[]" class="form-control" value="<?= esc($group->name) ?>">
                                            <input type="hidden" name="interview_group_id[]" value="<?= $group->id ?>">
                                        </td>
                                        <td class="px-0">
                                            <input type="number" min="0" value="<?= $group->application_limit ?>" name="interview_group_application_limit[]" class="form-control">
                                        </td>
                                        <td>
                                            <button class="btn btn-link text-danger delete-btn" type="button">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </table>
                            <button type="button" class="btn btn-link add-btn">
                                <i class="fa fa-plus"></i> <?= tr("Add Interview Group") ?>
                            </button>
                            <input type="hidden" name="deleted_interview_group" id="deleted_interview_group">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="">
             
                <div>
                 
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> <?= tr("Save") ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Delete Confirmation Modal -->
<form method="post" action="<?= base_url("jobs/delete") ?>" class="ajax">
    <?= csrf_field() ?>
    <div class="modal fade" id="delete-modal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger"><?= tr("Delete Job") ?></h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <h5 class="mt-3"><?= tr("Are you sure about this action?") ?></h5>
                        <p class="text-muted"><?= tr("This action cannot be undone. All related data will be permanently deleted.") ?></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times"></i> <?= tr("Cancel") ?>
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> <?= tr("Yes, Delete") ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" name="id" value="<?= $job->id ?>">
</form>

<?= $this->endSection() ?>

<?= $this->section("script") ?>
<script type="text/javascript">
    $(document).ready(function() {
        // Initialize Summernote rich text editor for job description
        $('#job-description-edit').summernote({
            height: 300,
            minHeight: 200,
            maxHeight: 500,
            placeholder: '<?= tr("Enter job description with rich formatting...") ?>',
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['fontname', ['fontname']],
                ['fontsize', ['fontsize']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['insert', ['link', 'picture', 'hr']],
                ['view', ['fullscreen', 'codeview', 'help']]
            ],
            callbacks: {
                onInit: function() {
                    console.log('Summernote initialized for job description edit');
                },
                onChange: function(contents, $editable) {
                    // Trigger validation on content change
                    $('#job-description-edit').trigger('input');
                }
            }
        });

        // Handle form submission - ensure Summernote content is synced
        $('form.ajax').on('submit', function(e) {
            // Sync Summernote content to textarea before submission
            $('#job-description-edit').summernote('code', $('#job-description-edit').summernote('code'));
        });

        // Test group management
        $('#edit-test-group').on('click', '.delete-btn', function() {
            var $row = $(this).closest('tr');
            var groupId = $row.find('input[type="hidden"][name="test_group_id[]"]').val();
            if (groupId !== "0") {
                var $deletedInput = $('#deleted_test_group');
                var deletedIds = $deletedInput.val();
                deletedIds = deletedIds ? deletedIds + ',' + groupId : groupId;
                $deletedInput.val(deletedIds);
            }
            $row.remove();
        });

        $('#edit-test-group').on('click', '.add-btn', function() {
            var newRow = `
                <tr>
                    <td class="px-0">
                        <input type="text" name="test_group_name[]" class="form-control" placeholder="<?= tr("Group Name") ?>">
                        <input type="hidden" name="test_group_id[]" value="0">
                    </td>
                    <td class="px-0">
                        <input type="number" min="0" value="10" name="test_group_application_limit[]" class="form-control">
                    </td>
                    <td>
                        <button class="btn btn-link text-danger delete-btn" type="button">
                            <i class="fa fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
            $('#edit-test-group table').append(newRow);
        });

        // Interview group management
        $('#edit-interview-group').on('click', '.delete-btn', function() {
            var $row = $(this).closest('tr');
            var groupId = $row.find('input[type="hidden"][name="interview_group_id[]"]').val();
            if (groupId !== "0") {
                var $deletedInput = $('#deleted_interview_group');
                var deletedIds = $deletedInput.val();
                deletedIds = deletedIds ? deletedIds + ',' + groupId : groupId;
                $deletedInput.val(deletedIds);
            }
            $row.remove();
        });

        $('#edit-interview-group').on('click', '.add-btn', function() {
            var newRow = `
                <tr>
                    <td class="px-0">
                        <input type="text" name="interview_group_name[]" class="form-control" placeholder="<?= tr("Group Name") ?>">
                        <input type="hidden" name="interview_group_id[]" value="0">
                    </td>
                    <td class="px-0">
                        <input type="number" min="0" value="10" name="interview_group_application_limit[]" class="form-control">
                    </td>
                    <td>
                        <button class="btn btn-link text-danger delete-btn" type="button">
                            <i class="fa fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
            $('#edit-interview-group table').append(newRow);
        });

        // Handle successful form submission
        $(document).on('submit_complete', function(event, data) {
            if (data.success && data.action === 'redirect') {
                window.location.href = data.url;
            }
        });
    });

    function moveUsers(sourceId, destinationId) {
        var source = document.getElementById(sourceId);
        var destination = document.getElementById(destinationId);
        for (var i = 0; i < source.options.length; i++) {
            if (source.options[i].selected) {
                var option = source.options[i];
                destination.add(option.cloneNode(true));
                source.remove(i);
                i--;
            }
        }
    }

    function selectAllOptions(selectId) {
        var select = document.getElementById(selectId);
        for (var i = 0; i < select.options.length; i++) {
            select.options[i].selected = true;
        }
    }
</script>
<?= $this->endSection() ?>
