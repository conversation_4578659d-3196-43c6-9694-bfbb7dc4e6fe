<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProfileExperience extends Model
{
    protected $table = 'profile_experiences';

    protected $fillable = [
        'profile_id',
        'job_name',
        'location',
        'start_date',
        'end_date',
        'file_id',
        'updated_by',
        'is_current'
    ];

    public function profile()
    {
        return $this->belongsTo(Profile::class, 'profile_id');
    }
}
