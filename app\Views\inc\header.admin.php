<form id="logout-form" method="POST" style="display: none;">
    <input type="hidden" name="logout" value="1">
</form>
<div class="navbar navbar-expand-md navbar-dark  bg-transparent shadow  border-0 rounded-0 m-0 shadow-0">
    <!-- Header with logos -->
    <div class="navbar-header navbar-dark d-none d-md-flex align-items-md-center bg-transparent border-0 rounded-0 m-0">

        <div class="navbar-brand navbar-brand-md  m-0 pb-0">

            <div class="d-flex align-items-center">

            </div>
        </div>

        <div class="navbar-brand navbar-brand-xs">
           
        </div>

    </div>

    <!-- /header with logos -->
    <!-- Mobile controls -->
    <div class="d-flex flex-1 d-md-none">
        <div class="navbar-brand mr-auto py-1 align-items-center">
            <a href="<?= base_url() ?>" class="d-flex text-light align-items-center">
                <img src="<?= base_url("assets/images/logo.png") ?>" class="mx-auto" style="height:50px;" alt="">
                <span style="font-size: 0.91rem;"><?= get_option("app_name") ?></span>
            </a>
        </div>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-mobile">
            <i class="fas fa-angle-down"></i>
        </button>
        <button class="navbar-toggler sidebar-mobile-main-toggle" type="button">
            <i class="icon-paragraph-justify3"></i>
        </button>


    </div>
    <!-- /mobile controls -->
    <!-- Navbar content -->
    <div class="collapse navbar-collapse d-md-flex " id="navbar-mobile">
        <ul class="navbar-nav">
            <!-- <li class="nav-item">
                <a href="#" class="navbar-nav-link sidebar-control sidebar-main-toggle d-none d-md-block ">
                    <i class="icon-paragraph-justify3"></i>
                </a>
            </li> -->


        </ul>
        <!-- Global Search Form -->

        <!-- /Global Search Form -->
        <div class="collapse navbar-collapse d-md-flex " id="navbar-mobile">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a href="#" class="navbar-nav-link sidebar-control sidebar-main-toggle d-none d-md-block ">
                        <i class="icon-paragraph-justify3"></i>
                    </a>
                </li>


            </ul>
           

        </div>
        <ul class="navbar-nav ml-auto">




            <?php if (isMobile()): ?>
            <li class="nav-item ">
                <a href="<?= base_url("users/profile") ?>" class="navbar-nav-link ">
                    <i class="far fa-user-cog mr-2"></i> <?= tr("My profile") ?>
                </a>
            </li>

            <li class="nav-item ">
                <a href="<?= base_url("auth/logout") ?>" class="navbar-nav-link ">
                    <i class="icon-switch2 mr-2"></i> <?= tr("Logout") ?>
                </a>
            </li>


            <?php else: ?>
            <li class="nav-item dropdown dropdown-user">
                <a href="#" class="navbar-nav-link d-flex align-items-center dropdown-toggle" data-toggle="dropdown">
                    <i class="fas fa-user px-2"></i>
                    <span><?= esc(auth()->name) ?></span>
                </a>
                <div class="dropdown-menu dropdown-menu-right">

                    <a href="<?= base_url("users/profile") ?>" class="dropdown-item"><i class="far fa-user-cog"></i>
                        <?= tr("My profile") ?></a>

                    <div class="dropdown-divider"></div>

                    <a href="<?= base_url("auth/logout") ?>" class="dropdown-item"><i class="icon-switch2"></i>
                        <?= tr("Logout") ?></a>
                </div>
            </li>
            <?php endif ?>
        </ul>
    </div>
    <!-- /navbar content -->

</div>
<div class="page-content   px-0 mx-0 py-2">
    <?= view("inc/navigation") ?>

    <div class="content-wrapper rounded mr-2 bg-light ">
        <!-- Page header -->
        <div class="page-header print-hide  py-2 my-1">
            <div class="page-header-content header-elements-inline">
                <?php if (isset($nav)): ?>
                <?php if (isset($nav['breadcrumb'])): ?>
                <div class="page-title py-0 d-flex ">
                    <h4>


                        <?php $n=1; foreach ($nav['breadcrumb'] as $key => $value): ?>
                        <?php if ($n!=count($nav['breadcrumb'])): ?>
                        <?php //if (1): ?>
                        <a href="<?= $value ?>" class="text-primary"><span class="font-weight-semibold"
                                style="font-size: 1.2rem;"><?= esc($key) ?></span></a> /
                        <?php else: ?>
                        <span class="font-weight-semibold text-muted"><?= esc($key) ?></span>
                        <?php endif ?>


                        <?php $n++; endforeach ?>
                    </h4>
                    <a href="#" class="header-elements-toggle text-default d-none"><i class="icon-more"></i></a>
                </div>
                <?php endif ?>






                <div class="header-elements d-none text-center text-sm-left mb-3 mb-sm-0">

                    <?php if (isset($nav['date_filter'])): ?>

                    <button type="buttom" data-toggle="modal" data-target="#filter-modal"
                        class="btn mx-1 bg-primary t ">
                        <b><i class="far fa-calendar-alt"></i></b>

                        <span class="mx-4"><?= _d(session()->get("from_date")) ?> -
                            <?= _d(session()->get("to_date")) ?></span>
                    </button>
                    <div class="modal" id="filter-modal" tabindex="-1" role="dialog"
                        aria-labelledby="filter-modal-title" aria-hidden="true" data-backdrop="static"
                        data-keyboard="false">
                        <div class="modal-dialog modal-dialog-centered" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h3 class="modal-title" id="filter-modal-title"><i class="far fa-calendar-alt"></i>
                                        <?= tr("Filter") ?></h3>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">

                                    <div class="row justify-content-center">

                                        <div class="col-6 py-1">
                                            <button class="btn-block text-center border btn btn-light"
                                                onclick="$('#from_date').val('<?= date("Y-m-d") ?>');$('#to_date').val('<?= date("Y-m-d") ?>');$('#filter-form').submit();"><?= tr("Today") ?></button>
                                        </div>
                                        <div class="col-6 py-1">
                                            <button class="btn-block text-center border btn btn-light"
                                                onclick="$('#from_date').val('<?= date("Y-m-d",strtotime("-1 day")) ?>',);$('#to_date').val('<?= date("Y-m-d",strtotime("-1 day")) ?>');$('#filter-form').submit();"><?= tr("Yesterday") ?></button>
                                        </div>
                                        <div class="col-6 py-1">
                                            <button class="btn-block text-center border btn btn-light" onclick="
                      $('#from_date').val('<?= date("Y-m-d",strtotime("-1 week")) ?>');
                      $('#to_date').val('<?= date("Y-m-d") ?>');
                      $('#filter-form').submit();"><?= tr("Last Week") ?></button>
                                        </div>
                                        <div class="col-6 py-1">
                                            <button class="btn-block text-center border btn btn-light"
                                                onclick="$('#from_date').val('<?= date("Y-m-01") ?>');$('#to_date').val('<?= date("Y-m-t") ?>');$('#filter-form').submit();"><?= tr("This Month") ?></button>
                                        </div>
                                        <div class="col-6 py-1">
                                            <button class="btn-block text-center border btn btn-light"
                                                onclick="$('#from_date').val('<?= date("Y-m-01",strtotime("-1 month")) ?>');$('#to_date').val('<?= date("Y-m-t",strtotime("-1 month")) ?>');$('#filter-form').submit();"><?= tr("Last Month") ?></button>
                                        </div>
                                        <div class="col-6 py-1">
                                            <button class="btn-block text-center border btn btn-light"
                                                onclick="$('#from_date').val('<?= date("Y-01-01") ?>');$('#to_date').val('<?= date("Y-12-t") ?>');$('#filter-form').submit();"><?= tr("This Year") ?></button>
                                        </div>
                                        <div class="col-6 py-1">
                                            <button class="btn-block text-center border btn btn-light"
                                                onclick="$('#from_date').val('<?= date("Y-01-01",strtotime("-1 year")) ?>');$('#to_date').val('<?= date("Y-12-t",strtotime("-1 year")) ?>');$('#filter-form').submit();"><?= tr("Last Year") ?></button>
                                        </div>

                                    </div>
                                    <div class="row">
                                        <div class="col-6 py-1">
                                            <button class="btn btn-light btn-block"
                                                onclick="$('#from_date').val('<?= date("Y-01-01") ?>');$('#to_date').val('<?= date("Y-03-31") ?>');$('#filter-form').submit();"><?= tr("Q1") ?></button>
                                        </div>
                                        <div class="col-6 py-1">
                                            <button class="btn btn-light btn-block"
                                                onclick="$('#from_date').val('<?= date("Y-04-01") ?>');$('#to_date').val('<?= date("Y-06-30") ?>');$('#filter-form').submit();"><?= tr("Q2") ?></button>
                                        </div>
                                        <div class="col-6 py-1">
                                            <button class="btn btn-light btn-block"
                                                onclick="$('#from_date').val('<?= date("Y-07-01") ?>');$('#to_date').val('<?= date("Y-09-30") ?>');$('#filter-form').submit();"><?= tr("Q3") ?></button>
                                        </div>
                                        <div class="col-6 py-1">
                                            <button class="btn btn-light btn-block"
                                                onclick="$('#from_date').val('<?= date("Y-10-01") ?>');$('#to_date').val('<?= date("Y-12-31") ?>');$('#filter-form').submit();"><?= tr("Q4") ?></button>
                                        </div>
                                    </div>

                                    <hr>
                                    <?= tr("Date") ?>
                                    <form action="<?= url()->get() ?>" method="get" class="form-row mb-2 no_csrf"
                                        id="filter-form">
                                        <div class="col">
                                            <label for=""><?= tr("From") ?></label>
                                            <input name="from_date" id="from_date" type="date"
                                                class="form-control text-center"
                                                value="<?= session()->get("from_date") ?>">
                                        </div>
                                        <div class="col">
                                            <label for=""><?= tr("To") ?></label>
                                            <input name="to_date" id="to_date" type="date"
                                                class="form-control text-center"
                                                value="<?= session()->get("to_date") ?>">
                                        </div>
                                        <?php if (isset($_GET['i'])): ?>
                                        <input name="i" type="hidden" value="<?= $_GET['i'] ?>">
                                        <?php endif ?>
                                        <?php if (isset($_GET['v'])): ?>
                                        <input name="v" type="hidden" value="<?= $_GET['v'] ?>">
                                        <?php endif ?>

                                    </form>

                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-danger"
                                        data-dismiss="modal"><?= tr("Cancel") ?></button>

                                    <button onclick="$('#filter-form').submit();" type="button"
                                        class="btn btn-primary"><?= tr("Apply") ?></button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php endif ?>
                    <?php if (isset($nav['reload']) && $nav['reload']==true): ?>

                    <button type="button" onclick="window.location.replace('<?= url()->get() ?>')" class="btn ">
                        <i class="fas fa-sync-alt"></i>

                    </button>

                    <?php endif ?>
                    <?php if (isset($nav['back'])): ?>
                    <a href="<?= $nav['back'] ?>" class="btn  "><i
                            class="fas fa-arrow-<?= (get_local()=="ar")?'left':'right' ?>"></i></a>
                    <?php endif ?>
                    <?php if (isset($nav['close'])||isset($_GET['popup'])): ?>

                    <button onclick="window.close()" type="button"
                        class="btn bg-danger mx-1 btn-labeled btn-labeled-right rounded-round"><b><i
                                class="fas fa-times"></i></b><?= tr("Close") ?></button>

                    <?php endif ?>




                </div>

                <?php endif ?>
            </div>
        </div>
        <!-- /page header -->