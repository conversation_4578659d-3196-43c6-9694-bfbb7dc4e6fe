<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Application extends Model
{
    use SoftDeletes;

    protected $table = 'applications';

    protected $fillable = [
        'name',
        'gender',
        'birth',
        'card_id',
        'job_id',
        'email',
        'reg_id',
        'city_id',
        'name_en',
        'address',
        'phone',
        'phone_2',
        'updated_by',
        'card_id_file',
        'mol_file',
        'cv_file',
        'image_file',
        'status',
        'is_job_seeker',
    ];

    public function experiences()
    {
        return $this->hasMany(ApplicationExperience::class, 'application_id');
    }

    public function qualifications()
    {
        return $this->hasMany(ApplicationQualification::class, 'application_id');
    }

    public function others()
    {
        return $this->hasMany(ApplicationCert::class, 'application_id');
    }

    public function interview_marks()
    {
        return $this->hasMany(ApplicationInterviewMark::class, 'application_id');
    }

    public function job()
    {
        return $this->belongsTo(Job::class, 'job_id');
    }

    public function interview_group(){
        return $this->belongsTo(JobInterviewGroup::class, 'interview_group_id');
    }

    public function test_group(){
        return $this->belongsTo(JobTestGroup::class, 'test_group_id');
    }


    protected function email(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => _dr($value),
            set: fn (string $value) => _cr($value),
        );
    }

    protected function cardId(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => _dr($value),
            set: fn (string $value) => _cr($value),
        );
    }

    protected function profile(){
        return $this->belongsTo(Profile::class, 'profile_id');
    }
}
