# Database Overview

## System Architecture

The Career Application System uses a **MySQL database** with **Eloquent ORM** for data management. The system is designed to handle job applications, user management, and administrative workflows.

## Database Information
- **Database Name**: `app_office`
- **Engine**: MySQL/MariaDB 10.4.32
- **Character Set**: UTF-8 (utf8_general_ci)
- **ORM**: Laravel Eloquent (integrated with CodeIgniter 4)

## Core Entities

### Primary Entities
1. **Applications** - Job application records
2. **Jobs** - Available job positions
3. **Users** - System users (administrators, interviewers)
4. **Cities/Regions** - Geographic data for applicants

### Supporting Entities
1. **Application Qualifications** - Educational background
2. **Application Experiences** - Work experience records
3. **Application Certificates** - Additional certifications
4. **Interview Groups** - Interview scheduling
5. **Test Groups** - Testing arrangements

### System Entities
1. **Sessions** - User session management
2. **Email Queue** - Email delivery system
3. **OTP** - One-time password verification
4. **Rate Limiting** - API protection
5. **Options** - System configuration

## Data Flow

### Application Process
1. **Registration** → Applicant creates session
2. **Application** → Submits job application with documents
3. **Verification** → Phone/email verification via OTP
4. **Processing** → Admin reviews and schedules tests/interviews
5. **Evaluation** → Scoring and final decision

### Security Features
- **Data Encryption**: Sensitive fields (email, phone, card_id) are encrypted
- **Soft Deletes**: Records are marked as deleted, not physically removed
- **Session Management**: Database-driven session storage
- **Rate Limiting**: IP-based request throttling

## Performance Considerations
- **Indexes**: Primary keys and foreign key relationships
- **Timestamps**: Automatic created_at/updated_at tracking
- **Pagination**: Large datasets handled with pagination
- **Caching**: Session and configuration caching

## Backup Strategy
- **SQL Dump**: Regular exports via phpMyAdmin
- **File Storage**: Uploaded documents in `writable/uploads/`
- **Environment**: Configuration stored in `.env` file
