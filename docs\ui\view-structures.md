# View Structures & Component Organization

## Overview
This document details the specific view structures used in the Career Application System, including job views, application views, and their component organization patterns.

## Job Management Views

### Jobs Index View
**File**: `app/Views/jobs/index.php`

#### Layout Structure
```
┌─────────────────────────────────────────────────────────────┐
│ Page Header: Title + Action Buttons                        │
├─────────────────────────────────────────────────────────────┤
│ Data Table Card                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Table: Jobs List with ID, Name, Applications, Status   │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Modal: New Job Form (XL Size)                              │
│ Modal: Edit Job Form (XL Size)                             │
│ Modal: Delete Confirmation (MD Size)                       │
└─────────────────────────────────────────────────────────────┘
```

#### Header Actions Pattern
```html
<div class="d-flex justify-content-between mb-1 mt-3">
    <h3><?= tr("Jobs") ?></h3>
    <div>
        <a class="btn btn-secondary btn-sm" href="<?= base_url("jobs/import_applications") ?>">
            <i class="fas fa-plus"></i> <?= tr("Import applications") ?>
        </a>
        <button class="btn btn-secondary btn-sm" data-toggle="modal" data-target="#import-modal">
            <i class="fas fa-plus"></i> <?= tr("Import jobs") ?>
        </button>
        <button class="btn btn-primary btn-sm" data-toggle="modal" data-target="#new-modal">
            <i class="fas fa-plus"></i> <?= tr("Add new") ?>
        </button>
    </div>
</div>
```

#### Job Form Modal Structure
```html
<div class="modal fade" id="new-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?= tr("New job") ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <!-- Job Details Form -->
                        <label><?= tr("job_name") ?></label>
                        <input type="text" class="form-control" name="name">
                        
                        <label><?= tr("job_end_date") ?></label>
                        <input type="date" class="form-control" name="end_date">
                        
                        <!-- More job fields -->
                    </div>
                    <div class="col-md-8">
                        <!-- Job Description -->
                        <label><?= tr("job_description") ?></label>
                        <textarea class="form-control" name="description" rows="10"></textarea>
                        
                        <!-- Interviewer Selection -->
                        <h5><?= tr("Interviewers") ?></h5>
                        <div class="row justify-content-between align-items-center">
                            <div class="col-md-5">
                                <label><?= tr("Available") ?></label>
                                <select id="availableUsers-new" class="form-control" multiple style="height:150px;">
                                    <!-- Available users -->
                                </select>
                            </div>
                            <div class="col-md-2 text-center">
                                <button type="button" class="btn" onclick="moveUsers('availableUsers-new', 'selectedUsers-new')">
                                    <h2>+</h2>
                                </button>
                                <br>
                                <button type="button" class="btn text-danger" onclick="moveUsers('selectedUsers-new', 'availableUsers-new')">
                                    <h2>-</h2>
                                </button>
                            </div>
                            <div class="col-md-5">
                                <label><?= tr("Selected") ?></label>
                                <select id="selectedUsers-new" name="user_id[]" class="form-control" multiple style="height:150px;">
                                    <!-- Selected users -->
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><?= tr("Close") ?></button>
                <button type="submit" class="btn btn-primary"><?= tr("Submit") ?></button>
            </div>
        </div>
    </div>
</div>
```

#### Dynamic Group Management
Edit modal includes dynamic test and interview group management:

```html
<div class="row">
    <div class="col-md-6" id="edit-test-group">
        <h5><?= tr("Test groups") ?></h5>
        <table class="table table-sm table-borderless">
            <!-- Dynamic rows for test groups -->
        </table>
        <button type="button" class="btn btn-link add-btn">
            <i class="fa fa-plus"></i>
        </button>
    </div>
    <div class="col-md-6" id="edit-interview-group">
        <h5><?= tr("Interview groups") ?></h5>
        <table class="table table-sm table-borderless">
            <!-- Dynamic rows for interview groups -->
        </table>
        <button type="button" class="btn btn-link add-btn">
            <i class="fa fa-plus"></i>
        </button>
    </div>
</div>
```

## Application Management Views

### Applications Index View
**File**: `app/Views/applications/index.php`

#### Layout Structure
```
┌─────────────────────────────────────────────────────────────┐
│ Page Header: Title                                          │
├─────────────────────────────────────────────────────────────┤
│ Job Selection Dropdown + SMS Button                        │
├─────────────────────────────────────────────────────────────┤
│ Status Tabs (Dynamic Count Badges)                         │
├─────────────────────────────────────────────────────────────┤
│ Group Filter Buttons (Test/Interview Groups)               │
├─────────────────────────────────────────────────────────────┤
│ Filter Controls (Gender, etc.)                             │
├─────────────────────────────────────────────────────────────┤
│ Data Table Card                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Table: Applications with ID, Name, Gender, Age/Mark    │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Modal: SMS Sending                                          │
└─────────────────────────────────────────────────────────────┘
```

#### Job Selection Component
```html
<div class="dropdown">
    <button class="btn btn-primary text-start" type="button" style="min-width: 250px;" 
            id="dropdownMenuButton" data-toggle="dropdown">
        <div class="row align-items-center" style="width: 100%;">
            <div class="col-8">
                <h4><?= esc($job->name) ?></h4>
                <p><small class="text-muted">GSC000<?= $job->id ?></small></p>
            </div>
            <div class="col-4 text-end"><i class="fas fa-angle-down"></i></div>
        </div>
    </button>
    <div class="dropdown-menu border-0 shadow rounded-0 py-0" 
         style="min-width: 350px; max-height: 600px; overflow-y: scroll;">
        <?php foreach ($jobs as $key => $job): ?>
            <a class="dropdown-item <?= ($selected_job == $job->id) ? 'bg-primary' : '' ?>" 
               href="<?= base_url("applications/".$job->id) ?>">
                <div class="row align-items-center" style="width: 100%;">
                    <div class="col-8">
                        <h4><?= $job->name ?></h4>
                        <p><small class="text-muted">GSC000<?= $job->id ?></small></p>
                    </div>
                    <div class="col-4 text-end">
                        <span class="badge bg-primary px-2">
                            <?= $job->applications->where("status","!=","Incomplete")->count() ?>
                        </span>
                    </div>
                </div>
            </a>
        <?php endforeach ?>
    </div>
</div>
```

#### Status Tabs Component
**File**: `app/Views/applications/count.php`

```html
<div class="d-flex justify-content-between mb-0">
    <ul class="nav nav-tabs mb-0">
        <?php foreach ($application_statuses as $key => $status): ?>
            <li class="nav-item">
                <a class="nav-link border-0 <?= ($tab == $status) ? 'active text-primary' : '' ?>" 
                   href="<?= url(base_url("applications/$selected_job"))->add("status",$status) ?>">
                    <?= tr($status) ?> 
                    <span class="badge badge-light rounded-pill px-2">
                        <?= $status_count[$status]??0 ?>
                    </span>
                </a>
            </li>
        <?php endforeach ?>
    </ul>
</div>
```

#### Group Filter Buttons
Dynamic filtering for test and interview groups:

```html
<?php if ($tab=="الاختبار"): ?>
    <div class="d-flex justify-content-between">
        <div>
            <a href="<?= url()->add("group",0) ?>" 
               class="btn btn-<?= $selected_group==0?'primary':'secondary' ?> mx-1">
                غير معرف (<?= $default_test_count ?>)
            </a>
            <?php foreach ($test_groups as $key => $tg): ?>
                <a href="<?= url()->add("group",$tg->id) ?>" 
                   class="btn btn-<?= $selected_group==$tg->id?'primary':'secondary' ?> mx-1">
                    <?= $tg->name ?> (<?= $tg->applications()->count() ?>/<?= $tg->application_limit ?>)
                </a>
            <?php endforeach ?>
        </div>
        <div>
            <a href="<?= base_url("applications/test_group/$selected_job") ?>" class="btn btn-link">
                <i class="fas fa-sync"></i> <?= tr("Reorder") ?>
            </a>
        </div>
    </div>
<?php endif ?>
```

## Public Application Form

### Application Form View
**File**: `app/Views/my/form.php`

#### Layout Structure
```
┌─────────────────────────────────────────────────────────────┐
│ Form Card                                                   │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Card Header: Job Title                                  │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ Card Body:                                              │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ Personal Information Section                        │ │ │
│ │ │ - Name (Arabic/English)                             │ │ │
│ │ │ - Phone (with validation)                           │ │ │
│ │ │ - Gender, Card ID, Email, Birth Date               │ │ │
│ │ │ - Region/City Selection                             │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ Document Upload Section                             │ │ │
│ │ │ - Card ID Copy, CV Copy                             │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ Qualifications Table                                │ │ │
│ │ │ - Dynamic table with add/delete functionality      │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ Experiences Table                                   │ │ │
│ │ │ - Dynamic table with add/delete functionality      │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ Other Certificates Table                            │ │ │
│ │ │ - Dynamic table with add/delete functionality      │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ ├─────────────────────────────────────────────────────────┤ │
│ │ Card Footer: Submit Button                              │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Modal: OTP Verification                                     │
└─────────────────────────────────────────────────────────────┘
```

#### Input Group Pattern
Consistent input styling with icons:

```html
<div class="col-md-6">
    <label required><?= tr("Full name Arabic") ?></label>
    <div class="input-group">
        <div class="input-group-prepend">
            <span class="input-group-text"><i class="far fa-user"></i></span>
        </div>
        <input type="text" name="name" class="form-control" value="<?= esc($application->name) ?>">
    </div>
    <br>
</div>
```

#### Phone Validation Component
Special phone input with validation button:

```html
<div class="col-md-5">
    <label required><?= tr("Primary phone") ?></label>
    <div class="input-group">
        <div class="input-group-prepend">
            <span class="input-group-text"><i class="far fa-phone"></i></span>
        </div>
        <input type="tel" name="primary_phone" class="form-control text-center" id="primary_phone-input">
        <div class="input-group-prepend" id="phone-validate-status">
            <button type="button" class="btn btn-danger border-0 rounded-0" onclick="validate_phone()">
                <?= tr("Validate") ?>
            </button>
        </div>
    </div>
    <br>
</div>
```

#### Dynamic Table Pattern
Tables with add/delete functionality:

```html
<div class="row mt-4">
    <div class="col table-responsive">
        <h3 class="text-primary"><?= tr("Qualifications") ?> <span class="text-danger">*</span></h3>
        <table class="table table-sm" id="qualifications-table">
            <thead>
                <tr class="bg-primary">
                    <th class="border-2"><?= tr('Major') ?></th>
                    <th class="border-2"><?= tr('Qualification') ?></th>
                    <th class="border-2"><?= tr('qualification_location') ?></th>
                    <th class="border-2"><?= tr('qualification_year') ?></th>
                    <th class="border-2"><?= tr('GPA') ?></th>
                    <th class="border-2"><?= tr('Attach') ?></th>
                    <th class="text-center border-2"></th>
                </tr>
            </thead>
            <tbody id="qualifications-tbody">
                <!-- Existing qualifications -->
                <?php foreach ($application_qualifications as $key => $q): ?>
                    <tr data-id="<?= _cr($q->id) ?>">
                        <td><?= esc($q->major) ?></td>
                        <td><?= esc($q->qualification) ?></td>
                        <td><?= esc($q->location) ?></td>
                        <td><?= esc($q->end_year) ?></td>
                        <td><?= esc($q->gpa) ?></td>
                        <td class="text-center">
                            <i class="far fa-file text-primary"></i>
                        </td>
                        <td class="d-flex">
                            <button class="btn btn-link btn-sm text-danger delete-qualification-button" 
                                    type="button" data-id="<?= _cr($q->id) ?>" title="<?= tr("Delete") ?>">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                <?php endforeach ?>
            </tbody>
            <tfoot id="qualification-form">
                <tr>
                    <td class="p-0 py-1">
                        <input type="text" name="major" class="form-control" style="font-size: 1rem !important;">
                    </td>
                    <td class="p-0 py-1">
                        <select class="form-control" name="qualification" style="font-size: 1rem !important;">
                            <option><?= tr("Select qualification") ?></option>
                            <?php foreach ($qualifications as $key => $qualification): ?>
                                <option value="<?= $qualification ?>"><?= $qualification ?></option>
                            <?php endforeach ?>
                        </select>
                    </td>
                    <!-- More form fields -->
                    <td class="p-1 text-center" style="min-width: 100px;">
                        <button class="btn btn-primary rounded-0 border-0 btn-sm" type="button" id="qualification-submit">
                            <?= tr("Save") ?> <i class="far fa-save"></i>
                        </button>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
```

## Component Reusability Patterns

### Modal Template
Standard modal structure used across the application:

```html
<div class="modal fade" id="{modal-id}" tabindex="-1" role="dialog">
    <div class="modal-dialog {modal-size}" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{title}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                {content}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <?= tr("Close") ?>
                </button>
                <button type="submit" class="btn btn-primary">
                    <?= tr("Submit") ?>
                </button>
            </div>
        </div>
    </div>
</div>
```

### Data Table Template
Consistent table structure with DataTables integration:

```html
<div class="card shadow border-0">
    <table class="table table-striped dt" id="{table-id}">
        <thead>
            <tr>
                {table-headers}
            </tr>
        </thead>
        <tbody>
            <!-- AJAX populated -->
        </tbody>
    </table>
</div>
```

### Form Section Template
Reusable form section with responsive grid:

```html
<div class="row">
    <div class="col-md-{size}">
        <label {required}><?= tr("{label}") ?></label>
        <div class="input-group">
            <div class="input-group-prepend">
                <span class="input-group-text"><i class="{icon}"></i></span>
            </div>
            <input type="{type}" name="{name}" class="form-control" {attributes}>
        </div>
        <br>
    </div>
</div>
```
