<?php
namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Job extends Model
{
    // Job status constants
    const STATUS_PUBLIC = 'open';
    const STATUS_PRIVATE = 'closed';
    const STATUS_ARCHIVE = 'archived';

    protected $table = 'jobs';
    protected $fillable = ['name', 'description', 'end_date', 'status',"country","gender","number","grade","location","type"];

 
    public function Applications()
    {
        return $this->hasMany(Application::class,'job_id');
    }

    public function Interviewers()
    {
        return $this->hasMany(JobInterviewer::class,'job_id');
    }

    public function test_groups()
    {
        return $this->hasMany(JobTestGroup::class,'job_id');
    }

    public function interview_groups()
    {
        return $this->hasMany(JobInterviewGroup::class,'job_id');
    }

    public function InterviewGroups()
    {
        return $this->hasMany(JobInterviewGroup::class,'job_id');
    }

    public function TestGroups()
    {
        return $this->hasMany(JobTestGroup::class,'job_id');
    }

    public function interviewQuestions()
    {
        return $this->hasMany(JobInterviewQuestion::class, 'job_id');
    }

    // Scope methods for filtering by visibility
    public function scopePublic($query)
    {
        return $query->where('status', self::STATUS_PUBLIC);
    }

    public function scopePrivate($query)
    {
        return $query->where('status', self::STATUS_PRIVATE);
    }

    public function scopeArchive($query)
    {
        return $query->where('status', self::STATUS_ARCHIVE);
    }

    public function scopeVisibleToPublic($query)
    {
        if(get_option("application_status")=="active"){
            return $query->where('status', self::STATUS_PUBLIC)->where("end_date",">=",date("Y-m-d"));
        }else{
            return $query->where("id",0);
        }
    }

    public function scopeVisibleToAdmin($query)
    {
        return $query->whereIn('status', [self::STATUS_PUBLIC, self::STATUS_PRIVATE]);
    }

    // Helper methods
    public function isPublic()
    {
        return $this->status === self::STATUS_PUBLIC;
    }

    public function isPrivate()
    {
        return $this->status === self::STATUS_PRIVATE;
    }

    public function isArchive()
    {
        return $this->status === self::STATUS_ARCHIVE;
    }

    public static function getStatusOptions()
    {
        return [
            self::STATUS_PUBLIC => 'Open',
            self::STATUS_PRIVATE => 'Closed',
            self::STATUS_ARCHIVE => 'Archived'
        ];
    }
}