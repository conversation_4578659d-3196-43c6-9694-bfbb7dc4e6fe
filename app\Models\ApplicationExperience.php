<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ApplicationExperience extends Model
{
    protected $table = 'application_experiences';

    protected $fillable = [
        'application_id',
        'job_name',
        'location',
        'start_date',
        'end_date',
        'file_id',
        'updated_by',
        'is_current'
    ];

    public function application()
    {
        return $this->belongsTo(Application::class, 'application_id');
    }
}
