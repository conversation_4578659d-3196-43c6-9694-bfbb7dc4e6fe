# Form Submission & AJAX Handling

## Overview
The Career Application System uses a sophisticated AJAX-based form submission system with comprehensive validation, error handling, and user feedback. This document covers the complete form submission workflow.

## AJAX Form System

### Form Setup
Forms must include the `ajax` class to enable AJAX submission:

```html
<form class="ajax" action="<?= base_url('applications/update_status/123') ?>" method="POST">
    <?= csrf_field() ?>
    
    <div class="form-group">
        <label for="status"><?= tr("Status") ?></label>
        <select name="status" class="form-control" required>
            <option value="New"><?= tr("New") ?></option>
            <option value="Under Review"><?= tr("Under Review") ?></option>
        </select>
    </div>
    
    <button type="submit" class="btn btn-primary">
        <?= tr("Update Status") ?>
    </button>
</form>
```

### JavaScript Handler (foot.php)
The AJAX form handler is located in `app/Views/inc/foot.php`:

```javascript
$("body").on('submit', 'form.ajax', function(event) {
    event.preventDefault();
    
    var form = $(this);
    var action = form.attr("action") || "";
    var method = form.attr("method") || "post";
    var submit_button = form.find('button[type!=button]').attr('name');
    
    var formData = new FormData(this);
    
    // Add submit button name if exists
    if (submit_button) {
        formData.append(submit_button, 1);
    }
    
    // Mark as AJAX form
    formData.append("ajax_form", 1);
    
    // Handle GET requests
    if (method.toLowerCase() == "get") {
        for(var pair of formData.entries()) {
            action = addParams(action, pair);
        }
        location.href = action;
    } else {
        // Handle POST requests
        var submit_button2 = form.find('button[type!=button]');
        $(submit_button2).prop('disabled', true);
        btntxt = $(submit_button2).text();
        $(submit_button2).empty();
        $(submit_button2).append('<div class="spinner-border spinner-border-sm" role="status"><span class="sr-only">Loading...</span></div>');
        
        $.ajax({
            url: action,
            type: method,
            dataType: 'json',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success: function(data) {
                $(submit_button2).prop('disabled', false);
                $(submit_button2).empty();
                $(submit_button2).text(btntxt);
                
                // Trigger custom event for response handling
                $(document).trigger('submit_complete', data);
            }
        });
    }
});
```

## Validation System

### Server-Side Validation
The application uses a custom validation helper with Rakit Validator:

```php
// In Controller
public function update_status($id) {
    $v = validate([
        tr("Status") => ["status", "required|in:" . implode(",", $this->application_statuses)],
    ]);
    
    if ($v->passes()) {
        // Process the form
        $application = Application::find($id);
        $application->status = input("status");
        $application->save();
        
        return _response([
            "success" => true,
            "message" => [tr("Application updated successfully")],
            "action" => "reload"
        ]);
    }
    
    return _response([
        "success" => false,
        "message" => $v->errors()->all()
    ]);
}
```

### Validation Rules
Common validation rules used in the system:

```php
// Basic validation
$v = validate([
    tr("Name") => ["name", "required|min:2|max:150"],
    tr("Email") => ["email", "required|email|unique:applications,email"],
    tr("Phone") => ["phone_1", "required|min:8|max:8|integer"],
    tr("Birth Date") => ["birth", "required|date"],
    tr("Gender") => ["gender", "required|in:Male,Female"],
]);

// File validation
$v = validate([
    tr("CV") => ["cv_file", "required|uploaded_file:0," . env("storage.max_size") . "," . env("storage.extensions") . "|file_pdf"],
    tr("Card ID") => ["card_id_file", "required|uploaded_file:0,2M,pdf,jpeg,png,jpg"],
]);

// Custom validation rules
$v = validate([
    tr("Region") => ["reg_id", "required|in_table:regions,id"],
    tr("City") => ["city_id", "required|in_table:cities,id"],
    tr("Job") => ["job_id", "required|in_table:jobs,id"],
]);
```

### Custom Validation Rules
The system includes custom validation rules in `app/Helpers/validator_helper.php`:

#### UniqueRule
```php
class UniqueRule extends Rule {
    public function check($value): bool {
        // Check if value exists in database
        // Handles encrypted fields automatically
        // Supports soft deletes
        // Allows exceptions for updates
    }
}

// Usage
"email|unique:applications,email,123" // Exclude ID 123
```

#### InTableRule
```php
class InTableRule extends Rule {
    public function check($value): bool {
        // Verify value exists in specified table/column
        return DB::table($table)->where($column, $value)->count() > 0;
    }
}

// Usage
"reg_id|in_table:regions,id"
```

#### IsPDFRule
```php
class IsPDFRule extends Rule {
    public function check($value): bool {
        // Verify uploaded file is a valid PDF
        $handle = fopen($value['tmp_name'], "r");
        $header = fread($handle, 5);
        fclose($handle);
        return $header == '%PDF-';
    }
}

// Usage
"cv_file|file_pdf"
```

## Response Handling

### Standard Response Format
All AJAX responses follow a consistent format:

```php
function _response($data = []) {
    $responseData = [
        'status' => 200,
        'message' => $data['message'] ?? [],
        'data' => $data['data'] ?? [],
        "success" => $data['success'] ?? true,
        "action" => $data['action'] ?? "",
        "type" => $data['type'] ?? ($data['success'] ?? true) ? "success" : "error"
    ];
    
    return service('response')->setJSON($responseData);
}
```

### Response Actions
The system supports various response actions:

```php
// Reload the page
return _response([
    "success" => true,
    "message" => [tr("Success message")],
    "action" => "reload"
]);

// Redirect to URL
return _response([
    "success" => true,
    "message" => [tr("Success message")],
    "action" => "redirect",
    "url" => base_url("applications")
]);

// Reset form
return _response([
    "success" => true,
    "message" => [tr("Success message")],
    "action" => "reset_form();"
]);

// Custom JavaScript action
return _response([
    "success" => true,
    "message" => [tr("Success message")],
    "action" => "updateTable(); showModal();"
]);
```

## Error Handling

### Validation Errors
When validation fails, errors are returned in a structured format:

```php
// Validation failure response
return _response([
    "success" => false,
    "message" => $v->errors()->all(), // Array of error messages
    "type" => "error"
]);

// Example error response
{
    "status": 200,
    "message": [
        "Name is required",
        "Email must be a valid email address",
        "Phone must be exactly 8 digits"
    ],
    "data": [],
    "success": false,
    "action": "",
    "type": "error"
}
```

### Custom Error Messages
The system supports custom error messages in Arabic and English:

```php
$customMessages = [
    "phone:required" => "يجب إدخال رقم هاتف تابع لمزودي الخدمة بسلطنة عمان",
    "phone:min" => "يجب أن يكون رقم الهاتف مكوناً من 8 أرقام",
    "phone:max" => "يجب أن يكون رقم الهاتف مكوناً من 8 أرقام",
    "email:required" => "يجب تسجيل البريد الإلكتروني",
    "email:email" => "يجب إدخال عنوان بريد إلكتروني صحيح",
    "email:unique" => "هذا البريد الإلكتروني سبق أن تم استخدامه",
    "card_id:unique" => "رقم البطاقة الشخصية سبق أن تم استخدامه"
];

$v = validate([
    tr("Phone") => ["phone", "required|min:8|max:8|integer"],
    tr("Email") => ["email", "required|email|unique:applications,email"]
], $customMessages);
```

### Permission Errors
Access control errors are handled consistently:

```php
public function update_status($id) {
    if (!_can("update-applications")) {
        return _error_code(403); // Returns HTTP 403 Forbidden
    }

    // Continue with processing...
}
```

### Not Found Errors
Resource not found errors:

```php
$application = Application::find($id);
if (!$application) {
    return _response([
        "success" => false,
        "message" => ["Record not found"],
        "type" => "error"
    ]);
}
```

## Complete Form Examples

### Application Status Update Form

**HTML Form:**
```html
<form class="ajax" action="<?= base_url('applications/update_status/' . $application->id) ?>" method="POST">
    <?= csrf_field() ?>

    <div class="form-group">
        <label for="status"><?= tr("Status") ?></label>
        <select name="status" class="form-control" required>
            <?php foreach($application_statuses as $status): ?>
                <option value="<?= $status ?>" <?= $application->status == $status ? 'selected' : '' ?>>
                    <?= tr($status) ?>
                </option>
            <?php endforeach; ?>
        </select>
        <?= validation_show_error('status') ?>
    </div>

    <button type="submit" class="btn btn-primary">
        <i class="fas fa-save"></i> <?= tr("Update Status") ?>
    </button>
</form>
```

**Controller Method:**
```php
public function update_status($id) {
    if (!_can("update-applications")) {
        return _error_code(403);
    }

    $v = validate([
        tr("Status") => ["status", "required|in:" . implode(",", $this->application_statuses)],
    ]);

    if ($v->passes()) {
        $application = Application::find($id);

        if (!$application) {
            return _response([
                "success" => false,
                "message" => ["Application not found"],
            ]);
        }

        Log::new("Application status updated [id:$application->id, From $application->status To " . input("status") . "]");

        $application->status = input("status");
        $application->updated_by = auth()->id;
        $application->save();

        return _response([
            "success" => true,
            "message" => [tr("Application updated successfully")],
            "action" => "reload"
        ]);
    }

    return _response([
        "success" => false,
        "message" => $v->errors()->all()
    ]);
}
```

### File Upload Form

**HTML Form:**
```html
<form class="ajax" action="<?= base_url('applications/upload_cv/' . $application->id) ?>" method="POST" enctype="multipart/form-data">
    <?= csrf_field() ?>

    <div class="form-group">
        <label for="cv_file"><?= tr("CV File") ?></label>
        <input type="file" name="cv_file" class="form-control" accept=".pdf" required>
        <small class="form-text text-muted">
            <?= tr("Maximum file size") ?>: <?= env("storage.max_size") ?>
            <?= tr("Allowed formats") ?>: PDF
        </small>
        <?= validation_show_error('cv_file') ?>
    </div>

    <button type="submit" class="btn btn-success">
        <i class="fas fa-upload"></i> <?= tr("Upload CV") ?>
    </button>
</form>
```

**Controller Method:**
```php
public function upload_cv($id) {
    if (!_can("update-applications")) {
        return _error_code(403);
    }

    $v = validate([
        tr("CV") => ["cv_file", "required|uploaded_file:0," . env("storage.max_size") . "," . env("storage.extensions") . "|file_pdf"],
    ]);

    if ($v->passes()) {
        $application = Application::find($id);

        if (!$application) {
            return _response([
                "success" => false,
                "message" => ["Application not found"],
            ]);
        }

        if (isset($_FILES['cv_file']) && $_FILES['cv_file']['error'] == 0) {
            $storage = storage()->save($_FILES['cv_file']);

            if ($storage->saved()) {
                Log::new("Application CV updated [id:$application->id, From $application->cv_file To " . $storage->id . "]");

                $application->cv_file = $storage->id;
                $application->updated_by = auth()->id;
                $application->save();
            }
        }

        return _response([
            "success" => true,
            "message" => [tr("CV uploaded successfully")],
            "action" => "reload"
        ]);
    }

    return _response([
        "success" => false,
        "message" => $v->errors()->all()
    ]);
}
```

## Helper Functions

### Input Helper
The `input()` function safely retrieves form data:

```php
// Basic usage
$name = input("name", "default_value");
$email = input("email");

// File handling
$file = input("cv_file");
if ($file && $file['error'] === UPLOAD_ERR_OK) {
    // Process file
}

// Array handling
$skills = input("skills", []);
```

### Response Helper
The `_response()` function standardizes AJAX responses:

```php
// Success response
return _response([
    "success" => true,
    "message" => [tr("Operation completed successfully")],
    "data" => $result,
    "action" => "reload"
]);

// Error response
return _response([
    "success" => false,
    "message" => ["Error occurred"],
    "type" => "error"
]);
```

### Validation Helper
The `validate()` function provides comprehensive validation:

```php
$v = validate([
    tr("Field Label") => ["field_name", "validation_rules"],
    tr("Email") => ["email", "required|email|unique:table,column"],
    tr("Phone") => ["phone", "required|min:8|max:8|integer"]
], $customMessages);

if ($v->passes()) {
    // Validation passed
} else {
    // Handle errors: $v->errors()->all()
}
```

### Authentication Helpers
```php
// Check if user is authenticated
if (auth()) {
    $user = auth(); // Returns User model instance
}

// Check permissions
if (_can("update-applications")) {
    // User has permission
}

// Get current user ID
$userId = auth()->id;
```

### Encryption Helpers
For sensitive data handling:

```php
// Encrypt data
$encrypted = _cr($sensitiveData);

// Decrypt data
$decrypted = _dr($encryptedData);

// Used automatically in models for sensitive fields
```

## Frontend Message Handling

### Success Messages
Success messages are typically displayed using notifications:

```javascript
$(document).on('submit_complete', function(event, data) {
    if (data.success) {
        // Show success notification
        showNotification(data.message.join('<br>'), 'success');

        // Execute action if specified
        if (data.action) {
            if (data.action === 'reload') {
                location.reload();
            } else if (data.action.startsWith('redirect:')) {
                location.href = data.action.substring(9);
            } else {
                // Execute custom JavaScript
                eval(data.action);
            }
        }
    } else {
        // Show error notification
        showNotification(data.message.join('<br>'), 'error');
    }
});
```

### Error Display
Validation errors can be displayed inline or as notifications:

```html
<!-- Inline error display -->
<div class="form-group">
    <label for="email"><?= tr("Email") ?></label>
    <input type="email" name="email" class="form-control" value="<?= set_value('email') ?>">
    <?= validation_show_error('email') ?>
</div>

<!-- Global error display -->
<?= validation_list_errors() ?>
```

## Best Practices

### Form Security
1. **Always include CSRF protection:**
   ```html
   <?= csrf_field() ?>
   ```

2. **Validate on both client and server side:**
   ```html
   <input type="email" name="email" required>
   ```

3. **Use proper input types and validation:**
   ```html
   <input type="file" accept=".pdf,.doc,.docx">
   <input type="tel" pattern="[0-9]{8}">
   ```

### Performance Optimization
1. **Disable submit button during processing**
2. **Show loading indicators**
3. **Use FormData for file uploads**
4. **Implement proper error handling**

### User Experience
1. **Provide clear error messages**
2. **Show progress indicators**
3. **Preserve form data on errors**
4. **Use appropriate response actions**

### Code Organization
1. **Keep validation rules in controllers**
2. **Use helper functions consistently**
3. **Implement proper logging**
4. **Follow naming conventions**

## Troubleshooting

### Common Issues

**CSRF Token Mismatch:**
- Ensure `<?= csrf_field() ?>` is included in forms
- Check that CSRF protection is properly configured

**File Upload Errors:**
- Verify `enctype="multipart/form-data"` is set
- Check file size limits in PHP and application config
- Ensure proper file validation rules

**Validation Not Working:**
- Verify validation rules syntax
- Check that field names match form inputs
- Ensure custom validation rules are properly registered

**AJAX Not Triggering:**
- Confirm form has `class="ajax"`
- Check for JavaScript errors in console
- Verify jQuery is loaded

**Response Not Handled:**
- Check response format matches expected structure
- Verify `submit_complete` event handler is registered
- Ensure proper JSON response from server
