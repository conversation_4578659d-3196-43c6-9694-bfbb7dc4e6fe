@import "../../node_modules/bootstrap/scss/bootstrap";

@import url("../vendor/font-awesome/css/all.min.css");

html,body{
  height:100vh; 
}

// main{
//   height: 92vh !important;
// }
.media{
  height: 100%;width: 100%;object-fit: contain
}

.btn.btn-rounded{
  border-radius: 40px;
}  

.btns-action{
  button,a{
    width: 50%;
  }
}

table.v-align-center{
  td{
    vertical-align: middle;
  }
}
.v-align-center{
  vertical-align: middle;
}
.text-end{
  text-align: end;
}
.text-start{
  text-align: start;
}

.form-control, .btn, .card, .badge, .alert, .modal-content,.input-group-text,.list-group,.list-group-item {
  border-radius: 0px;
}


.feather {
  width: 16px;
  height: 16px;
  vertical-align: text-bottom;
}

/*
 * Sidebar
 */

.sidebar {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100; /* Behind the navbar */
  padding: 48px 0 0; /* Height of navbar */
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
  position: relative;
  top: 0;
  height: calc(100vh - 48px);
  padding-top: .5rem;
  overflow-x: hidden;
  overflow-y: auto; /* Scrollable contents if viewport is shorter than content. */
}

@supports ((position: -webkit-sticky) or (position: sticky)) {
  .sidebar-sticky {
    position: -webkit-sticky;
    position: sticky;
  }
}

.sidebar .nav-link {
  font-weight: 500;
  color: #333;
}

.sidebar .nav-link .feather {
  margin-right: 4px;
  color: #999;
}

.sidebar .nav-link.active {
  color: #007bff;
}

.sidebar .nav-link:hover .feather,
.sidebar .nav-link.active .feather {
  color: inherit;
}

.sidebar-heading {
  font-size: .75rem;
  text-transform: uppercase;
}

/*
 * Content
 */

[role="main"] {
  padding-top: 133px; /* Space for fixed navbar */
}

@media (min-width: 768px) {
  [role="main"] {
    padding-top: 90px; /* Space for fixed navbar */
  }
}

/*
 * Navbar
 */

#pos-products{
  .nav-item a{
    border-radius: 0px;
    color:$dark;
    &.active{
      background: $dark;
      color:white;
    }
  }
  .tab-content{
 
    .tab-pane{
      .row{
        width: 100%;
        overflow-y: auto;
        max-height:86vh;
        margin: 0;
      }
      
    }
  }
  
  .card-product{
    position: relative;
    background: #000;

    .img-container{
      height: 90px;
    }
    .card-img-top{
  
    }
    .card-title{
  
      color: #fff;
      background: #000;
      width: 100%;
      margin: 0;
      font-size: 12px;
      height: 30px;
      padding: 3px;
     
    }
    .list-group > li{
      -webkit-box-flex: 1;
  -webkit-flex: 1;
  -moz-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -moz-box-orient: vertical;
  -moz-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  font-size: 12px !important;
    }

    .list-group{
      display: -webkit-box;
      display: -webkit-flex;
      display: -moz-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-box-direction: normal;
      -webkit-flex-direction: column;
      -moz-box-orient: vertical;
      -moz-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      margin: 0;
      flex: 1;
      height: 100%;
      .list-group-item{
        padding: 0 !important;
        font-size: 12px !important;
        height: auto;
        .btn{
          padding: 0 !important;
          height: 100%;
          font-size: 12px !important;
        }
      }
    }
  }


  #categories-list{

    max-height:86vh;
    overflow-y: auto;
  }
}

#bill_list{
  // min-height: 100%;
  min-height: 90vh;
  
 .table-box{
   overflow-y: scroll;
   max-height: 65vh;
    table.table-bill{
      
      table-layout:fixed;
      
      thead{
        th {
          position: sticky; top: 0 !important;
          font-size: 0.85rem;
          z-index: 2;
        }
      }
      
      tbody{
        overflow-y: scroll;
        td{

          z-index: 1;
          
        }
      }
    
    }
  }
}

div[data-notify=container]{
  z-index: 999;
}

// #products-list {
//   height: 73vh;
//   min-height: 300px !important;
//   overflow-y: scroll !important;
//   padding: 0 !important;
// }
// #products-list table{
//   height: auto;
//   overflow-y: scroll;
// }
// #products-list th {
//   position: sticky; top: 0 !important;
//   z-index: 999 !important;
// }





.datepicker .dow{
  padding: 10px;
}
.datepicker {
  
}
.datepicker .table-condensed{
  height: auto !important
}
   
.datepicker table{

}
.datepicker table {
  border-radius: 3px;
  overflow: hidden;
}

.datepicker th, .datepicker td {
  padding-right: 0.3em;
  padding-left: 0.3em;
  padding-top: 0.3em;
  padding-bottom: 0.3em;
  border: 1px solid white; 
}

.datepicker .disabled{
  background-color: #CCCCCC !important;
}
.datepicker .next, .datepicker .prev{
  background-color: #D1D1D1 !important;
}


.datepicker .active{
   background-image: -webkit-linear-gradient(#08c, #08c) !important;
}

.ui-autocomplete { 
  position: absolute;
  z-index: 99999 !important;
border-radius: 0px;
border:black 1px solid !important;
}

[v-cloak] { display: none; }



#kithcen{

#accordion{
  .card-header{
    height: 60px;
  }
}

  .tb-header{
    tbody{
      tr{
        td:nth-child(1){
          width: 30%;
        }
        td:nth-child(2){
          width: 10%;
        }
        td:nth-child(3){
          width: 20%;
        }
      }
    }
    
  }
}
