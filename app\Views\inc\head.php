<head>
    <meta charset="UTF-8">
    <meta name="<?= csrf_token() ?>" content="<?= csrf_hash() ?>">
    <title><?= esc($page['title']??env("app.name")) ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="shortcut icon" href="<?= base_url() ?>favicon.ico" type="image/x-icon">
    <link rel="icon" href="<?= base_url() ?>favicon.ico" type="image/x-icon">



    <script type="text/javascript">
    var base_url = "<?= base_url("/") ?>";
    var _base_url = "<?= base_url("/") ?>";
    var _confirm_action_prompt = "<?= tr("Are you sure about this action?") ?>";
    </script>
    <?php hooks()->do("before_load_page", url()->get()) ?>
    <meta name="theme-color" content="#303841" />
    <meta name="mobile-web-app-capable" content="yes">
    <link rel="apple-touch-icon" sizes="180x180" href="<?= base_url() ?>apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="<?= base_url() ?>favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="<?= base_url() ?>favicon-16x16.png">
    <link rel="manifest" href="<?= base_url() ?>site.webmanifest">
    <link rel="mask-icon" href="<?= base_url() ?>safari-pinned-tab.svg" color="#303841">
    <meta name="msapplication-TileColor" content="#303841">


    <link href="<?= base_url() ?>assets/css/icons/icomoon/styles.min.css" rel="stylesheet" type="text/css">


    <link href="<?= base_url() ?>assets/css/icons/fontawesome/all.css" rel="stylesheet" type="text/css">


    <?php if (current_lang()=="ar"): ?>

    <link href="<?= base_url() ?>assets/css/rtl/bootstrap.min.css" rel="stylesheet">
    <link href="<?= base_url() ?>assets/css/rtl/bootstrap_limitless.min.css" rel="stylesheet">
    <link href="<?= base_url() ?>assets/css/rtl/layout.min.css" rel="stylesheet">

    <link href="<?= base_url() ?>assets/css/rtl/components.min.css" rel="stylesheet">
    <link href="<?= base_url() ?>assets/css/rtl/colors.min.css" rel="stylesheet">

    <?php else: ?>


    <link href="<?= base_url() ?>assets/css/style.css" rel="stylesheet">
    <?php endif ?>



    <script src="<?= base_url() ?>assets/js/jquery.min.js"></script>


    <script src="<?= base_url() ?>assets/js/main/popper.min.js"></script>
    <script src="<?= base_url() ?>assets/js/main/bootstrap.min.js"></script>
    <script src="<?= base_url() ?>assets/js/plugins/loaders/blockui.min.js"></script>
    <script src="<?= base_url() ?>assets/js/vue.min.js"></script>




    <?php if (auth()): ?>
        <script src="<?= base_url() ?>assets/js/plugins/tables/datatables/datatables.min.js"></script>
        <script src="<?= base_url() ?>assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
        <script src="<?= base_url() ?>assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
        <script src="<?= base_url() ?>assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
        <script src="<?= base_url() ?>assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
        <link href="<?= base_url() ?>assets/vendors/summernote/summernote-lite.min.css" rel="stylesheet">
        <script src="<?= base_url() ?>assets/vendors/summernote/summernote-lite.min.js"></script>
        <script src="<?= base_url() ?>assets/js/chart.js"></script>


    <?php endif ?>

    <script src="<?= base_url() ?>assets/js/plugins/notifications/sweet_alert.min.js"></script>
    <script src="<?= base_url() ?>assets/js/plugins/ui/moment/moment.min.js"></script>

    <script src="<?= base_url() ?>assets/js/plugins/forms/selects/select2.min.js"></script>


    <script src="<?= base_url() ?>assets/js/plugins/notifications/pnotify.min.js"></script>

    


    <script src="<?= base_url() ?>assets/js/main/app.js"></script>


    <script src="<?= base_url() ?>assets/js/main/custom.js"></script>
    <link href="<?= base_url() ?>assets/css/custom.css?v=<?= date("Ymd") ?>" rel="stylesheet">

    <!-- Dynamic Color Theme CSS Override -->
    <style>
    :root {
        --primary: <?= get_option('color_primary', '#27374D') ?> !important;
        --secondary: <?= get_option('color_secondary', '#526D82') ?> !important;
        --body: <?= get_option('color_body', '#EEEEEE') ?> !important;
    }
    </style>

    <style>
    .dataTable thead .sorting:before {
        content: "" !important;
    }

    [v-cloak] {
        display: none;
    }

    input[type='number'] {
        direction: ltr;
        text-align: center;
    }

    .select2-results__option[aria-selected=true] {
        color: #fff;
        background-color: #384252;
    }
    </style>

    <?php if (current_lang()=="AR"): ?>
    <style>
    .bootstrap-select .dropdown-toggle .filter-option {
        text-align: right !important;
    }

    .bootstrap-select .dropdown-toggle .filter-option-inner {
        padding-right: 0 !important;
    }
    </style>


    <?php endif ?>

    <script type="text/javascript">
    (function($) {
        $.fn.button = function(action) {
            if (action === 'loading') {
                var loadingText = "<i class='fas fa-spinner fa-spin'></i> <?= tr('processing') ?>";
                this.data('original-text', this.html()).html(loadingText).prop('disabled', true);
            }
            if (action === 'reset' && this.data('original-text')) {
                this.html(this.data('original-text')).prop('disabled', false);
            }
        };
    }(jQuery));
    </script>

    <script>
    function getCSRFToken() {
        return document.querySelector('meta[name="<?= csrf_token() ?>"]').getAttribute('content');
    }


    function setCSRFToken(token) {
        document.querySelector('meta[name="<?= csrf_token() ?>"]').setAttribute('content', token);

        $('input[name=<?= csrf_token() ?>]').each(function(index, el) {
            $(el).val(token);
        });

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': token
            }
        });
    }

    function reset_form() {
        $('form').find(':input').each(function() {
            if ($(this).hasClass('<?= csrf_token() ?>')) {
                return;
            }
            $(this).val('');
        });


        $('form .modal').modal('hide');

        initDatatable();
    }


    $(document).ajaxComplete(function(event, xhr) {
        var csrfToken = xhr.getResponseHeader('<?= csrf_token() ?>');
        if (csrfToken) {
            setCSRFToken(csrfToken);
            console.log(csrfToken)

        }

        try {
            var response = JSON.parse(xhr.responseText);

            if (response.action === 'reload') {
                location.reload();

            } else if (response.action === 'redirect') {

                location.href = response.url;
            } else {

                if (typeof response.message === "string" || Array.isArray(response.message)) {

                    if (typeof response.message === "string") {
                        doswal({
                            "type": response.type,
                            'text': response.message,
                            confirmButtonText: 'موافق',

                        });
                    } else if (Array.isArray(response.message)) {
                        swall_text = '<ul>';
                        for (i = 0; i < response.message.length; i++) {

                            swall_text += '<li>' + response.message[i] + '</li>';


                        }
                        swall_text += '</ul>';

                        if (response.message.length) {
                            doswal({
                                "type": response.type,
                                'html': swall_text,
                                confirmButtonText: 'موافق',

                            });
                        }
                    } else {

                    }
                }


                try {
                    eval(response.action);
                } catch (e) {

                }

                try {
                    if (response.success == true) {
                        if (form.children('.modal').length == 1) {
                            form.children('.modal').modal('hide')
                        }
                    }

                } catch (e) {
                    if (form.children('.modal').length == 1) {
                        form.children('.modal').modal('hide')
                    }
                }

            }
        } catch (e) {

        }
    });

    var csrf = $('meta[name=<?= csrf_token() ?>]').attr("content")

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': csrf
        }
    });

    $(document).ready(function() {

        $("#ajax_loading").hide()

    });
    </script>

    <link href='<?= base_url() ?>assets/css/IBM.css' rel='stylesheet'>
    <style>
    * {
        font-family: 'IBM';
        ;
    }
    </style>
</head>