<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateDriveSystemMVP extends Migration
{
    public function up()
    {
        // 1. Create directories table
        $this->forge->addField([
            'id' => [
                'type' => 'CHAR',
                'constraint' => 36, // UUID: 550e8400-e29b-41d4-a716-************
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'parent_id' => [
                'type' => 'CHAR',
                'constraint' => 36,
                'null' => true,
            ],
            'user_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'path' => [
                'type' => 'TEXT',
            ],
            'level' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'size_bytes' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'default' => 0,
            ],
            'is_shared' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            ],
            'deleted_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('parent_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey(['path'], false, false, 'idx_path');
        $this->forge->createTable('directories');

        // 2. Create files table
        $this->forge->addField([
            'id' => [
                'type' => 'CHAR',
                'constraint' => 36, // UUID for security
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'original_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'directory_id' => [
                'type' => 'CHAR',
                'constraint' => 36,
            ],
            'user_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'file_path' => [
                'type' => 'TEXT',
            ],
            'mime_type' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'size_bytes' => [
                'type' => 'BIGINT',
                'constraint' => 20,
            ],
            'hash_sha256' => [
                'type' => 'VARCHAR',
                'constraint' => 64,
            ],
            'version_number' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 1,
            ],
            'is_shared' => [
                'type' => 'BOOLEAN',
                'default' => false,
            ],
            'download_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['ACTIVE', 'PROCESSING', 'DELETED'],
                'default' => 'ACTIVE',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
            ],
            'deleted_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('directory_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('hash_sha256');
        $this->forge->addKey('status');
        $this->forge->createTable('files');

        // 3. Create file_versions table
        $this->forge->addField([
            'id' => [
                'type' => 'CHAR',
                'constraint' => 36, // UUID
            ],
            'file_id' => [
                'type' => 'CHAR',
                'constraint' => 36,
            ],
            'version_number' => [
                'type' => 'INT',
                'constraint' => 11,
            ],
            'file_path' => [
                'type' => 'TEXT',
            ],
            'size_bytes' => [
                'type' => 'BIGINT',
                'constraint' => 20,
            ],
            'hash_sha256' => [
                'type' => 'VARCHAR',
                'constraint' => 64,
            ],
            'change_notes' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'created_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey(['file_id', 'version_number'], false, true, 'unique_file_version');
        $this->forge->addKey('file_id');
        $this->forge->createTable('file_versions');

        // 4. Create file_operations table
        $this->forge->addField([
            'id' => [
                'type' => 'CHAR',
                'constraint' => 36, // UUID
            ],
            'file_id' => [
                'type' => 'CHAR',
                'constraint' => 36,
            ],
            'operation_type' => [
                'type' => 'ENUM',
                'constraint' => ['MOVE', 'COPY', 'RENAME', 'DELETE', 'RESTORE'],
            ],
            'source_directory_id' => [
                'type' => 'CHAR',
                'constraint' => 36,
                'null' => true,
            ],
            'target_directory_id' => [
                'type' => 'CHAR',
                'constraint' => 36,
                'null' => true,
            ],
            'source_path' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'target_path' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'old_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'new_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'performed_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'operation_data' => [
                'type' => 'JSON',
                'null' => true,
            ],
            'can_undo' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'undone_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'undone_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('file_id');
        $this->forge->addKey('operation_type');
        $this->forge->addKey('performed_by');
        $this->forge->addKey('created_at');
        $this->forge->addKey('can_undo');
        $this->forge->createTable('file_operations');

        // 5. Create file_shares table
        $this->forge->addField([
            'id' => [
                'type' => 'CHAR',
                'constraint' => 36, // UUID
            ],
            'file_id' => [
                'type' => 'CHAR',
                'constraint' => 36,
                'null' => true,
            ],
            'directory_id' => [
                'type' => 'CHAR',
                'constraint' => 36,
                'null' => true,
            ],
            'shared_by' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
            ],
            'shared_with_user_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'shared_with_email' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'permission_level' => [
                'type' => 'ENUM',
                'constraint' => ['VIEW', 'EDIT'],
                'default' => 'VIEW',
            ],
            'share_token' => [
                'type' => 'VARCHAR',
                'constraint' => 64,
                'unique' => true,
                'null' => true,
            ],
            'expires_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
            'access_count' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
            'revoked_at' => [
                'type' => 'TIMESTAMP',
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('file_id');
        $this->forge->addKey('directory_id');
        $this->forge->addKey('shared_by');
        $this->forge->addKey('share_token');
        $this->forge->createTable('file_shares');

        // 6. Create activity_logs table
        $this->forge->addField([
            'id' => [
                'type' => 'CHAR',
                'constraint' => 36, // UUID
            ],
            'user_id' => [
                'type' => 'BIGINT',
                'constraint' => 20,
                'unsigned' => true,
                'null' => true,
            ],
            'file_id' => [
                'type' => 'CHAR',
                'constraint' => 36,
                'null' => true,
            ],
            'directory_id' => [
                'type' => 'CHAR',
                'constraint' => 36,
                'null' => true,
            ],
            'action' => [
                'type' => 'ENUM',
                'constraint' => ['UPLOAD', 'DOWNLOAD', 'VIEW', 'EDIT', 'DELETE', 'MOVE', 'COPY', 'SHARE', 'RENAME'],
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 45,
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => 'CURRENT_TIMESTAMP',
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('user_id');
        $this->forge->addKey('file_id');
        $this->forge->addKey('directory_id');
        $this->forge->addKey('action');
        $this->forge->addKey('created_at');
        $this->forge->createTable('activity_logs');

        // Add foreign key constraints
        $this->addForeignKeys();
    }

    private function addForeignKeys()
    {
        // Add foreign key constraints after all tables are created
        
        // Directories foreign keys
        $this->forge->addForeignKey('parent_id', 'directories', 'id', 'CASCADE', 'CASCADE', 'fk_directories_parent');
        
        // Files foreign keys  
        $this->db->query('ALTER TABLE files ADD CONSTRAINT fk_files_directory FOREIGN KEY (directory_id) REFERENCES directories(id) ON DELETE CASCADE');
        
        // File versions foreign keys
        $this->db->query('ALTER TABLE file_versions ADD CONSTRAINT fk_file_versions_file FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE');
        
        // File operations foreign keys
        $this->db->query('ALTER TABLE file_operations ADD CONSTRAINT fk_file_operations_file FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE');
        $this->db->query('ALTER TABLE file_operations ADD CONSTRAINT fk_file_operations_source_dir FOREIGN KEY (source_directory_id) REFERENCES directories(id) ON DELETE SET NULL');
        $this->db->query('ALTER TABLE file_operations ADD CONSTRAINT fk_file_operations_target_dir FOREIGN KEY (target_directory_id) REFERENCES directories(id) ON DELETE SET NULL');
        
        // File shares foreign keys
        $this->db->query('ALTER TABLE file_shares ADD CONSTRAINT fk_file_shares_file FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE');
        $this->db->query('ALTER TABLE file_shares ADD CONSTRAINT fk_file_shares_directory FOREIGN KEY (directory_id) REFERENCES directories(id) ON DELETE CASCADE');
        
        // Activity logs foreign keys
        $this->db->query('ALTER TABLE activity_logs ADD CONSTRAINT fk_activity_logs_file FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE SET NULL');
        $this->db->query('ALTER TABLE activity_logs ADD CONSTRAINT fk_activity_logs_directory FOREIGN KEY (directory_id) REFERENCES directories(id) ON DELETE SET NULL');
    }

    public function down()
    {
        // Drop tables in reverse order to handle foreign key constraints
        $this->forge->dropTable('activity_logs', true);
        $this->forge->dropTable('file_shares', true);
        $this->forge->dropTable('file_operations', true);
        $this->forge->dropTable('file_versions', true);
        $this->forge->dropTable('files', true);
        $this->forge->dropTable('directories', true);
    }
} 