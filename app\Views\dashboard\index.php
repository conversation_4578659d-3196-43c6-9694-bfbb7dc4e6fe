<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>

<!-- Welcome Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800"> <?= tr("Welcome") ?> <?= esc(auth()->name) ?></h1>
</div>

<!-- Statistics Cards Row -->
<div class="row">
    <!-- Total Jobs Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            <?= tr("Total Jobs") ?>
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $total_jobs ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-briefcase fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Applications Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            <?= tr("Total Applications") ?>
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $total_applications ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Jobs Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            <?= tr("Active Jobs") ?>
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $active_jobs ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-play fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Applications (30 days) -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            <?= tr("Recent Applications") ?> (30 <?= tr("days") ?>)
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?= $recent_applications ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row">
    <!-- Applications by Region Chart (Top 8 Governors) -->
    <div class="col-xl-3 col-lg-3">
        <div class="card ">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary"><?= tr(" Regions") ?></h6>
            </div>
            <div class="card-body">
                <div class=" pt-4 pb-2">
                    <canvas id="regionsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications by Jobs Chart -->
    <div class="col-xl-3 col-lg-3">
        <div class="card ">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary"><?= tr("Applications by Jobs") ?></h6>
            </div>
            <div class="card-body">
                <div class=" pt-4 pb-2">
                    <canvas id="jobsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Applications by Gender Chart -->
    <div class="col-xl-3 col-lg-3">
        <div class="card ">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary"><?= tr("Applications by Gender") ?></h6>
            </div>
            <div class="card-body">
                <div class=" pt-4 pb-2">
                    <canvas id="genderChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Application Status Chart -->
    <div class="col-xl-3 col-lg-3">
        <div class="card ">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary"><?= tr("Applications by Status") ?></h6>
            </div>
            <div class="card-body">
                <div class=" pt-4 pb-2">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>




<?= $this->endSection() ?>

<?= $this->section("script") ?>
<script src="<?= base_url() ?>assets/js/chart.js"></script>

<script>
// Prepare data for charts
const regionsData = <?= json_encode($applications_by_region) ?>;
const jobsData = <?= json_encode($applications_by_jobs) ?>;
const genderData = <?= json_encode($applications_by_gender) ?>;
const statusData = <?= json_encode($applications_by_status) ?>;
const statusLabels = <?= json_encode($status_labels) ?>;

// Color palettes
const colors = [
    '<?= get_option("color_primary") ?>', '<?= get_option("color_secondary") ?>', '<?= get_option("color_body") ?>',
    '<?= get_option("color_primary") ?>', '<?= get_option("color_secondary") ?>', '<?= get_option("color_body") ?>'
];

// Applications by Region Pie Chart (Top 8 Governors)
const regionsChart = new Chart(document.getElementById('regionsChart'), {
    type: 'doughnut',
    data: {
        labels: regionsData.map(item => item.region_name),
        datasets: [{
            data: regionsData.map(item => item.count),
            backgroundColor: colors.slice(0, regionsData.length),
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Applications by Jobs Chart
const jobsChart = new Chart(document.getElementById('jobsChart'), {
    type: 'doughnut',
    data: {
        labels: jobsData.map(item => item.job_name),
        datasets: [{
            data: jobsData.map(item => item.count),
            backgroundColor: colors.slice(0, jobsData.length),
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Applications by Gender Chart
const genderChart = new Chart(document.getElementById('genderChart'), {
    type: 'doughnut',
    data: {
        labels: genderData.map(item => item.gender == 'Male' ? '<?= tr("Male") ?>' : '<?= tr("Female") ?>'),
        datasets: [{
            data: genderData.map(item => item.count),
            backgroundColor: ['<?= get_option("color_primary") ?>',
                '<?= get_option("color_secondary") ?>'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Applications by Status Chart
const statusChart = new Chart(document.getElementById('statusChart'), {
    type: 'doughnut',
    data: {
        labels: statusData.map(item => statusLabels[item.status] || item.status || '<?= tr("Unknown") ?>'),
        datasets: [{
            data: statusData.map(item => item.count),
            backgroundColor: colors.slice(0, statusData.length),
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>

<?= $this->endSection() ?>