<?php

namespace App\Controllers;



use App\Models\User;
use App\Models\Log;
use Illuminate\Database\Capsule\Manager as DB;

class Logs extends BaseController
{



    public function index()
    {   



        
        $data=[];

        $this->d['page']['title']="Logs";
        $this->d['nav']=[
            "active"=>"logs",
            "breadcrumb"=>[
                tr("Home")=>base_url("dashboard"),
                tr("Logs")=>"",
            ]
        ];

        $this->d['users']=User::all();

        return view("logs/index",$this->d);


    }


    public function datatable(){

        $draw = intval($this->request->getVar('draw'));
        $start = intval($this->request->getVar('start'));
        $length = intval($this->request->getVar('length'));
        $searchValue = $this->request->getVar('search')['value']??'';

        $query = Log::query();

     
        $user_id = $this->request->getVar('user');


        if ($user_id) {
            $query->where('user_id', $user_id);
        }


        if (!empty($searchValue)) {
            $query->where(function($q) use ($searchValue) {
                $q->where('description', 'LIKE', '%' . $searchValue . '%')
                  ->orWhere('created_at', 'LIKE', '%' . $searchValue . '%');
            });
        }


        // Apply ordering
        $order = $this->request->getVar('order');
        if (!empty($order)) {

            $columns = ['id', 'user_id', 'created_at', 'description' ];

          
              // Define your own column mapping here
            $query->orderBy($columns[$order[0]['column']], $order[0]['dir']);
        }

        $total = $query->count();

        $data = $query->skip($start)
                    ->take($length)
                    ->get();

        $table = [];

        foreach ($data as $key => $ro) {
            $row = [];


            $row[] = $ro->id;
            $row[] = $ro->user->name;
       
            $row[] = date("Y-m-d h:iA",strtotime($ro->created_at));
            $row[] = $ro->description;


            $table[] = $row;
        }

        $filteredTotal = !empty($searchValue) ? count($data) : $total;

        $output = [
            'draw' => $draw,
            'recordsTotal' => $total,
            'recordsFiltered' => $filteredTotal,
            'data' => $table
        ];

        return $this->response->setJSON($output);
    }
   

    
}