# Job Status System

## Overview
The job status system provides three distinct visibility levels for job postings, allowing administrators to control which jobs are visible to the public and manage the job lifecycle effectively.

## Status Types

### Public Status
- **Value**: `public`
- **Visibility**: Visible to all users (public and administrators)
- **Purpose**: Active job postings that are open for applications
- **Badge Color**: Green (success)
- **Access**: Anyone can view and apply to these jobs

### Private Status  
- **Value**: `private`
- **Visibility**: Visible only to administrators
- **Purpose**: Jobs under review, preparation, or administrative filtering
- **Badge Color**: Yellow (warning)
- **Access**: Only users with `manage-jobs` permission can view

### Archive Status
- **Value**: `archive` 
- **Visibility**: Visible only to administrators
- **Purpose**: Historical jobs no longer accepting applications
- **Badge Color**: Gray (secondary)
- **Access**: Only users with `manage-jobs` permission can view

## Implementation Details

### Database Schema
```sql
-- Jobs table status field accepts the new values
ALTER TABLE jobs MODIFY status varchar(100) NOT NULL DEFAULT 'private';

-- Indexes for performance
CREATE INDEX idx_jobs_status ON jobs(status);
CREATE INDEX idx_jobs_status_end_date ON jobs(status, end_date);
```

### Model Constants
```php
// Job.php model constants
const STATUS_PUBLIC = 'public';
const STATUS_PRIVATE = 'private'; 
const STATUS_ARCHIVE = 'archive';
```

### Scope Methods
```php
// Available query scopes
Job::public()->get();           // Only public jobs
Job::private()->get();          // Only private jobs  
Job::archive()->get();          // Only archived jobs
Job::visibleToPublic()->get();  // Public jobs only
Job::visibleToAdmin()->get();   // All jobs (admin view)
```

## Access Control Rules

### Public Users
- Can only see jobs with `public` status
- Cannot access private or archived jobs
- Job listing on homepage shows only public jobs

### Administrators  
- Can see all job statuses (`public`, `private`, `archive`)
- Can create jobs with any status
- Can change job status through edit interface
- Applications interface shows all jobs regardless of status

## User Interface

### Job Creation Form
- Status dropdown with three options:
  - **Public**: Visible to all users and can be applied to
  - **Private**: Visible only to administrators for review  
  - **Archive**: No longer active, kept for historical purposes
- Help text explains each status type
- Default status is `private` for new jobs

### Job Listing (Admin)
- Status badges with appropriate colors:
  - Public: Green badge
  - Private: Yellow badge  
  - Archive: Gray badge
- All statuses visible in admin job management

### Public Job Listing
- Only shows jobs with `public` status
- No status badges shown to public users
- Filtered automatically by visibility rules

## Migration Process

### Existing Data
```sql
-- Convert existing statuses
UPDATE jobs SET status = CASE 
    WHEN status = 'Active' THEN 'public'
    WHEN status = 'Inactive' THEN 'private'
    ELSE 'private'
END;
```

### Validation Rules
```php
// Controller validation
'status' => 'required|in:public,private,archive'
```

## Security Considerations

### Permission Checks
- All admin job operations require `manage-jobs` permission
- Public job listing bypasses permission checks
- Private/archive jobs never exposed to public endpoints

### Data Filtering
- Home controller automatically filters to public jobs only
- Applications controller shows all jobs to admins
- API endpoints respect visibility rules

## Usage Examples

### Creating a Public Job
```php
$job = Job::create([
    'name' => 'Software Developer',
    'status' => Job::STATUS_PUBLIC,
    // ... other fields
]);
```

### Filtering Jobs by Status
```php
// Get all public jobs
$publicJobs = Job::public()->get();

// Get jobs visible to current user
$visibleJobs = _can('manage-jobs') 
    ? Job::visibleToAdmin()->get()
    : Job::visibleToPublic()->get();
```

### Checking Job Status
```php
if ($job->isPublic()) {
    // Job is visible to public
}

if ($job->isPrivate()) {
    // Job is private (admin only)
}

if ($job->isArchive()) {
    // Job is archived
}
```

## Testing Scenarios

### Public User Access
1. Visit homepage - should only see public jobs
2. Try to access private job directly - should get 404/403
3. Application form should only show public jobs

### Admin User Access  
1. Job management shows all statuses with proper badges
2. Can create jobs with any status
3. Can change job status through edit form
4. Applications interface shows all jobs

### Status Transitions
1. New job defaults to private status
2. Private → Public: Job becomes visible to public
3. Public → Archive: Job no longer accepts applications
4. Archive → Public: Job can be reactivated if needed

## Performance Considerations

### Database Indexes
- `idx_jobs_status` for status-based queries
- `idx_jobs_status_end_date` for combined filtering
- Queries optimized for common access patterns

### Caching Strategy
- Public job listings can be cached longer
- Admin views may need more frequent cache invalidation
- Status changes should clear relevant caches

## Future Enhancements

### Potential Additions
- **Draft Status**: For jobs being prepared
- **Scheduled Status**: For jobs with future publication dates
- **Expired Status**: For jobs past their end date
- **Workflow**: Approval process for status changes

### Automation Opportunities
- Auto-archive jobs past end date
- Scheduled publication of private jobs
- Notification system for status changes
