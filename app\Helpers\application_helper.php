<?php

use App\Models\User;
use App\Models\Application;
use App\Models\ApplicationInterviewMark;
use App\Models\JobInterviewer;
use App\Models\Job;
use App\Libraries\Notify;
use Carbon\Carbon;


hooks()->add("application_mark_updated",function($id){

	$application = Application::find($id);

	$interviewer_total = ApplicationInterviewMark::where("application_id", $application->id)->sum('total');
	$interviewers_count = ApplicationInterviewMark::where("application_id",$application->id)->count();
	$application->interview_mark = $interviewers_count == 0 ? 0 : $interviewer_total/$interviewers_count;
	$application->total_mark = ($application->interview_mark / 55 * 60 )  + $application->test_mark / 100 * 40;

    $application->save();

});


hooks()->add("job_updated",function($id){

	$job = Job::find($id);

	foreach ($job->applications()->get() as $key => $app) {
		hooks()->do("application_mark_updated",$app->id);
	}

});

hooks()->add("init",function(){

});

hooks()->add("application_submitted",function($application){

	if(!get_option("sms_new") || strlen(get_option("sms_new")) == 0){
		return;
	}

    $application = Application::find($application->id);
	
	if(!$application){
		return;
	}

	$job = Job::find($application->job_id);

	if(!$job){
		return;
	}


	$notify = new Notify();

    $template = template(get_option("sms_new"));

	$template->name = $application->name;
	$template->card_id = $application->card_id;
	$template->job_name = $job->name;
	$template->phone = _dr($application->phone);
	$template->application_id = "APL-".str_pad($application->id, 5, "0", STR_PAD_LEFT);


	$notify->text($template->render());


	if(is_numeric($application->phone)){
		$notify->phone($application->phone);
	}else{
		$notify->phone(_dr($application->phone));
	}

	if(strlen($application->phone_2) > 0){
		if(is_numeric($application->phone_2)){
			$notify->phone($application->phone_2);
		}else{
			$notify->phone(_dr($application->phone_2));
		}
	}

	$notify->send_sms();

});
