/* ------------------------------------------------------------------------------
 *
 *  # Cards content
 *
 *  Demo JS code for content_cards_content.html page
 *
 * ---------------------------------------------------------------------------- */


// Setup module
// ------------------------------

var CardsContent = function() {


    //
    // Setup module components
    //

    // Lightbox
    var _componentFancybox = function() {
        if (!$().fancybox) {
            console.warn('Warning - fancybox.min.js is not loaded.');
            return;
        }

        // Image lightbox
        $('[data-popup="lightbox"]').fancybox({
            padding: 3
        });
    };


    //
    // Return objects assigned to module
    //

    return {
        init: function() {
            _componentFancybox();
        }
    }
}();


// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function() {
    CardsContent.init();
});
