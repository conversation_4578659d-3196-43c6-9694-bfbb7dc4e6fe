<?= $this->extend('layouts/auth') ?>

<?= $this->section('content') ?>
<div class="content d-flex justify-content-center align-items-center px-1 ">


    <form class="login-form wmin-sm-400 ajax" method="post" autocomplete="off" enctype="multipart/form-data">
        <?php display()->messages() ?>
        <div class="message"></div>
        <div class="card  ">


            <div class=" card-body text-dark">
                <div class="tab-pane fade show active" id="login-tab1">
                    <div class="text-center mb-3 py-5">
                        <i class="far fa-lock-alt fa-4x text-primary"></i>

                    </div>

                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link <?= (url()->get() === base_url("auth/otp_login")) ? 'active' : '' ?>"
                                href="<?= base_url("auth/otp_login") ?>"><?= tr("OTP Login") ?></a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?= (url()->get() === base_url("auth/login")) ? 'active' : '' ?>"
                                href="<?= base_url("auth/login") ?>"><?= tr("Credentials") ?></a>
                        </li>
                    </ul>

                    <div class="form-group form-group-feedback form-group-feedback-left">
                        <input type="text" class="form-control" name="username" placeholder="Username">
                        <div class="form-control-feedback">
                            <i class="icon-user text-muted"></i>
                        </div>
                    </div>






                    <div class="form-group">
                        <button type="submit" name="signin" class="btn btn-primary btn-large py-2  btn-block"
                            id="submit-btn"><?= tr("Sign In") ?></button>
                    </div>








                </div>


            </div>
        </div>


        <div class="modal fade" id="otp-modal" tabindex="-1" role="dialog" aria-labelledby="otp-modalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="otp-modalLabel"><?= tr("Enter OTP") ?></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="input-group">
                            <input type="text" class="form-control" name="otp">
                            <span class="input-group-append">
                                <span class="input-group-text otp-timer">
                                    <span class="countdown text-monospace text-muted" data-show="#request-otp"
                                        data-hide="#request-otp" id="login-otp-delay">0</span>

                                </span>

                            </span>
                        </div>
                        <?php if (get_option('recaptcha_active')): ?>
                        <p>
                        <div class="g-recaptcha form-field" style="width: 100%"
                            data-sitekey="<?= get_option('recaptcha_site') ?>"></div>

                        </p>

                        <?php endif ?>


                        <a href="#" id="request-otp" class="otp"> <?= tr("Request OTP") ?></a>

                    </div>
                    <div class="modal-footer">
                        <button type="button" onclick="$('#submit-btn').trigger('click')"
                            class="btn btn-primary btn-block py-2"><?= tr("Submit") ?></button>
                    </div>
                </div>
            </div>
        </div>

    </form>


</div>

<?php if (get_option('recaptcha_active')): ?>
<script type="text/javascript" src="https://www.google.com/recaptcha/api.js?hl=<?= current_lang() ?>"></script>
<?php endif ?>

<script type="text/javascript">
$(document).ready(function() {
    $("button.otp, a.otp").on("click", function() {

        $.post('<?= base_url("auth/otp") ?>', {
            username: $("input[name=username]").val()
        }, function(data, textStatus, xhr) {


            if (data.success == true) {
                $("#otp-modal").modal("show");
            }

        }, 'json');
    });
});
</script>


<?= $this->endSection() ?>