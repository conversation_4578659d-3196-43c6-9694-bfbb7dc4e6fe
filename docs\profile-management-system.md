# Profile Management System

## Overview
Complete profile management system that allows users to create and maintain their personal profiles before applying for jobs. The system integrates with the existing job application workflow to ensure profile completeness.

## Requirements
Users must have a complete profile with validated phone number before they can apply for jobs. The profile includes personal information, qualifications, work experience, and document uploads.

## Implementation Steps

### Database Structure
The system uses the existing `profiles` table with the following fields:
- **Personal Information**: `name`, `name_en`, `gender`, `email`, `phone`, `card_id`, `birth`, `phone_2`
- **Location**: `reg_id`, `city_id`
- **Documents**: `cv_file`, `card_id_file`, `image_file`
- **System Fields**: `created_at`, `updated_at`, `deleted_at`

### Related Tables
- `profile_qualifications` - Educational background
- `profile_experiences` - Work history
- `profile_certs` - Additional certificates and documents

### Authentication Flow
1. User enters phone number on login page
2. OTP sent to phone for verification
3. Profile session established after successful OTP verification
4. User redirected to profile completion if profile is incomplete

### Profile Completion Requirements
- **Mandatory Fields**: Name (Arabic & English), Gender, Email, Card ID, Birth Date, Phone, Location
- **Required Documents**: CV file, Card ID copy, Profile image
- **Optional**: Secondary phone, work experience, additional certificates
- **Validation**: At least one qualification must be added

### Integration Points
- **My Controller**: Check profile completeness before job application
- **Home Controller**: Redirect to profile system for job applications
- **Profile Authentication**: `profile_auth()` helper function
- **Session Management**: Profile-specific session handling

## Security Considerations
- Phone number validation and encryption
- Rate limiting for OTP requests
- Failed attempt tracking and account locking
- Data encryption for sensitive fields (card_id, email, phone)

## API Endpoints

### Profile Management
- `GET /profile` - Profile dashboard
- `GET /profile/edit` - Profile edit form
- `POST /profile/update` - Update profile information
- `POST /profile/upload_image` - Upload profile image

### Authentication
- `GET /profile/login` - Login form
- `POST /profile/login` - Phone number submission
- `GET /profile/otp` - OTP verification form
- `POST /profile/otp` - OTP verification
- `POST /profile/resend_otp` - Resend OTP
- `POST /profile/logout` - End profile session

### Profile Components
- `POST /profile/create_qualification` - Add qualification
- `POST /profile/delete_qualification/{id}` - Remove qualification
- `POST /profile/create_experience` - Add work experience
- `POST /profile/delete_experience/{id}` - Remove experience
- `POST /profile/create_cert` - Add certificate
- `POST /profile/delete_cert/{id}` - Remove certificate

## Validation Rules

### Phone Number
- Required, 8 digits, Oman numbers only (70000000-99999999)
- Custom validation messages in Arabic

### Personal Information
- Name (Arabic): Required, minimum 3 characters
- Name (English): Required, minimum 3 characters
- Gender: Required, from predefined list
- Email: Required, valid email format
- Card ID: Required, numeric, unique per job application
- Birth Date: Required, valid date, age restrictions

### Documents
- CV File: Required, PDF format, max 2MB
- Card ID File: Required, PDF format, max 2MB
- Profile Image: Optional, image formats, max 1MB

### Location
- Governorate: Required, must exist in regions table
- City: Required, must exist in cities table and match governorate

## UI Components

### Profile Form Structure
```php
<form method="post" class="ajax" enctype="multipart/form-data">
    <!-- Personal Information Section -->
    <!-- Location Section --> 
    <!-- Document Upload Section -->
    <!-- Qualifications Dynamic Table -->
    <!-- Experience Dynamic Table -->
    <!-- Certificates Dynamic Table -->
</form>
```

### Form Validation Display
- Real-time validation feedback
- Success/error message display
- Loading states for form submissions
- Dynamic content updates via AJAX

## File Management
- Integration with existing storage system
- Secure file upload handling
- File type and size validation
- Storage cleanup for deleted profiles

## Workflow Integration

### Job Application Process
1. User clicks "Apply" on job listing
2. System checks if user is authenticated via profile
3. If not authenticated, redirect to profile login
4. If authenticated but profile incomplete, redirect to profile completion
5. If profile complete, proceed to job application form
6. Pre-populate application form from profile data

### Profile Completion Check
```php
function isProfileComplete($profile) {
    $required = ['name', 'name_en', 'gender', 'email', 'card_id', 'birth', 'reg_id', 'city_id', 'cv_file', 'card_id_file'];
    
    foreach($required as $field) {
        if(empty($profile->$field)) return false;
    }
    
    return $profile->qualifications()->count() > 0;
}
```

## Progressive Enhancement

### Mobile Responsiveness
- Bootstrap 4 responsive design
- Touch-friendly form controls
- Mobile-optimized file uploads
- Responsive data tables

### Accessibility
- Proper form labels and ARIA attributes
- Keyboard navigation support
- Screen reader compatibility
- Color contrast compliance

## Performance Optimization
- Lazy loading of profile components
- Efficient database queries with relationships
- Client-side form validation
- Optimized file upload handling

## Testing Strategy
- Unit tests for profile validation
- Integration tests for authentication flow
- End-to-end tests for complete user journey
- Security testing for data protection

## Maintenance
- Regular cleanup of expired OTP records
- Profile data backup and recovery
- Performance monitoring and optimization
- Security audit and updates 