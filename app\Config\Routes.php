<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

// Public routes (no authentication required)
$routes->get('/', 'Home::index');
$routes->get('/job/(:any)', 'Home::job_details/$1');
$routes->get('/proceed/(:any)', 'Home::proceed/$1');
$routes->get('/profile_preview', 'Home::profile_preview');
$routes->post('/confirm_application', 'Home::confirm_application');
$routes->get('/success', 'Home::application_success');
$routes->match(['get', 'post'], '/get_cities/(:any)', 'Home::get_cities/$1');
$routes->match(['get', 'post'], 'home/get_cities/(:any)', 'Home::get_cities/$1');

// Auth routes (no authentication required - these handle authentication)
$routes->group('auth', [], function($routes) {
    $routes->match(['get', 'post'], 'login', 'Auth::login');
    $routes->match(['get', 'post'], 'register', 'Auth::register');
    $routes->match(['get', 'post'], 'otp_login', 'Auth::otp_login');
    $routes->match(['get', 'post'], 'otp', 'Auth::otp');
    $routes->match(['get', 'post'], 'verify_otp', 'Auth::verify_otp');
    $routes->match(['get', 'post'], 'resend_otp', 'Auth::resend_otp');
    $routes->match(['get', 'post'], 'forgot_password', 'Auth::forgot_password');
    $routes->match(['get', 'post'], 'reset_password', 'Auth::reset_password');
    $routes->match(['get', 'post'], 'reset_password/(:any)', 'Auth::reset_password/$1');
    $routes->get('logout', 'Auth::logout');
    $routes->get('profile', 'Auth::profile');
    $routes->match(['get', 'post'], 'change_password', 'Auth::change_password');
    $routes->match(['get', 'post'], '2fa', 'Auth::two_factor');
    $routes->match(['get', 'post'], '2fa/enable', 'Auth::enable_2fa');
    $routes->match(['get', 'post'], '2fa/disable', 'Auth::disable_2fa');
    $routes->get('verify_email/(:any)', 'Auth::verify_email/$1');
    $routes->match(['get', 'post'], 'verify_2fa', 'Auth::verify_2fa');
});

// Dashboard (protected)
$routes->get('dashboard', 'Dashboard::index', ['filter' => 'auth']);

// Applications management (protected)
$routes->group('applications', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Applications::index');
    $routes->get('(:num)', 'Applications::index/$1');
    $routes->get('search/(:num)', 'Applications::search/$1');
    $routes->get('get_attend/(:num)', 'Applications::get_attend/$1');
    
    // Application data and viewing
    $routes->get('count/(:num)', 'Applications::count/$1');
    $routes->match(['get', 'post'], 'datatable/(:num)', 'Applications::datatable/$1');
    $routes->get('view/(:num)', 'Applications::view/$1');
    $routes->get('edit/(:num)', 'Applications::edit/$1');
    
    // Application status and group management
    $routes->post('update_status/(:num)', 'Applications::update_status/$1');
    $routes->post('update_group/(:num)', 'Applications::update_group/$1');
    
    // File uploads
    $routes->post('upload_cv/(:num)', 'Applications::upload_cv/$1');
    $routes->post('upload_card_id/(:num)', 'Applications::upload_card_id/$1');
    $routes->post('upload_image/(:num)', 'Applications::upload_image/$1');
    
    // Interview and test marks
    $routes->post('add_interview_mark/(:num)', 'Applications::add_interview_mark/$1');
    $routes->post('update_interview_mark/(:num)', 'Applications::update_interview_mark/$1');
    $routes->post('add_test_mark/(:num)', 'Applications::add_test_mark/$1');
    
    // Qualification management
    $routes->post('create_qualification/(:num)', 'Applications::create_qualification/$1');
    $routes->post('update_qualification/(:num)', 'Applications::update_qualification/$1');
    $routes->post('delete_qualification/(:num)', 'Applications::delete_qualification/$1');
    
    // Experience management
    $routes->post('create_experience/(:num)', 'Applications::create_experience/$1');
    $routes->post('update_experience/(:num)', 'Applications::update_experience/$1');
    $routes->post('delete_experience/(:num)', 'Applications::delete_experience/$1');
    
    // Certificate management
    $routes->post('create_cert/(:num)', 'Applications::create_cert/$1');
    $routes->post('update_cert/(:num)', 'Applications::update_cert/$1');
    $routes->post('delete_cert/(:num)', 'Applications::delete_cert/$1');
    
    // Application deletion
    $routes->post('delete', 'Applications::delete');
    
    // SMS functionality
    $routes->post('sms', 'Applications::sms');
    $routes->post('application_sms/(:num)', 'Applications::application_sms/$1');

    
    // Group assignment
    $routes->get('test_group/(:num)', 'Applications::test_group/$1');
    $routes->get('interview_group/(:num)', 'Applications::interview_group/$1');
    $routes->get('final_approval/(:num)', 'Applications::final_approval/$1');
    $routes->get('final_approval', 'Applications::final_approval');
    $routes->get('approval/(:num)', 'Applications::approval/$1');
});

$routes->group('interviews', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Interviews::index');
    $routes->get('(:num)', 'Interviews::index/$1');
    $routes->get('view/(:num)', 'Interviews::view/$1');
    $routes->post('add_mark/(:num)', 'Interviews::add_mark/$1');
    $routes->post('update_mark/(:num)', 'Interviews::update_mark/$1');
});

// Questions management (protected)
$routes->group('questions', ['filter' => 'auth'], function($routes) {
    $routes->get('(:num)', 'Questions::index/$1');
    $routes->get('datatable/(:num)/(:any)', 'Questions::datatable/$1/$2');
});

// Job management (protected)
$routes->group('jobs', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Jobs::index');
    $routes->post('/', 'Jobs::index');
    $routes->get('create', 'Jobs::create');
    $routes->post('create', 'Jobs::create');
    $routes->get('edit/(:num)', 'Jobs::edit/$1');
    $routes->get('edit/(:num)/interview-questions', 'Jobs::interview_questions/$1');
    $routes->post('update', 'Jobs::update');
    $routes->match(['get', 'post'], 'datatable', 'Jobs::datatable');
    $routes->get('get/(:num)', 'Jobs::get/$1');
    $routes->get('import_applications', 'Jobs::import_applications');
    $routes->post('import_applications', 'Jobs::import_applications');
    $routes->get('delete/(:num)', 'Jobs::delete/$1');
    
    // Interview Questions management
    $routes->match(['get', 'post'], 'interview-questions/(:num)', 'Jobs::interview_questions/$1');
    $routes->post('interview-questions/create/(:num)', 'Jobs::create_interview_question/$1');
    $routes->post('interview-questions/update/(:num)', 'Jobs::update_interview_question/$1');
    $routes->post('interview-questions/delete/(:num)', 'Jobs::delete_interview_question/$1');
});

// Disciplines management (protected)
$routes->group('disciplines', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Disciplines::index');
    $routes->get('create', 'Disciplines::create');
    $routes->post('create', 'Disciplines::create');
    $routes->get('edit/(:num)', 'Disciplines::edit/$1');
    $routes->post('update/(:num)', 'Disciplines::update/$1');
    $routes->get('delete/(:num)', 'Disciplines::delete/$1');
    $routes->post('datatable', 'Disciplines::datatable');
    $routes->get('tree', 'Disciplines::tree');
});

// Data export routes (protected)
$routes->post('data/export/(:any)', 'Data::export/$1', ['filter' => 'auth']);

// File management routes (protected)
$routes->get('files/(:any)', 'Files::index/$1');

// Profile routes (protected)
$routes->group('profile', function($routes) {
    $routes->get('/', 'ProfileController::index');
    $routes->get('login', 'ProfileController::login');
    $routes->post('login', 'ProfileController::login');
    $routes->get('otp', 'ProfileController::otp');
    $routes->post('otp', 'ProfileController::otp');
    $routes->post('resend_otp', 'ProfileController::resend_otp');
    $routes->match(['get', 'post'], 'logout', 'ProfileController::logout');
    
    // Profile Management
    $routes->get('edit', 'ProfileController::edit');
    $routes->post('edit', 'ProfileController::edit');
    $routes->post('upload_image', 'ProfileController::upload_image');
    
    // Profile Components
    $routes->post('create_qualification', 'ProfileController::create_qualification');
    $routes->get('delete_qualification/(:any)', 'ProfileController::delete_qualification/$1');
    $routes->post('create_experience', 'ProfileController::create_experience');
    $routes->get('delete_experience/(:any)', 'ProfileController::delete_experience/$1');
    $routes->post('create_cert', 'ProfileController::create_cert');
    $routes->get('delete_cert/(:any)', 'ProfileController::delete_cert/$1');
});

// Roles management (protected)
$routes->group('roles', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Roles::index');
    $routes->post('/', 'Roles::index');
    $routes->get('create', 'Roles::create');
    $routes->post('create', 'Roles::create');
    $routes->get('update/(:num)', 'Roles::update/$1');
    $routes->post('update/(:num)', 'Roles::update/$1');
    $routes->get('delete/(:num)', 'Roles::delete/$1');
});

// Users management (protected)
$routes->group('users', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Users::index');
    $routes->get('new', 'Users::new');
    $routes->post('new', 'Users::new');
    $routes->get('view/(:num)', 'Users::view/$1');
    $routes->post('view/(:num)', 'Users::view/$1');
    $routes->get('reset_password/(:num)', 'Users::reset_password/$1');
    $routes->get('delete/(:num)', 'Users::delete/$1');
    
    // Profile and 2FA routes
    $routes->get('profile', 'Users::profile');
    $routes->match(['get', 'post'], 'setup_2fa', 'Users::setup_2fa');
    $routes->post('confirm_2fa', 'Users::confirm_2fa');
    $routes->get('disable_2fa', 'Users::disable_2fa');
    $routes->get('send_reset_password', 'Users::send_reset_password');
});

$routes->group('settings', ['filter' => 'auth'], function($routes) {
    $routes->match(['get', 'post'], '/', 'Settings::index');
});

// Language Management routes (protected)
$routes->group('language-manager', ['filter' => 'auth'], function($routes) {
    $routes->match(['get', 'post'], '/', 'LanguageManager::index');
    $routes->match(['get', 'post'], 'datatable', 'LanguageManager::datatable');
    $routes->post('save', 'LanguageManager::save');
});

// Logs management (protected)
$routes->group('logs', ['filter' => 'auth'], function($routes) {
    $routes->get('/', 'Logs::index');
    $routes->match(['get', 'post'], 'datatable', 'Logs::datatable');
});

// Special access routes (my/* - these have their own access control)
$routes->group('my', [], function($routes) {
    $routes->get('session/(:any)', 'My::session/$1');
    $routes->get('registration/confirm/(:any)', 'My::register_confirm/$1');
    $routes->get('hackathon/confirm/(:any)', 'My::hackathon_confirm/$1');
    $routes->post('create_qualification', 'My::create_qualification');
    $routes->get('delete_qualification/(:any)', 'My::delete_qualification/$1');
    $routes->post('create_experience', 'My::create_experience');
    $routes->get('delete_experience/(:any)', 'My::delete_experience/$1');
});

// Test/notification routes (special access)
$routes->group('tt', [], function($routes) {
    $routes->get('sms', 'Tt::sms');
    $routes->get('email', 'Tt::email');
});

