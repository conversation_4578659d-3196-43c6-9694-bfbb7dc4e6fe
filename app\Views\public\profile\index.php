<?= $this->extend('layouts/public') ?>
<?= $this->section('content') ?>

<div class="container-fluid pb-4 pt-3">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <?= display()->messages() ?>
            <div class="d-flex justify-content-between align-items-center">
                <h3><?= tr("My Profile") ?></h3>
                <div>
                    <a href="<?= base_url('profile/edit') ?>" class="btn btn-light btn-sm">
                        <i class="fas fa-edit"></i> <?= tr("Edit Profile") ?>
                    </a>
                </div>
            </div>
            <div class="card">


                <div class="card-body">
                    <!-- Profile Completion Status -->
                    <div class="alert alert-info d-flex align-items-center mb-4">
                        <div class="flex-grow-1">
                            <h6 class="mb-1"><?= tr("Profile Completion") ?></h6>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar" role="progressbar"
                                    style="width: <?= $completion_percentage ?>%"
                                    aria-valuenow="<?= $completion_percentage ?>" aria-valuemin="0" aria-valuemax="100">
                                </div>
                            </div>
                            <small class="text-muted"><?= $completion_percentage ?>% <?= tr("completed") ?></small>
                        </div>
                        <div class="ml-3">
                            <i class="fas fa-chart-pie fa-2x text-primary"></i>
                        </div>
                    </div>

                    <?php if (!empty($missing_fields)): ?>
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> <?= tr("Missing Information") ?></h6>
                        <ul class="mb-0 pl-3">
                            <?php foreach ($missing_fields as $field): ?>
                            <li><?= esc($field) ?></li>
                            <?php endforeach ?>
                        </ul>
                        <hr>
                        <a href="<?= base_url('profile/edit') ?>" class="btn btn-warning btn-sm">
                            <?= tr("Complete Profile") ?>
                        </a>
                    </div>
                    <?php endif ?>

                    <!-- Profile Information -->
                    <div class="row">
                        <div class="col-md-4 text-center mb-4">
                            <div class="profile-image-container">
                                <img src="<?= $profile->getImageUrl() ?>" alt="<?= tr("Profile Image") ?>"
                                    class="img-fluid rounded-circle shadow"
                                    style="width: 150px; height: 150px; object-fit: cover;">
                            </div>
                            <h5 class="mt-3 mb-1"><?= esc($profile->name) ?></h5>
                            <p class="text-muted"><?= esc($profile->name_en) ?></p>
                        </div>

                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Phone") ?></label>
                                    <div class="fw-bold" dir="ltr">+968 <?= esc($profile->phone) ?></div>
                                </div>

                                <?php if ($profile->phone_2): ?>
                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Secondary Phone") ?></label>
                                    <div class="fw-bold" dir="ltr">+968 <?= esc($profile->phone_2) ?></div>
                                </div>
                                <?php endif ?>

                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Email") ?></label>
                                    <div class="fw-bold"><?= esc($profile->email) ?></div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Card ID") ?></label>
                                    <div class="fw-bold"><?= esc($profile->card_id) ?></div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Gender") ?></label>
                                    <div class="fw-bold"><?= tr($profile->gender) ?></div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Birth Date") ?></label>
                                    <div class="fw-bold"><?= $profile->birth ? $profile->birth->format('Y-m-d') : '' ?>
                                    </div>
                                </div>

                                <?php if ($profile->reg_id): ?>
                                <div class="col-md-6 mb-3">
                                    <label class="text-muted small"><?= tr("Location") ?></label>
                                    <div class="fw-bold">
                                        <?= reg($profile->reg_id)->name_ar ?? '' ?>
                                        <?php if ($profile->city_id): ?>
                                        - <?= city($profile->city_id)->name_ar ?? '' ?>
                                        <?php endif ?>
                                    </div>
                                </div>
                                <?php endif ?>
                            </div>
                        </div>
                    </div>

                    <!-- Documents Section -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="border-bottom pb-2"><?= tr("Documents") ?></h5>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card border">
                                <div class="card-body text-center py-3">
                                    <i class="fas fa-file-pdf fa-2x text-danger mb-2"></i>
                                    <h6><?= tr("CV File") ?></h6>
                                    <?php if ($profile->cv_file): ?>
                                    <a href="<?= $profile->getCvUrl() ?>" target="_blank"
                                        class="btn btn-sm btn-link text-primary">
                                        <i class="fas fa-download"></i> <?= tr("Download") ?>
                                    </a>
                                    <?php else: ?>
                                    <small class="text-muted"><?= tr("Not uploaded") ?>
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </small>
                                    <?php endif ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card border">
                                <div class="card-body text-center py-3">
                                    <i class="fas fa-id-card fa-2x text-primary mb-2"></i>
                                    <h6><?= tr("Card ID File") ?></h6>
                                    <?php if ($profile->card_id_file): ?>
                                    <a href="<?= $profile->getCardIdUrl() ?>" target="_blank"
                                        class="btn btn-sm btn-link text-primary">
                                        <i class="fas fa-download"></i> <?= tr("Download") ?>
                                    </a>
                                    <?php else: ?>
                                    <small class="text-muted"><?= tr("Not uploaded") ?>
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </small>
                                    <?php endif ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-3">
                            <div class="card border">
                                <div class="card-body text-center py-3">
                                    <i class="fas fa-file-pdf fa-2x text-danger mb-2"></i>
                                    <h6><?= tr("Ministry of Labor File") ?></h6>
                                    <?php if ($profile->mol_file): ?>
                                    <a href="<?= $profile->getMolUrl() ?>" target="_blank"
                                        class="btn btn-sm btn-link text-primary">
                                        <i class="fas fa-download"></i> <?= tr("Download") ?>
                                    </a>
                                    <?php else: ?>
                                    <small class="text-muted"><?= tr("Not uploaded") ?>
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </small>
                                    <?php endif ?>
                                </div>
                            </div>
                        </div>
                    </div>







                </div>

                
            </div>
            

            <h5 class="mt-3"><?= tr("Qualifications") ?></h5>
            <div class="card">
                <!-- Qualifications Section -->
                <?php if ($profile->qualifications->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="bg-primary">
                            <tr>
                                <th><?= tr("Major") ?></th>
                                <th><?= tr("Qualification") ?></th>
                                <th><?= tr("Location") ?></th>
                                <th><?= tr("Year") ?></th>
                                <th><?= tr("GPA") ?></th>
                                <th><?= tr("File") ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($profile->qualifications as $q): ?>
                            <tr>
                                <td><?= esc($q->major) ?></td>
                                <td><?= esc($q->qualification) ?></td>
                                <td><?= esc($q->location) ?></td>
                                <td><?= esc($q->end_year) ?></td>
                                <td><?= esc($q->gpa) ?></td>
                                <td>
                                    <?php if ($q->file_id): ?>
                                    <a href="<?= $q->getFileUrl() ?>" class="btn btn-sm btn-link text-primary">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <?php endif ?>
                                </td>
                            </tr>
                            <?php endforeach ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-3 text-muted">
                    <i class="fas fa-graduation-cap fa-2x mb-2"></i>
                    <p><?= tr("No qualifications added yet") ?></p>
                </div>
                <?php endif ?>

            </div>

            <!-- Experience Section -->
            <h5 class="mt-3"><?= tr("Work Experience") ?></h5>
            <div class="card">
                <?php if ($profile->experiences->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead class="bg-primary">
                            <tr>
                                <th><?= tr("experience_job") ?></th>
                                <th><?= tr("experience_location") ?></th>
                                <th><?= tr("experience_start_date") ?></th>
                                <th><?= tr("experience_end_date") ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($profile->experiences as $e): ?>
                            <tr>
                                <td><?= esc($e->job_name) ?></td>
                                <td><?= esc($e->location) ?></td>
                                <td><?= esc($e->start_date) ?></td>
                                <td><?= esc($e->end_date) ?></td>
                            </tr>
                            <?php endforeach ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-3 text-muted">
                    <i class="fas fa-briefcase fa-2x mb-2"></i>
                    <p><?= tr("No work experience added yet") ?></p>
                </div>
                <?php endif ?>
            </div>

            <!-- Certificates Section -->
            <h5 class="mt-3"><?= tr("Other Certificates") ?></h5>
            <div class="card">
                <?php if ($profile->certs->count() > 0): ?>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($profile->certs as $c): ?>
                        <div class="col-md-4 my-2">
                            <div class="card border">
                                <div class="card-body text-center py-3">
                                    <i class="far fa-file-certificate fa-2x text-primary mb-2"></i>
                                    <h6><?= esc($c->name) ?></h6>
                                    <?php if ($c->file_id): ?>
                                    <a href="<?= storage()->get($c->file_id) ?>" target="_blank"
                                        class="btn btn-sm btn-link text-primary">
                                        <i class="fas fa-download"></i> <?= tr("Download") ?>
                                    </a>
                                    <?php endif ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach ?>
                    </div>
                </div>
                <?php else: ?>
                <div class="text-center py-3 text-muted">
                    <i class="fas fa-certificate fa-2x mb-2"></i>
                    <p><?= tr("No certificates added yet") ?></p>
                </div>
                <?php endif ?>
            </div>


            <div class="text-center">
                <a href="<?= base_url() ?>" class="btn btn-secondary px-3 py-2 mt-3">
                    <i class="fas fa-arrow-right"></i> <?= tr("Back") ?>
                </a>
                <a href="<?= base_url('profile/edit') ?>" class="btn btn-primary px-3 py-2 mt-3">
                    <i class="fas fa-edit"></i> <?= tr("Edit Profile") ?>
                </a>

                <a href="<?= base_url('profile/logout') ?>" class="btn btn-danger px-3 py-2 mt-3">
                    <i class="fas fa-sign-out-alt"></i> <?= tr("Logout") ?>
                </a>
            </div>


        </div>
    </div>
</div>

<?= $this->endSection() ?>