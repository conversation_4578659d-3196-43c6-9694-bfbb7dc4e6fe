<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>

<div class="d-flex mb-1 justify-content-between">
    <h3><?= tr("Roles") ?></h3>
    <div>
        <a class="btn btn-primary" href="<?= base_url("roles/create") ?>"><i class="fas fa-plus"></i>
            <?= tr("New role") ?></a>

    </div>
</div>
<div class="card shadow">
    <!-- <div class="card-header bg-primary"></div> -->
    <table class="table datatable table-striped table-hover table-sm">
        <thead class="bg-primary">
            <tr>
                <th><?= tr("No.") ?></th>
                <th><?= tr("Name") ?></th>
                <th><?= tr("Rights") ?></th>
                <th><?= tr("Members") ?></th>

            </tr>
        </thead>
        <tbody>
            <?php foreach ($list as $key => $item): ?>
            <tr>
                <td><a href="<?= base_url("roles/update/".($item->id)) ?>"><?= $item->id ?></a></td>
                <td><?= $item->name ?></td>
                <td><?= implode(" . ", ($item->roles)??[]) ?></td>
                <td><?= $item->users()->count() ?></td>
            </tr>
            <?php endforeach ?>
        </tbody>
    </table>
</div>

<?= $this->endSection() ?>