<?php

namespace App\Controllers;
use Illuminate\Database\Capsule\Manager as DB;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Job;


class Home extends BaseController
{
    public function index()
    {


        if (get_option("public_status")!=="active" && !auth()) {
            return _error_code(404);
        }


        $this->d['page']["title"]=get_option("app_name");

 
        $this->d['jobs'] = Job::visibleToPublic()->orderBy("end_date","desc")->get();

    

        return view('public/index',$this->d);
    }

    public function job_details($code){
        
        if (get_option("public_status")!=="active" && !auth()) {
            return _error_code(404);
        }

        $job = Job::visibleToPublic()->where("code",$code)->first();

        if(!$job){
            return _error_code(404);
        }

        $this->d['job'] = $job;

        $this->d['nav']['breadcrumb'] = [
            tr("Job details") => ''
        ];


        return view('public/job_details',$this->d);
    }

    public function proceed($code){

        if (get_option("public_status")!=="active" && !auth()) {
            return _error_code(404);
        }

        $job = Job::visibleToPublic()->where("code",$code)->first();

        if(!$job){
            return _error_code(404);
        }

        // Check if user is authenticated via profile system
        if(!profile_auth()){
            // Store job code in session for after login redirect
            session()->set('intended_job_code', $code);
            return redirect()->to(base_url("profile/login"));
        }

        $profile = profile_auth();
        
        // Check if profile is complete
        if(!$profile->isComplete()){
            // Store job code in session for after profile completion
            session()->set('intended_job_code', $code);
            display()->message(tr('Please complete your profile before applying for jobs'));
            return redirect()->to(base_url("profile"));
        }
        
        // Profile is complete, show profile preview for confirmation
        return redirect()->to(base_url("profile_preview")."?job_code=".$code);
    }

    public function profile_preview(){
        
        if (get_option("public_status")!=="active" && !auth()) {
            return _error_code(404);
        }

        if(!profile_auth()){
            return redirect()->to(base_url("profile/login"));
        }

        if(!input("job_code")){
            return _error_code(404);
        }

        $job = Job::visibleToPublic()->where("code", input("job_code"))->first();
        
        if(!$job){
            return _error_code(404);
        }

        $profile = profile_auth();
        
        // Check if profile is complete
        if(!$profile->isComplete()){
            display()->message(tr('Please complete your profile before applying for jobs'));
            return redirect()->to(base_url("profile"));
        }

        // Check if already applied for this job
        $existingApplication = \App\Models\Application::where('job_id', $job->id)
            ->where('profile_id', $profile->id)
            ->first();

        if($existingApplication){
            display()->message(tr('You have already applied for this job'));
            return redirect()->to(base_url("job/".$job->code));
        }

        $this->d['profile'] = $profile;
        $this->d['job'] = $job;
        $this->d['nav'] = [
            'breadcrumb' => [
                tr('Job Details') => base_url('job/'.$job->code),
                tr('Profile Preview') => ''
            ]
        ];

        return view('public/profile_preview', $this->d);
    }

    public function confirm_application(){

        if (get_option("public_status")!=="active" && !auth()) {
            return _error_code(404);
        }
        
        if(!profile_auth()){
            return _response([
                "success" => false,
                "message" => [tr("Not authenticated")]
            ]);
        }

        if(!input("job_code")){
            return _response([
                "success" => false,
                "message" => [tr("Job not specified")]
            ]);
        }

        $job = Job::visibleToPublic()->where("code", input("job_code"))->first();
        
        if(!$job){
            return _response([
                "success" => false,
                "message" => [tr("Job not found")]
            ]);
        }

        $profile = profile_auth();
        
        // Check if profile is complete
        if(!$profile->isComplete()){
            return _response([
                "success" => false,
                "message" => [tr("Profile is not complete")]
            ]);
        }

        // Check if already applied for this job
        $existingApplication = \App\Models\Application::where('job_id', $job->id)
            ->where('profile_id', $profile->id)
            ->first();

        if($existingApplication){
            return _response([
                "success" => false,
                "message" => [tr("You have already applied for this job")]
            ]);
        }

        // Copy profile data to application
        $application = new \App\Models\Application();
        
        // Copy main profile data
        $application->name = $profile->name;
        $application->name_en = $profile->name_en;
        $application->gender = $profile->gender;
        $application->email = $profile->email;
        $application->card_id = $profile->card_id;
        $application->birth = $profile->birth;
        $application->phone = _cr($profile->phone);
        $application->phone_2 = _cr($profile->phone_2);
        $application->reg_id = $profile->reg_id;
        $application->city_id = $profile->city_id;
        $application->cv_file = $profile->cv_file;
        $application->card_id_file = $profile->card_id_file;
        $application->image_file = $profile->image_file;
        $application->job_id = $job->id;
        $application->status = 'new';
        $application->profile_id = $profile->id;
        $application->phone_confirmed = _cr($profile->phone); // Mark as confirmed from profile
        
        $application->save();

        // Copy qualifications
        foreach($profile->qualifications as $qualification){
            \App\Models\ApplicationQualification::create([
                'application_id' => $application->id,
                'major' => $qualification->major,
                'qualification' => $qualification->qualification,
                'location' => $qualification->location,
                'gpa' => $qualification->gpa,
                'end_year' => $qualification->end_year,
                'file_id' => $qualification->file_id
            ]);
        }

        // Copy experiences
        foreach($profile->experiences as $experience){
            \App\Models\ApplicationExperience::create([
                'application_id' => $application->id,
                'job_name' => $experience->job_name,
                'location' => $experience->location,
                'start_date' => $experience->start_date,
                'end_date' => $experience->end_date,
                'is_current' => $experience->is_current
            ]);
        }

        // Copy certificates
        foreach($profile->certs as $cert){
            \App\Models\ApplicationCert::create([
                'application_id' => $application->id,
                'name' => $cert->name,
                'file_id' => $cert->file_id
            ]);
        }

        hooks()->do("application_submitted",$application);

        return _response([
            "success" => true,
            "message" => [tr("Application submitted successfully")],
            "action" => "location.href='" . base_url('success?application_id=' . _cr($application->id)) . "'"
        ]);
    }

    public function application_success(){

        if (get_option("public_status")!=="active" && !auth()) {
            return _error_code(404);
        }
        
        if(!profile_auth()){
            return redirect()->to(base_url("profile/login"));
        }
        
        if(!input("application_id")){
            return _error_code(404);
        }

        $application_id = _dr(input("application_id"));
        $profile = profile_auth();
       
        $application = \App\Models\Application::where('id', $application_id)
            ->where('profile_id', $profile->id)
            ->where('status','new')
            ->first();

        if(!$application){
            return _error_code(404);
        }

        $this->d['application'] = $application;
        $this->d['job'] = $application->job;
        $this->d['nav'] = [
            'breadcrumb' => [
                tr('Application Success') => ''
            ]
        ];

        return view('public/application_success', $this->d);
    }
    



    public function get_cities($reg_id=""){
        
        
        $list = DB::table("cities")->where("reg_id",_dr($reg_id))->get();

        $n=0;

        foreach ($list as $key => $city) {


            $list[$n]->{'id'} = _cr($city->id);
            $list[$n]->{'name'} = ($city->name_ar);


            $n++;

        }

        return json_encode($list);

        
    }



}
