<?php 

namespace Config;

use Illuminate\Database\Capsule\Manager as Capsule;

class Eloquent
{
    public function __construct()
    {
        $capsule = new Capsule;

        $capsule->addConnection([
            'driver'    => 'mysql',
            'host'      => env('database.default.hostname'),
            'database'  => env('database.default.database'),
            'username'  => env('database.default.username'),
            'password'  => env('database.default.password'),
            'charset'   => env('database.default.charset'),
            'collation' => env('database.default.collation'),
            'prefix'    => env('database.default.DBPrefix'),
            'strict'    => false,
            'engine'    => null,
            'options'   => [
                // Performance: Enable persistent connections to reduce connection overhead
                \PDO::ATTR_PERSISTENT => true,
                
                // Existing options
                \PDO::ATTR_CASE => \PDO::CASE_NATURAL,
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
                \PDO::ATTR_ORACLE_NULLS => \PDO::NULL_NATURAL,
                \PDO::ATTR_STRINGIFY_FETCHES => false,
                \PDO::ATTR_EMULATE_PREPARES => false,
                
                // Performance: Buffer optimization
                \PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                // \PDO::MYSQL_ATTR_MAX_BUFFER_SIZE => 1024 * 1024, // 1MB buffer
                
                // Performance: Connection timeout and prefetch settings
                \PDO::ATTR_TIMEOUT => 30,
                \PDO::ATTR_PREFETCH => 1000,
                
                // Performance: Initialize with optimal SQL mode for production
                // \PDO::MYSQL_ATTR_INIT_COMMAND => "SET sql_mode='STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'",
            ],
        ]);

        // Set the event dispatcher used by Eloquent models.
        $capsule->setAsGlobal();

        // Setup the Eloquent ORM... (optional; unless you've used setEventDispatcher())
        $capsule->bootEloquent();
        
        // Performance: Prevent lazy loading in production to catch N+1 queries
        if (env('CI_ENVIRONMENT') === 'production') {
            \Illuminate\Database\Eloquent\Model::preventLazyLoading(true);
        }
        
        // Development: Enable query logging for debugging
        if (env('CI_ENVIRONMENT') === 'development') {
            \Illuminate\Database\Capsule\Manager::connection()->enableQueryLog();
        }
    }
}