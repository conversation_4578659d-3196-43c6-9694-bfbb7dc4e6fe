<?php 
function unparse_url($parsed_url) { 

  $scheme   = isset($parsed_url['scheme']) ? $parsed_url['scheme'] . '://' : ''; 
  $host     = isset($parsed_url['host']) ? $parsed_url['host'] : ''; 
  $port     = isset($parsed_url['port']) ? ':' . $parsed_url['port'] : ''; 

  $user     = isset($parsed_url['user']) ? $parsed_url['user'] : ''; 
  $pass     = isset($parsed_url['pass']) ? ':' . $parsed_url['pass']  : ''; 
  $pass     = ($user || $pass) ? "$pass@" : ''; 
  $path     = isset($parsed_url['path']) ? $parsed_url['path'] : ''; 
  $query    = (isset($parsed_url['query']) && strlen($parsed_url['query'] )>0) ? '?' . $parsed_url['query'] : ''; 
  $fragment = isset($parsed_url['fragment']) ? '#' . $parsed_url['fragment'] : ''; 

  return "$scheme$user$pass$host$port$path$query$fragment"; 
}

class Url
{

  private $_this_url = "";

  private $_url = "";
  
  function __construct($url="")
  {
    $this_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";

    $this->_this_url=$this_url;


    if (strlen($url)>0 ) {
      $this->_url=$url;
    }else{
      $this->_url=$this_url;
    }
  }

  private function isAssoc(array $arr)
  {
      if (array() === $arr) return false;
      return array_keys($arr) !== range(0, count($arr) - 1);
  }

  public function add($key=[], $value=[]){
    if (is_array($key)) {

      if ($this->isAssoc($key)) {
         $value = array_values($key);
        $key = array_keys($key);
       
      }
      
      $this->_url=$this->remove($key);
      
      for ($i=0; $i < count($key); $i++) { 
        $this->_url = preg_replace('/(.*)(?|&)'. $key[$i] .'=[^&]+?(&)(.*)/i', '$1$2$4', $this->_url .'&');
        $this->_url = substr($this->_url, 0, -1);
        
        if (strpos($this->_url, '?') === false) {
          $this->_url = $this->_url .'?'. $key[$i] .'='. $value[$i];
        } else {
          $this->_url =$this->_url .'&'. $key[$i] .'='. $value[$i];
        }
      }
    }else{
      $this->_url=$this->remove([$key]);
      $this->_url = preg_replace('/(.*)(?|&)'. $key .'=[^&]+?(&)(.*)/i', '$1$2$4', $this->_url .'&');
      $this->_url = substr($this->_url, 0, -1);
      
      if (strpos($this->_url, '?') === false) {
        return ($this->_url .'?'. $key .'='. $value);
      } else {
        return ($this->_url .'&'. $key .'='. $value);
      }
    }

    return $this->_url;
  }



  public function remove($key){
    $url = parse_url($this->_url);

    if (!isset($url['query'])) return unparse_url($url);
    parse_str($url['query'], $query_data);

    if (is_array($key)) {
      for ($i=0; $i < count($key); $i++) { 
        if (isset($query_data[$key[$i]])) {
          unset($query_data[$key[$i]]);
        }
      }
    }else{
      if ($key===TRUE) {
        $query_data=[];
      }else{
        if (isset($query_data[$key])) {
          unset($query_data[$key]);
        }
      }
    }


        
    $url['query'] = http_build_query($query_data);

    return unparse_url($url);
  }

  public function get(){
    return $this->_this_url;
  }


  public function app(){
    return base_url();
  }


  public function go(){
    // redirect()->to(base_url());
    return redirect()->to($this->_url);
    // die;
    // header("location:".$this->_url);
  }
 

}




function url($url = ""){

 

  return new Url($url);
}



function remove_from_url($sourceURL, $key){

  return url($sourceURL)->remove($key);
}




function add_to_url($url, $key=[], $value=[]) {
  return url($url)->add($key,$value);
}
