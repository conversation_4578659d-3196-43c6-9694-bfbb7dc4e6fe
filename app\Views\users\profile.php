<?= $this->extend('layouts/main') ?>

<?= $this->section('content') ?>
<br>


<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="d-flex justify-content-between">
            <h3><?= tr("Profile") ?></h3>
            <div>
                <a href="<?= base_url("users/send_reset_password") ?>" title="" class="btn btn-secondary "><?= tr("Reset password request") ?></a>
            </div>
        </div>
        <div class="card sahdow">
            <div class="card-body">

                <label><?= tr("Name") ?></label>
                <input type="text" class="form-control" disabled="" value="<?= esc($user->name) ?>" > <br>
                <label><?= tr("Card ID") ?></label>
                <input type="text" class="form-control" disabled="" value="<?= esc($user->log_name) ?>" > <br>
                <label><?= tr("Phone") ?></label>
                <input type="text" class="form-control" disabled="" value="<?= esc($user->phone) ?>" > <br>
                <label><?= tr("Email") ?></label>
                <input type="text" class="form-control" disabled="" value="<?= esc($user->email) ?>" > <br>
                <br>
                <div id="2fa-setup">
                    <?php if ($user->two_factor_enabled): ?>
                    <p><?= tr("Two-factor authentication is currently") ?> <strong class="text-success"><?= tr("Enabled") ?></strong>.</p>
                    
                    <a href="<?= base_url("users/disable_2fa") ?>" class="btn btn-danger"><?= tr("Disable 2FA") ?></a>
                    
                    <?php else: ?>
                    <p><?= tr("Two-factor authentication is currently") ?> <strong class="text-danger"><?= tr("disabled") ?></strong>.</p>
                    <a onclick="setup_2fa()" href="#" class="btn btn-primary"><?= tr("Enable 2FA") ?></a>
                    <?php endif; ?>
                    
                    
                </div>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">

	function setup_2fa(){
        console.log('Setting up 2FA...');
		$.get('<?= base_url("users/setup_2fa") ?>', function(html) {
            console.log('HTML received, length:', html.length);
			$('#2fa-setup').html(html);
		}).fail(function(xhr, status, error) {
            console.error('AJAX request failed:', status, error);
            console.log('Response text:', xhr.responseText);
        });
	}
</script>
<?= $this->endSection() ?>

