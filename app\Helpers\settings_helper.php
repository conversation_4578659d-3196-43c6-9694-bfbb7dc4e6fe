<?php

use Illuminate\Database\Capsule\Manager as DB;

use App\Models\Setting;


function get_option($option_name) {
    // Replace spaces with hyphens in the option name
    $option_name = str_replace(' ', '-', $option_name);

    $option = Setting::where('name', $option_name)->first();
    return $option ? $option->value : null;
}

function set_option($option_name, $value) {
    // Replace spaces with hyphens in the option name
    $option_name = str_replace(' ', '-', $option_name);

    $option = Setting::firstOrNew(['name' => $option_name]);
    $option->value = $value;
    $option->save();
}
