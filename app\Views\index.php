<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>
<!-- Welcome message -->
<div class="row">
    <!-- <div class="col-lg-3">
        <div class="card shadow card-hover bg-primary">
            <div class="card-body d-flex justify-content-between align-items-center ">
                <div class="col-4 text-center">
                    <i class="fas fa-microphone-stand fa-2x"></i>
                </div>
                <div>
                    <h1>210</h1>
                    <h3>الاعلاميون</h3>
                </div>
            </div>
        </div>
    </div> -->


    <div class="container">
    <canvas id="requestsChart"></canvas>
</div>
    
</div>


<script>
$(document).ready(function() {
    var ctx = $('#requestsChart');
    var requestsChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($labels); ?>,
            datasets: [{
                label: 'Requests per Day',
                data: <?php echo json_encode($counts); ?>,
                backgroundColor: 'rgba(0, 123, 255, 0.5)',
                borderColor: 'rgba(0, 123, 255, 1)',
                borderWidth: 1
            }]
        },
        options: {
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>

<?= $this->endSection() ?>
<?= $this->section("script") ?>

<?= $this->endSection() ?>