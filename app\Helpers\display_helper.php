<?php

class Display {
	private static  $errors    = array();
	private static  $messages  = array();
	private static  $success  = array();
	private static  $_ajax  = false;


	public function __construct(){			
		if (isset($_SESSION['success_message']) && is_array($_SESSION['success_message'])) {
			self::$success=$_SESSION['success_message'];
		}
		if (isset($_SESSION['message_message']) && is_array($_SESSION['message_message'])) {
			self::$messages=$_SESSION['message_message'];
		}
		if (isset($_SESSION['error_message']) && is_array($_SESSION['error_message'])) {
			self::$errors=$_SESSION['error_message'];
		}

	}



	public static function add_error($error){
		self::$errors[] = $error;
	}

	public static function error($error){
		if (is_array($error)) {
			foreach ($error as $key => $value) {
				self::$errors[]=$value;
			}
		}else{
			self::$errors[] = $error;
		}

		if (self::$_ajax) {
			echo json_encode([
	            'action' => 'display_message',
	            'type' => 'error',
	            'message' => is_array($error) ? $error : [$error]
	        ]);
	        die();
		}

		$_SESSION['error_message']=self::$errors;
	}


	public static function add_message($message){
		self::$messages[] = $message;
	}

	public static function message($message){
		if (is_array($message)) {
			foreach ($message as $key => $value) {
				self::$messages[]=$value;
			}
		}else{
			self::$messages[] = $message;
		}

		if (self::$_ajax) {
			echo json_encode([
	            'action' => 'display_message',
	            'type' => 'error',
	            'message' => is_array($message) ? $message : [$message]
	        ]);
	        die();
		}
		$_SESSION['message_message']=self::$messages;
	}

	public static function add_success($success){
		self::$success[] = $success;
	}

	public static function success($success){
		if (is_array($success)) {
			foreach ($success as $key => $value) {
				self::$success[]=$value;
			}
		}else{
			self::$success[] = $success;
		}

		if (self::$_ajax) {
			echo json_encode([
	            'action' => 'display_message',
	            'type' => 'success',
	            'message' => is_array($success) ? $success : [$success]
	        ]);
	        die();
		}

		$_SESSION['success_message']=self::$success;
		
	}


	public static function messages(){
		// collect  everyone's messages and errors
		$errors   = array_merge(self::$errors?self::$errors:[]);
		$messages = array_merge(self::$messages?self::$messages:[]);
		$success  = array_merge(self::$success?self::$success:[]);
	  
		//display errors
        foreach ($errors as $error) {
	    	echo '<div class="alert alert-danger alert-dismissible"><button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button><strong></strong>'.PHP_EOL;
	        echo $error.PHP_EOL; 
	    	echo '</div>'.PHP_EOL;          
	    }
	    $_SESSION['error_message']=null;
	
		//display messages
		foreach ($messages as $message) {
	    	echo '<div class="alert alert-warning alert-dismissible"><button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button><strong></strong>'.PHP_EOL;
	        echo $message.PHP_EOL; 
	    	echo '</div>'.PHP_EOL;             
	    } 
	    $_SESSION['message_message']=null;

	    //display success
		foreach ($success as $success) {
			
	
	    	echo '<div class="alert alert-success alert-dismissible"><button type="button" class="close" data-dismiss="alert"><span>×</span></button><strong></strong>'.PHP_EOL;
	        echo $success.PHP_EOL; 
	    	echo '</div>'.PHP_EOL;               
	    }    

	    $_SESSION['success_message']=null;
		
		//clean buffers	
		self::$messages = array();
		self::$errors   = array();	    
		self::$success  = array();	    
	}

	public function ajax(){
		self::$_ajax=true;
		return $this;
	}

	public function json(){
		self::$_ajax=true;
		return $this;
	}


    function __destruct(){
    	
	}

}

function display(){
	return new Display();
}