<?= $this->extend('layouts/main') ?>
<?= $this->section('content') ?>
<div class="d-flex ">
				<h3><?= tr("New user") ?></h3>
			</div>
<div class="row justify-content-center">

	<div class="col-md-8">
		<div class="card">
			
			<div class="card-body">
				<form method="post" class="ajax"  autocomplete="off">
					<div class="form-group">
						
						
						


						<div class="row">
							<div class="col-md-6">
								<label for=""><?= tr("Name") ?> *</label>
								<input  required type="text" class="form-control"  name="name"><br>
							</div>
							<div class="col-md-6">
								<label for=""><?= tr("Card Id") ?> *</label>
								<input type="text" name="log_name" class="form-control" required ><br>
							</div>
							
						</div>

						<div class="row">
							<div class="col-md-6">
								<label for=""><?= tr("Phone") ?> *</label>
								<input type="text" name="phone" class="form-control"  ><br>
							</div>
							<div class="col-md-6">
								<label for=""><?= tr("Email") ?> *</label>
								<input  required type="email"  class="form-control" name="email"><br>
							</div>
							
						</div>
						
					
						<br>
						
						<div class="row">
							<div class="col-md-6">
								<label for=""><?= tr("Role") ?> *</label>
								<select name="role_id" class="form-control">
									<?php foreach ($roles as $key => $role): ?>
									<option value="0" disabled="" hidden selected ><?= tr("Select role") ?></option>
									<option  value="<?= $role->id ?>"><?= escap($role->name) ?></option>
									<?php endforeach ?>
								</select>
							</div>
							 <div class="col-md-6">
								<label><?= tr("Password") ?> *</label>
								<div class="input-group">
									
									<input type="text" name="password" class="form-control" required>
									<div class="input-group-prepend">
										<button type="button" class="btn btn-light" onclick="$('input[name=password]').val(Math.random().toString(36).substring(2, 10))"><?= tr("Generate password") ?> <i class="fas fa-random"></i></button>
									</div>
								</div>
							</div> 
							
						</div>
						
						<br>
						
					</div>
					<hr>
					<button type="submit" name="new_admin" class="btn btn-primary rounded-pill"><?= tr("Submit") ?></button>
				</form>
			</div>
		</div>
	</div>
</div>
<?= $this->endSection() ?>