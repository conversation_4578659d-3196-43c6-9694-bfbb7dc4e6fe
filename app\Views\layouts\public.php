<!DOCTYPE html>
<html lang="<?= strtolower(get_local()) ?>" dir="<?= strtolower(get_local())=="ar"?'rtl':'ltr' ?>">
<?= view("inc/head.php") ?>

<body class="">
    <?= view("inc/header.public.php") ?>
    <div class="page-content ">

        <!-- Main content -->
        <div class="content-wrapper ">


            <div class="container pt-5">

                <div class="rounded-5 text-primary" style="">
                    <?php if (isset($nav)): ?>
                    <?php if (isset($nav['breadcrumb'])): ?>
                    <div class="page-title py-0 d-flex text-primary">
                        <h4 class="text-primary">
                            <a href="<?= base_url() ?>"><span class="font-weight-semibold text-primary"><i
                                        class="far fa-home fa-1x"></i></span></a> \

                            <?php $n=1; foreach ($nav['breadcrumb'] as $key => $value): ?>
                            <?php if ($n!=count($nav['breadcrumb'])): ?>

                            <a href="<?= $value ?>"><span class="font-weight-semibold text-primary"
                                    style="font-size: 1.2rem; "><?= ($key) ?></span></a> \
                            <?php else: ?>
                            <span class="font-weight-semibold text-muted"><?= ($key) ?></span>
                            <?php endif ?>


                            <?php $n++; endforeach ?>

                        </h4>
                        <a href="#" class="header-elements-toggle text-default d-none"><i class="icon-more"></i></a>
                    </div>
                    <?php endif ?>
                    <?php endif ?>
                </div>

                <br>
                <?= $this->renderSection('content') ?>

            </div>

        </div>
    </div>


    <?= view("inc/foot.php") ?>

    <?= $this->renderSection('script') ?>
</body>

</html>