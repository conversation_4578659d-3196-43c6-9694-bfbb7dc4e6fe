<?php

namespace App\Filters;

use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use CodeIgniter\Filters\FilterInterface;

class AuthFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // Check if user is logged in
        if (!session()->get('isLoggedIn')) {
            // Check if this is an AJAX request
            if ($request->hasHeader('X-Requested-With') && $request->getHeaderLine('X-Requested-With') === 'XMLHttpRequest') {
                return service('response')
                    ->setJSON(['error' => 'Authentication required', 'redirect' => base_url('auth/login')])
                    ->setStatusCode(401);
            }
            
            // For regular requests, redirect to login
            return redirect()->to(base_url('auth/login'))->with('message', 'Please log in to access this page.');
        }
        
        // User is authenticated, continue with request
        return null;
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No action needed after the request
        return null;
    }
}