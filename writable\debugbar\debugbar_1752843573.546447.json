{"url": "http://*************/ci_office/public/drive/onlyoffice/callback/a5c54696-e479-427e-9714-92b933731f38", "method": "POST", "isAJAX": false, "startTime": **********.786755, "totalTime": 615.9, "totalMemory": "11.538", "segmentDuration": 90, "segmentCount": 7, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.812118, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.899542, "duration": 0.021065950393676758}, {"name": "Routing", "component": "Timer", "start": **********.920625, "duration": 0.*****************}, {"name": "Before Filters", "component": "Timer", "start": **********.097392, "duration": 0.*****************}, {"name": "Controller", "component": "Timer", "start": **********.126094, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.126097, "duration": 0.****************}, {"name": "After Filters", "component": "Timer", "start": **********.395228, "duration": 0.005607128143310547}, {"name": "Required After Filters", "component": "Timer", "start": **********.400886, "duration": 0.0019049644470214844}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(0 total Query, 0  unique across 0 Connection)", "display": {"queries": []}, "badgeValue": 0, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(16): CodeIgniter\\Router\\RouteCollection->match([...], '/get_cities/(:any)', 'Home::get_cities/$1')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(16): CodeIgniter\\Router\\RouteCollection->match([...], '/get_cities/(:any)', 'Home::get_cities/$1')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(17): CodeIgniter\\Router\\RouteCollection->match([...], 'home/get_cities/(:any)', 'Home::get_cities/$1')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(17): CodeIgniter\\Router\\RouteCollection->match([...], 'home/get_cities/(:any)', 'Home::get_cities/$1')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(21): CodeIgniter\\Router\\RouteCollection->match([...], 'login', 'Auth::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(21): CodeIgniter\\Router\\RouteCollection->match([...], 'login', 'Auth::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection->match([...], 'register', 'Auth::register')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection->match([...], 'register', 'Auth::register')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(23): CodeIgniter\\Router\\RouteCollection->match([...], 'otp_login', 'Auth::otp_login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(23): CodeIgniter\\Router\\RouteCollection->match([...], 'otp_login', 'Auth::otp_login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(24): CodeIgniter\\Router\\RouteCollection->match([...], 'otp', 'Auth::otp')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(24): CodeIgniter\\Router\\RouteCollection->match([...], 'otp', 'Auth::otp')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(25): CodeIgniter\\Router\\RouteCollection->match([...], 'verify_otp', 'Auth::verify_otp')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(25): CodeIgniter\\Router\\RouteCollection->match([...], 'verify_otp', 'Auth::verify_otp')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(26): CodeIgniter\\Router\\RouteCollection->match([...], 'resend_otp', 'Auth::resend_otp')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(26): CodeIgniter\\Router\\RouteCollection->match([...], 'resend_otp', 'Auth::resend_otp')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(27): CodeIgniter\\Router\\RouteCollection->match([...], 'forgot_password', 'Auth::forgot_password')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(27): CodeIgniter\\Router\\RouteCollection->match([...], 'forgot_password', 'Auth::forgot_password')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(28): CodeIgniter\\Router\\RouteCollection->match([...], 'reset_password', 'Auth::reset_password')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(28): CodeIgniter\\Router\\RouteCollection->match([...], 'reset_password', 'Auth::reset_password')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(29): CodeIgniter\\Router\\RouteCollection->match([...], 'reset_password/(:any)', 'Auth::reset_password/$1')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(29): CodeIgniter\\Router\\RouteCollection->match([...], 'reset_password/(:any)', 'Auth::reset_password/$1')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(32): CodeIgniter\\Router\\RouteCollection->match([...], 'change_password', 'Auth::change_password')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(32): CodeIgniter\\Router\\RouteCollection->match([...], 'change_password', 'Auth::change_password')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(33): CodeIgniter\\Router\\RouteCollection->match([...], '2fa', 'Auth::two_factor')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(33): CodeIgniter\\Router\\RouteCollection->match([...], '2fa', 'Auth::two_factor')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(34): CodeIgniter\\Router\\RouteCollection->match([...], '2fa/enable', 'Auth::enable_2fa')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(34): CodeIgniter\\Router\\RouteCollection->match([...], '2fa/enable', 'Auth::enable_2fa')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(35): CodeIgniter\\Router\\RouteCollection->match([...], '2fa/disable', 'Auth::disable_2fa')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(35): CodeIgniter\\Router\\RouteCollection->match([...], '2fa/disable', 'Auth::disable_2fa')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(37): CodeIgniter\\Router\\RouteCollection->match([...], 'verify_2fa', 'Auth::verify_2fa')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(37): CodeIgniter\\Router\\RouteCollection->match([...], 'verify_2fa', 'Auth::verify_2fa')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(20): CodeIgniter\\Router\\RouteCollection->group('auth', [], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(58): CodeIgniter\\Router\\RouteCollection->match([...], 'logout', 'ProfileController::logout')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(51): CodeIgniter\\Router\\RouteCollection->group('profile', Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(58): CodeIgniter\\Router\\RouteCollection->match([...], 'logout', 'ProfileController::logout')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(51): CodeIgniter\\Router\\RouteCollection->group('profile', Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(97): CodeIgniter\\Router\\RouteCollection->match([...], 'setup_2fa', 'Users::setup_2fa')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(86): CodeIgniter\\Router\\RouteCollection->group('users', [...], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(97): CodeIgniter\\Router\\RouteCollection->match([...], 'setup_2fa', 'Users::setup_2fa')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(86): CodeIgniter\\Router\\RouteCollection->group('users', [...], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(104): CodeIgniter\\Router\\RouteCollection->match([...], '/', 'Settings::index')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(103): CodeIgniter\\Router\\RouteCollection->group('settings', [...], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(104): CodeIgniter\\Router\\RouteCollection->match([...], '/', 'Settings::index')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(103): CodeIgniter\\Router\\RouteCollection->group('settings', [...], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(109): CodeIgniter\\Router\\RouteCollection->match([...], '/', 'LanguageManager::index')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(108): CodeIgniter\\Router\\RouteCollection->group('language-manager', [...], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(109): CodeIgniter\\Router\\RouteCollection->match([...], '/', 'LanguageManager::index')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(108): CodeIgniter\\Router\\RouteCollection->group('language-manager', [...], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(110): CodeIgniter\\Router\\RouteCollection->match([...], 'datatable', 'LanguageManager::datatable')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(108): CodeIgniter\\Router\\RouteCollection->group('language-manager', [...], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(110): CodeIgniter\\Router\\RouteCollection->match([...], 'datatable', 'LanguageManager::datatable')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(108): CodeIgniter\\Router\\RouteCollection->group('language-manager', [...], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(117): CodeIgniter\\Router\\RouteCollection->match([...], 'datatable', 'Logs::datatable')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(115): CodeIgniter\\Router\\RouteCollection->group('logs', [...], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(117): CodeIgniter\\Router\\RouteCollection->match([...], 'datatable', 'Logs::datatable')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(799): CodeIgniter\\Router\\RouteCollection->{closure}(Object(CodeIgniter\\Router\\RouteCollection))\n 4 APPPATH\\Config\\Routes.php(115): CodeIgniter\\Router\\RouteCollection->group('logs', [...], Object(Closure))\n 5 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\ci_office\\\\app\\\\Config\\\\Routes.php')\n 6 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 7 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 8 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 9 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n10 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n11 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}, {"level": "info", "msg": "OnlyOffice callback received for file: a5c54696-e479-427e-9714-92b933731f38"}, {"level": "debug", "msg": "OnlyOffice callback method: POST"}, {"level": "debug", "msg": "OnlyOffice callback IP: *************"}, {"level": "debug", "msg": "OnlyOffice callback headers: {\"Content-Type\":{},\"Authorization\":{},\"Accept\":{},\"User-Agent\":{},\"Connection\":{},\"Accept-Encoding\":{},\"Host\":{}}"}, {"level": "debug", "msg": "OnlyOffice callback raw body: {\"key\":\"5588bc070b0537935c997e645c72d115\",\"status\":1,\"users\":[\"1\"],\"actions\":[{\"type\":1,\"userid\":\"1\"}],\"token\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXkiOiI1NTg4YmMwNzBiMDUzNzkzNWM5OTdlNjQ1YzcyZDExNSIsInN0YXR1cyI6MSwidXNlcnMiOlsiMSJdLCJhY3Rpb25zIjpbeyJ0eXBlIjoxLCJ1c2VyaWQiOiIxIn1dLCJpYXQiOjE3NTI4NDM1NzIsImV4cCI6MTc1Mjg0Mzg3Mn0.m4xfYDI1GRQ1Yb73ovur9qTDI571Ppm5TmOrKLlAFbM\"}"}, {"level": "debug", "msg": "OnlyOffice callback parsed data: {\"key\":\"5588bc070b0537935c997e645c72d115\",\"status\":1,\"users\":[\"1\"],\"actions\":[{\"type\":1,\"userid\":\"1\"}],\"token\":\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJrZXkiOiI1NTg4YmMwNzBiMDUzNzkzNWM5OTdlNjQ1YzcyZDExNSIsInN0YXR1cyI6MSwidXNlcnMiOlsiMSJdLCJhY3Rpb25zIjpbeyJ0eXBlIjoxLCJ1c2VyaWQiOiIxIn1dLCJpYXQiOjE3NTI4NDM1NzIsImV4cCI6MTc1Mjg0Mzg3Mn0.m4xfYDI1GRQ1Yb73ovur9qTDI571Ppm5TmOrKLlAFbM\"}"}, {"level": "debug", "msg": "OnlyOffice callback signature check - Secret configured, signature: Bearer eyJhb<PERSON>ciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXlsb2FkIjp7ImtleSI6IjU1ODhiYzA3MGIwNTM3OTM1Yzk5N2U2NDVjNzJkMTE1Iiwic3RhdHVzIjoxLCJ1c2VycyI6WyIxIl0sImFjdGlvbnMiOlt7InR5cGUiOjEsInVzZXJpZCI6IjEifV19LCJpYXQiOjE3NTI4NDM1NzIsImV4cCI6MTc1Mjg0Mzg3Mn0.p5eqLcLUmbCPyPLoBT_mUUCoGFJOI0SaIuvRVJ8sxwE"}, {"level": "debug", "msg": "OnlyOffice: Verifying JWT signature: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXlsb2FkI..."}, {"level": "debug", "msg": "OnlyOffice: JWT header: {\"alg\":\"HS256\",\"typ\":\"JWT\"}"}, {"level": "debug", "msg": "OnlyOffice: JWT payload: {\"payload\":{\"key\":\"5588bc070b0537935c997e645c72d115\",\"status\":1,\"users\":[\"1\"],\"actions\":[{\"type\":1,\"userid\":\"1\"}]},\"iat\":**********,\"exp\":1752843872}"}, {"level": "debug", "msg": "OnlyOffice: Expected signature: p5eqLcLUmbCPyPLoBT_mUUCoGFJOI0SaIuvRVJ8sxwE"}, {"level": "debug", "msg": "OnlyOffice: Received signature: p5eqLcLUmbCPyPLoBT_mUUCoGFJOI0SaIuvRVJ8sxwE"}, {"level": "debug", "msg": "OnlyOffice: Signature verification result: success"}, {"level": "debug", "msg": "OnlyOffice callback: Signature verification passed"}, {"level": "info", "msg": "OnlyOffice callback status: 1 for file: a5c54696-e479-427e-9714-92b933731f38"}, {"level": "debug", "msg": "OnlyOffice: Document is being edited"}, {"level": "debug", "msg": "OnlyOffice callback completed successfully for file: a5c54696-e479-427e-9714-92b933731f38"}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 267 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\Honeypot.php", "name": "Honeypot.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\Format\\Format.php", "name": "Format.php"}, {"path": "SYSTEMPATH\\Format\\FormatterInterface.php", "name": "FormatterInterface.php"}, {"path": "SYSTEMPATH\\Format\\JSONFormatter.php", "name": "JSONFormatter.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\form_helper.php", "name": "form_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\Honeypot\\Honeypot.php", "name": "Honeypot.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Language\\Language.php", "name": "Language.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Security\\Security.php", "name": "Security.php"}, {"path": "SYSTEMPATH\\Security\\SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Eloquent.php", "name": "Eloquent.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Format.php", "name": "Format.php"}, {"path": "APPPATH\\Config\\Honeypot.php", "name": "Honeypot.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Security.php", "name": "Security.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Controllers\\Drive\\OnlyOfficeController.php", "name": "OnlyOfficeController.php"}, {"path": "APPPATH\\Filters\\CSRFHeaderFilter.php", "name": "CSRFHeaderFilter.php"}, {"path": "APPPATH\\Filters\\LanguageFilter.php", "name": "LanguageFilter.php"}, {"path": "APPPATH\\Filters\\RateLimiterFilter.php", "name": "RateLimiterFilter.php"}, {"path": "APPPATH\\Helpers\\App_helpers.php", "name": "App_helpers.php"}, {"path": "APPPATH\\Helpers\\display_helper.php", "name": "display_helper.php"}, {"path": "APPPATH\\Helpers\\files_helper.php", "name": "files_helper.php"}, {"path": "APPPATH\\Helpers\\form_helper.php", "name": "form_helper.php"}, {"path": "APPPATH\\Helpers\\hook_helper.php", "name": "hook_helper.php"}, {"path": "APPPATH\\Helpers\\notify_helper.php", "name": "notify_helper.php"}, {"path": "APPPATH\\Helpers\\settings_helper.php", "name": "settings_helper.php"}, {"path": "APPPATH\\Helpers\\template_helper.php", "name": "template_helper.php"}, {"path": "APPPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "APPPATH\\Helpers\\uuid_helper.php", "name": "uuid_helper.php"}, {"path": "APPPATH\\Helpers\\validator_helper.php", "name": "validator_helper.php"}, {"path": "APPPATH\\Language\\ar\\ar.php", "name": "ar.php"}, {"path": "APPPATH\\Libraries\\Hooks.php", "name": "Hooks.php"}, {"path": "APPPATH\\Libraries\\Navigation.php", "name": "Navigation.php"}, {"path": "APPPATH\\Models\\Drive\\File.php", "name": "File.php"}, {"path": "APPPATH\\Models\\Setting.php", "name": "Setting.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "VENDORPATH\\autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH\\composer\\installed.php", "name": "installed.php"}, {"path": "VENDORPATH\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH\\illuminate\\collections\\Arr.php", "name": "Arr.php"}, {"path": "VENDORPATH\\illuminate\\collections\\Collection.php", "name": "Collection.php"}, {"path": "VENDORPATH\\illuminate\\collections\\Enumerable.php", "name": "Enumerable.php"}, {"path": "VENDORPATH\\illuminate\\collections\\Traits\\EnumeratesValues.php", "name": "EnumeratesValues.php"}, {"path": "VENDORPATH\\illuminate\\collections\\Traits\\TransformsToResourceCollection.php", "name": "TransformsToResourceCollection.php"}, {"path": "VENDORPATH\\illuminate\\collections\\functions.php", "name": "functions.php"}, {"path": "VENDORPATH\\illuminate\\collections\\helpers.php", "name": "helpers.php"}, {"path": "VENDORPATH\\illuminate\\conditionable\\Traits\\Conditionable.php", "name": "Conditionable.php"}, {"path": "VENDORPATH\\illuminate\\container\\Container.php", "name": "Container.php"}, {"path": "VENDORPATH\\illuminate\\contracts\\Broadcasting\\HasBroadcastChannel.php", "name": "HasBroadcastChannel.php"}, {"path": "VENDORPATH\\illuminate\\contracts\\Container\\Container.php", "name": "Container.php"}, {"path": "VENDORPATH\\illuminate\\contracts\\Database\\Eloquent\\Builder.php", "name": "Builder.php"}, {"path": "VENDORPATH\\illuminate\\contracts\\Database\\Query\\Builder.php", "name": "Builder.php"}, {"path": "VENDORPATH\\illuminate\\contracts\\Queue\\QueueableCollection.php", "name": "QueueableCollection.php"}, {"path": "VENDORPATH\\illuminate\\contracts\\Queue\\QueueableEntity.php", "name": "QueueableEntity.php"}, {"path": "VENDORPATH\\illuminate\\contracts\\Routing\\UrlRoutable.php", "name": "UrlRoutable.php"}, {"path": "VENDORPATH\\illuminate\\contracts\\Support\\Arrayable.php", "name": "Arrayable.php"}, {"path": "VENDORPATH\\illuminate\\contracts\\Support\\CanBeEscapedWhenCastToString.php", "name": "CanBeEscapedWhenCastToString.php"}, {"path": "VENDORPATH\\illuminate\\contracts\\Support\\Jsonable.php", "name": "Jsonable.php"}, {"path": "VENDORPATH\\illuminate\\database\\Capsule\\Manager.php", "name": "Manager.php"}, {"path": "VENDORPATH\\illuminate\\database\\Concerns\\BuildsQueries.php", "name": "BuildsQueries.php"}, {"path": "VENDORPATH\\illuminate\\database\\Concerns\\BuildsWhereDateClauses.php", "name": "BuildsWhereDateClauses.php"}, {"path": "VENDORPATH\\illuminate\\database\\Concerns\\CompilesJsonPaths.php", "name": "CompilesJsonPaths.php"}, {"path": "VENDORPATH\\illuminate\\database\\Concerns\\ExplainsQueries.php", "name": "ExplainsQueries.php"}, {"path": "VENDORPATH\\illuminate\\database\\Concerns\\ManagesTransactions.php", "name": "ManagesTransactions.php"}, {"path": "VENDORPATH\\illuminate\\database\\Connection.php", "name": "Connection.php"}, {"path": "VENDORPATH\\illuminate\\database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "VENDORPATH\\illuminate\\database\\ConnectionResolverInterface.php", "name": "ConnectionResolverInterface.php"}, {"path": "VENDORPATH\\illuminate\\database\\Connectors\\ConnectionFactory.php", "name": "ConnectionFactory.php"}, {"path": "VENDORPATH\\illuminate\\database\\Connectors\\Connector.php", "name": "Connector.php"}, {"path": "VENDORPATH\\illuminate\\database\\Connectors\\ConnectorInterface.php", "name": "ConnectorInterface.php"}, {"path": "VENDORPATH\\illuminate\\database\\Connectors\\MySqlConnector.php", "name": "MySqlConnector.php"}, {"path": "VENDORPATH\\illuminate\\database\\DatabaseManager.php", "name": "DatabaseManager.php"}, {"path": "VENDORPATH\\illuminate\\database\\DetectsConcurrencyErrors.php", "name": "DetectsConcurrencyErrors.php"}, {"path": "VENDORPATH\\illuminate\\database\\DetectsLostConnections.php", "name": "DetectsLostConnections.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Builder.php", "name": "Builder.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Collection.php", "name": "Collection.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Concerns\\GuardsAttributes.php", "name": "GuardsAttributes.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Concerns\\HasAttributes.php", "name": "HasAttributes.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Concerns\\HasEvents.php", "name": "HasEvents.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Concerns\\HasGlobalScopes.php", "name": "HasGlobalScopes.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Concerns\\HasRelationships.php", "name": "HasRelationships.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Concerns\\HasTimestamps.php", "name": "HasTimestamps.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Concerns\\HasUniqueIds.php", "name": "HasUniqueIds.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Concerns\\HidesAttributes.php", "name": "HidesAttributes.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Concerns\\PreventsCircularRecursion.php", "name": "PreventsCircularRecursion.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Concerns\\QueriesRelationships.php", "name": "QueriesRelationships.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Concerns\\TransformsToResource.php", "name": "TransformsToResource.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\HasCollection.php", "name": "HasCollection.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Model.php", "name": "Model.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Relations\\Concerns\\InteractsWithDictionary.php", "name": "InteractsWithDictionary.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\Scope.php", "name": "Scope.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\SoftDeletes.php", "name": "SoftDeletes.php"}, {"path": "VENDORPATH\\illuminate\\database\\Eloquent\\SoftDeletingScope.php", "name": "SoftDeletingScope.php"}, {"path": "VENDORPATH\\illuminate\\database\\Events\\QueryExecuted.php", "name": "QueryExecuted.php"}, {"path": "VENDORPATH\\illuminate\\database\\Events\\StatementPrepared.php", "name": "StatementPrepared.php"}, {"path": "VENDORPATH\\illuminate\\database\\Grammar.php", "name": "Grammar.php"}, {"path": "VENDORPATH\\illuminate\\database\\MySqlConnection.php", "name": "MySqlConnection.php"}, {"path": "VENDORPATH\\illuminate\\database\\Query\\Builder.php", "name": "Builder.php"}, {"path": "VENDORPATH\\illuminate\\database\\Query\\Grammars\\Grammar.php", "name": "Grammar.php"}, {"path": "VENDORPATH\\illuminate\\database\\Query\\Grammars\\MySqlGrammar.php", "name": "MySqlGrammar.php"}, {"path": "VENDORPATH\\illuminate\\database\\Query\\Processors\\MySqlProcessor.php", "name": "MySqlProcessor.php"}, {"path": "VENDORPATH\\illuminate\\database\\Query\\Processors\\Processor.php", "name": "Processor.php"}, {"path": "VENDORPATH\\illuminate\\macroable\\Traits\\Macroable.php", "name": "Macroable.php"}, {"path": "VENDORPATH\\illuminate\\support\\ConfigurationUrlParser.php", "name": "ConfigurationUrlParser.php"}, {"path": "VENDORPATH\\illuminate\\support\\Fluent.php", "name": "Fluent.php"}, {"path": "VENDORPATH\\illuminate\\support\\InteractsWithTime.php", "name": "InteractsWithTime.php"}, {"path": "VENDORPATH\\illuminate\\support\\Str.php", "name": "Str.php"}, {"path": "VENDORPATH\\illuminate\\support\\Traits\\CapsuleManagerTrait.php", "name": "CapsuleManagerTrait.php"}, {"path": "VENDORPATH\\illuminate\\support\\Traits\\ForwardsCalls.php", "name": "ForwardsCalls.php"}, {"path": "VENDORPATH\\illuminate\\support\\Traits\\InteractsWithData.php", "name": "InteractsWithData.php"}, {"path": "VENDORPATH\\illuminate\\support\\functions.php", "name": "functions.php"}, {"path": "VENDORPATH\\illuminate\\support\\helpers.php", "name": "helpers.php"}, {"path": "VENDORPATH\\kint-php\\kint\\init.php", "name": "init.php"}, {"path": "VENDORPATH\\kint-php\\kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "VENDORPATH\\kint-php\\kint\\src\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "VENDORPATH\\kint-php\\kint\\src\\Kint.php", "name": "Kint.php"}, {"path": "VENDORPATH\\kint-php\\kint\\src\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "VENDORPATH\\kint-php\\kint\\src\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "VENDORPATH\\kint-php\\kint\\src\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "VENDORPATH\\kint-php\\kint\\src\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "VENDORPATH\\kint-php\\kint\\src\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "VENDORPATH\\kint-php\\kint\\src\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "VENDORPATH\\kint-php\\kint\\src\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "VENDORPATH\\kint-php\\kint\\src\\Utils.php", "name": "Utils.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH\\psr\\container\\src\\ContainerInterface.php", "name": "ContainerInterface.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH\\rakit\\validation\\src\\Rule.php", "name": "Rule.php"}, {"path": "VENDORPATH\\react\\promise\\src\\functions.php", "name": "functions.php"}, {"path": "VENDORPATH\\react\\promise\\src\\functions_include.php", "name": "functions_include.php"}, {"path": "VENDORPATH\\symfony\\clock\\Resources\\now.php", "name": "now.php"}, {"path": "VENDORPATH\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}, {"path": "VENDORPATH\\symfony\\polyfill-ctype\\bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH\\symfony\\polyfill-ctype\\bootstrap80.php", "name": "bootstrap80.php"}, {"path": "VENDORPATH\\symfony\\polyfill-intl-grapheme\\bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH\\symfony\\polyfill-intl-normalizer\\bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH\\symfony\\polyfill-intl-normalizer\\bootstrap80.php", "name": "bootstrap80.php"}, {"path": "VENDORPATH\\symfony\\polyfill-mbstring\\bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH\\symfony\\polyfill-mbstring\\bootstrap80.php", "name": "bootstrap80.php"}, {"path": "VENDORPATH\\symfony\\polyfill-php80\\bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH\\symfony\\polyfill-php81\\bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH\\symfony\\polyfill-php83\\bootstrap.php", "name": "bootstrap.php"}, {"path": "VENDORPATH\\symfony\\polyfill-php83\\bootstrap81.php", "name": "bootstrap81.php"}, {"path": "VENDORPATH\\symfony\\string\\Resources\\functions.php", "name": "functions.php"}, {"path": "VENDORPATH\\symfony\\translation\\Resources\\functions.php", "name": "functions.php"}]}, "badgeValue": 267, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Drive\\OnlyOfficeController", "method": "callback", "paramCount": 1, "truePCount": 1, "params": [{"name": "$fileId = ", "value": "a5c54696-e479-427e-9714-92b933731f38"}]}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "job/(.*)", "handler": "\\App\\Controllers\\Home::job_details/$1"}, {"method": "GET", "route": "proceed/(.*)", "handler": "\\App\\Controllers\\Home::proceed/$1"}, {"method": "GET", "route": "profile_preview", "handler": "\\App\\Controllers\\Home::profile_preview"}, {"method": "GET", "route": "success", "handler": "\\App\\Controllers\\Home::application_success"}, {"method": "GET", "route": "get_cities/(.*)", "handler": "\\App\\Controllers\\Home::get_cities/$1"}, {"method": "GET", "route": "home/get_cities/(.*)", "handler": "\\App\\Controllers\\Home::get_cities/$1"}, {"method": "GET", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "GET", "route": "auth/register", "handler": "\\App\\Controllers\\Auth::register"}, {"method": "GET", "route": "auth/otp_login", "handler": "\\App\\Controllers\\Auth::otp_login"}, {"method": "GET", "route": "auth/otp", "handler": "\\App\\Controllers\\Auth::otp"}, {"method": "GET", "route": "auth/verify_otp", "handler": "\\App\\Controllers\\Auth::verify_otp"}, {"method": "GET", "route": "auth/resend_otp", "handler": "\\App\\Controllers\\Auth::resend_otp"}, {"method": "GET", "route": "auth/forgot_password", "handler": "\\App\\Controllers\\Auth::forgot_password"}, {"method": "GET", "route": "auth/reset_password", "handler": "\\App\\Controllers\\Auth::reset_password"}, {"method": "GET", "route": "auth/reset_password/(.*)", "handler": "\\App\\Controllers\\Auth::reset_password/$1"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/profile", "handler": "\\App\\Controllers\\Auth::profile"}, {"method": "GET", "route": "auth/change_password", "handler": "\\App\\Controllers\\Auth::change_password"}, {"method": "GET", "route": "auth/2fa", "handler": "\\App\\Controllers\\Auth::two_factor"}, {"method": "GET", "route": "auth/2fa/enable", "handler": "\\App\\Controllers\\Auth::enable_2fa"}, {"method": "GET", "route": "auth/2fa/disable", "handler": "\\App\\Controllers\\Auth::disable_2fa"}, {"method": "GET", "route": "auth/verify_email/(.*)", "handler": "\\App\\Controllers\\Auth::verify_email/$1"}, {"method": "GET", "route": "auth/verify_2fa", "handler": "\\App\\Controllers\\Auth::verify_2fa"}, {"method": "GET", "route": "dashboard", "handler": "\\App\\Controllers\\Dashboard::index"}, {"method": "GET", "route": "files/(.*)", "handler": "\\App\\Controllers\\Files::index/$1"}, {"method": "GET", "route": "profile", "handler": "\\App\\Controllers\\ProfileController::index"}, {"method": "GET", "route": "profile/login", "handler": "\\App\\Controllers\\ProfileController::login"}, {"method": "GET", "route": "profile/otp", "handler": "\\App\\Controllers\\ProfileController::otp"}, {"method": "GET", "route": "profile/logout", "handler": "\\App\\Controllers\\ProfileController::logout"}, {"method": "GET", "route": "profile/edit", "handler": "\\App\\Controllers\\ProfileController::edit"}, {"method": "GET", "route": "profile/delete_qualification/(.*)", "handler": "\\App\\Controllers\\ProfileController::delete_qualification/$1"}, {"method": "GET", "route": "profile/delete_experience/(.*)", "handler": "\\App\\Controllers\\ProfileController::delete_experience/$1"}, {"method": "GET", "route": "profile/delete_cert/(.*)", "handler": "\\App\\Controllers\\ProfileController::delete_cert/$1"}, {"method": "GET", "route": "roles", "handler": "\\App\\Controllers\\Roles::index"}, {"method": "GET", "route": "roles/create", "handler": "\\App\\Controllers\\Roles::create"}, {"method": "GET", "route": "roles/update/([0-9]+)", "handler": "\\App\\Controllers\\Roles::update/$1"}, {"method": "GET", "route": "roles/delete/([0-9]+)", "handler": "\\App\\Controllers\\Roles::delete/$1"}, {"method": "GET", "route": "users", "handler": "\\App\\Controllers\\Users::index"}, {"method": "GET", "route": "users/new", "handler": "\\App\\Controllers\\Users::new"}, {"method": "GET", "route": "users/view/([0-9]+)", "handler": "\\App\\Controllers\\Users::view/$1"}, {"method": "GET", "route": "users/reset_password/([0-9]+)", "handler": "\\App\\Controllers\\Users::reset_password/$1"}, {"method": "GET", "route": "users/delete/([0-9]+)", "handler": "\\App\\Controllers\\Users::delete/$1"}, {"method": "GET", "route": "users/profile", "handler": "\\App\\Controllers\\Users::profile"}, {"method": "GET", "route": "users/setup_2fa", "handler": "\\App\\Controllers\\Users::setup_2fa"}, {"method": "GET", "route": "users/disable_2fa", "handler": "\\App\\Controllers\\Users::disable_2fa"}, {"method": "GET", "route": "users/send_reset_password", "handler": "\\App\\Controllers\\Users::send_reset_password"}, {"method": "GET", "route": "settings", "handler": "\\App\\Controllers\\Settings::index"}, {"method": "GET", "route": "language-manager", "handler": "\\App\\Controllers\\LanguageManager::index"}, {"method": "GET", "route": "language-manager/datatable", "handler": "\\App\\Controllers\\LanguageManager::datatable"}, {"method": "GET", "route": "logs", "handler": "\\App\\Controllers\\Logs::index"}, {"method": "GET", "route": "logs/datatable", "handler": "\\App\\Controllers\\Logs::datatable"}, {"method": "GET", "route": "drive/onlyoffice/callback/(.*)", "handler": "\\App\\Controllers\\Drive\\OnlyOfficeController::callback/$1"}, {"method": "GET", "route": "drive/onlyoffice/test-callback", "handler": "\\App\\Controllers\\Drive\\OnlyOfficeController::testCallback"}, {"method": "GET", "route": "drive/onlyoffice/download/(.*)", "handler": "\\App\\Controllers\\Drive\\OnlyOfficeController::download/$1"}, {"method": "GET", "route": "drive/directories/(.*)/contents", "handler": "\\App\\Controllers\\Drive\\DirectoryController::contents/$1"}, {"method": "GET", "route": "drive/files/(.*)/download", "handler": "\\App\\Controllers\\Drive\\FileController::download/$1"}, {"method": "GET", "route": "drive/files/(.*)/view", "handler": "\\App\\Controllers\\Drive\\FileController::view/$1"}, {"method": "GET", "route": "drive/files/(.*)/versions/(.*)/download", "handler": "\\App\\Controllers\\Drive\\FileController::downloadVersion/$1/$2"}, {"method": "GET", "route": "drive/share/list", "handler": "\\App\\Controllers\\Drive\\ShareController::getShares"}, {"method": "GET", "route": "drive/onlyoffice/edit/(.*)", "handler": "\\App\\Controllers\\Drive\\OnlyOfficeController::edit/$1"}, {"method": "GET", "route": "drive/onlyoffice/config", "handler": "\\App\\Controllers\\Drive\\OnlyOfficeController::config"}, {"method": "GET", "route": "drive/onlyoffice/debug/(.*)", "handler": "\\App\\Controllers\\Drive\\OnlyOfficeController::debugConfig/$1"}, {"method": "GET", "route": "drive", "handler": "\\App\\Controllers\\Drive\\DriveController::index"}, {"method": "GET", "route": "drive/shared", "handler": "\\App\\Controllers\\Drive\\DriveController::shared"}, {"method": "GET", "route": "drive/recent", "handler": "\\App\\Controllers\\Drive\\DriveController::recent"}, {"method": "GET", "route": "drive/(.*)", "handler": "\\App\\Controllers\\Drive\\DriveController::index/$1"}, {"method": "GET", "route": "drive/shared/(.*)", "handler": "\\App\\Controllers\\Drive\\ShareController::accessShared/$1"}, {"method": "GET", "route": "tt/sms", "handler": "\\App\\Controllers\\Tt::sms"}, {"method": "GET", "route": "tt/email", "handler": "\\App\\Controllers\\Tt::email"}, {"method": "POST", "route": "confirm_application", "handler": "\\App\\Controllers\\Home::confirm_application"}, {"method": "POST", "route": "get_cities/(.*)", "handler": "\\App\\Controllers\\Home::get_cities/$1"}, {"method": "POST", "route": "home/get_cities/(.*)", "handler": "\\App\\Controllers\\Home::get_cities/$1"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "POST", "route": "auth/register", "handler": "\\App\\Controllers\\Auth::register"}, {"method": "POST", "route": "auth/otp_login", "handler": "\\App\\Controllers\\Auth::otp_login"}, {"method": "POST", "route": "auth/otp", "handler": "\\App\\Controllers\\Auth::otp"}, {"method": "POST", "route": "auth/verify_otp", "handler": "\\App\\Controllers\\Auth::verify_otp"}, {"method": "POST", "route": "auth/resend_otp", "handler": "\\App\\Controllers\\Auth::resend_otp"}, {"method": "POST", "route": "auth/forgot_password", "handler": "\\App\\Controllers\\Auth::forgot_password"}, {"method": "POST", "route": "auth/reset_password", "handler": "\\App\\Controllers\\Auth::reset_password"}, {"method": "POST", "route": "auth/reset_password/(.*)", "handler": "\\App\\Controllers\\Auth::reset_password/$1"}, {"method": "POST", "route": "auth/change_password", "handler": "\\App\\Controllers\\Auth::change_password"}, {"method": "POST", "route": "auth/2fa", "handler": "\\App\\Controllers\\Auth::two_factor"}, {"method": "POST", "route": "auth/2fa/enable", "handler": "\\App\\Controllers\\Auth::enable_2fa"}, {"method": "POST", "route": "auth/2fa/disable", "handler": "\\App\\Controllers\\Auth::disable_2fa"}, {"method": "POST", "route": "auth/verify_2fa", "handler": "\\App\\Controllers\\Auth::verify_2fa"}, {"method": "POST", "route": "data/export/(.*)", "handler": "\\App\\Controllers\\Data::export/$1"}, {"method": "POST", "route": "profile/login", "handler": "\\App\\Controllers\\ProfileController::login"}, {"method": "POST", "route": "profile/otp", "handler": "\\App\\Controllers\\ProfileController::otp"}, {"method": "POST", "route": "profile/resend_otp", "handler": "\\App\\Controllers\\ProfileController::resend_otp"}, {"method": "POST", "route": "profile/logout", "handler": "\\App\\Controllers\\ProfileController::logout"}, {"method": "POST", "route": "profile/edit", "handler": "\\App\\Controllers\\ProfileController::edit"}, {"method": "POST", "route": "profile/upload_image", "handler": "\\App\\Controllers\\ProfileController::upload_image"}, {"method": "POST", "route": "profile/create_qualification", "handler": "\\App\\Controllers\\ProfileController::create_qualification"}, {"method": "POST", "route": "profile/create_experience", "handler": "\\App\\Controllers\\ProfileController::create_experience"}, {"method": "POST", "route": "profile/create_cert", "handler": "\\App\\Controllers\\ProfileController::create_cert"}, {"method": "POST", "route": "roles", "handler": "\\App\\Controllers\\Roles::index"}, {"method": "POST", "route": "roles/create", "handler": "\\App\\Controllers\\Roles::create"}, {"method": "POST", "route": "roles/update/([0-9]+)", "handler": "\\App\\Controllers\\Roles::update/$1"}, {"method": "POST", "route": "users/new", "handler": "\\App\\Controllers\\Users::new"}, {"method": "POST", "route": "users/view/([0-9]+)", "handler": "\\App\\Controllers\\Users::view/$1"}, {"method": "POST", "route": "users/setup_2fa", "handler": "\\App\\Controllers\\Users::setup_2fa"}, {"method": "POST", "route": "users/confirm_2fa", "handler": "\\App\\Controllers\\Users::confirm_2fa"}, {"method": "POST", "route": "settings", "handler": "\\App\\Controllers\\Settings::index"}, {"method": "POST", "route": "language-manager", "handler": "\\App\\Controllers\\LanguageManager::index"}, {"method": "POST", "route": "language-manager/datatable", "handler": "\\App\\Controllers\\LanguageManager::datatable"}, {"method": "POST", "route": "language-manager/save", "handler": "\\App\\Controllers\\LanguageManager::save"}, {"method": "POST", "route": "logs/datatable", "handler": "\\App\\Controllers\\Logs::datatable"}, {"method": "POST", "route": "drive/onlyoffice/callback/(.*)", "handler": "\\App\\Controllers\\Drive\\OnlyOfficeController::callback/$1"}, {"method": "POST", "route": "drive/directories/create", "handler": "\\App\\Controllers\\Drive\\DirectoryController::create"}, {"method": "POST", "route": "drive/directories/(.*)/rename", "handler": "\\App\\Controllers\\Drive\\DirectoryController::rename/$1"}, {"method": "POST", "route": "drive/directories/(.*)/delete", "handler": "\\App\\Controllers\\Drive\\DirectoryController::delete/$1"}, {"method": "POST", "route": "drive/directories/(.*)/move", "handler": "\\App\\Controllers\\Drive\\DirectoryController::move/$1"}, {"method": "POST", "route": "drive/files/upload", "handler": "\\App\\Controllers\\Drive\\FileController::upload"}, {"method": "POST", "route": "drive/files/(.*)/delete", "handler": "\\App\\Controllers\\Drive\\FileController::delete/$1"}, {"method": "POST", "route": "drive/files/(.*)/rename", "handler": "\\App\\Controllers\\Drive\\FileController::rename/$1"}, {"method": "POST", "route": "drive/files/(.*)/move", "handler": "\\App\\Controllers\\Drive\\FileController::move/$1"}, {"method": "POST", "route": "drive/files/(.*)/copy", "handler": "\\App\\Controllers\\Drive\\FileController::copy/$1"}, {"method": "POST", "route": "drive/share/file/(.*)", "handler": "\\App\\Controllers\\Drive\\ShareController::shareFile/$1"}, {"method": "POST", "route": "drive/share/directory/(.*)", "handler": "\\App\\Controllers\\Drive\\ShareController::shareDirectory/$1"}, {"method": "POST", "route": "drive/share/(.*)/revoke", "handler": "\\App\\Controllers\\Drive\\ShareController::revokeShare/$1"}, {"method": "POST", "route": "drive/share/(.*)/update", "handler": "\\App\\Controllers\\Drive\\ShareController::updateShare/$1"}, {"method": "POST", "route": "drive/onlyoffice/config/save", "handler": "\\App\\Controllers\\Drive\\OnlyOfficeController::saveConfig"}, {"method": "POST", "route": "drive/onlyoffice/test-connection", "handler": "\\App\\Controllers\\Drive\\OnlyOfficeController::testConnection"}, {"method": "POST", "route": "drive/search", "handler": "\\App\\Controllers\\Drive\\DriveController::search"}, {"method": "OPTIONS", "route": "drive/onlyoffice/callback/(.*)", "handler": "\\App\\Controllers\\Drive\\OnlyOfficeController::callback/$1"}, {"method": "OPTIONS", "route": "drive/onlyoffice/test-callback", "handler": "\\App\\Controllers\\Drive\\OnlyOfficeController::testCallback"}]}, "badgeValue": 56, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "40.80", "count": 1}}}, "badgeValue": 1, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.858723, "duration": 0.040804147720336914}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>**********</pre>", "locale": "ar", "unique_id": "149c6c4bdfa715c3e07af99634fc2144", "_csrf": "6965e016634dfa62f51dd02992c80a62"}, "headers": {"Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXlsb2FkIjp7ImtleSI6IjU1ODhiYzA3MGIwNTM3OTM1Yzk5N2U2NDVjNzJkMTE1Iiwic3RhdHVzIjoxLCJ1c2VycyI6WyIxIl0sImFjdGlvbnMiOlt7InR5cGUiOjEsInVzZXJpZCI6IjEifV19LCJpYXQiOjE3NTI4NDM1NzIsImV4cCI6MTc1Mjg0Mzg3Mn0.p5eqLcLUmbCPyPLoBT_mUUCoGFJOI0SaIuvRVJ8sxwE", "Accept": "application/json, text/plain, */*", "User-Agent": "Node.js/6.13", "Connection": "Keep-Alive", "Accept-Encoding": "gzip, deflate, br", "Host": "*************"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "application/json; charset=UTF-8", "headers": {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET, POST, OPTIONS", "Access-Control-Allow-Headers": "Authorization, Content-Type", "Content-Type": "application/json; charset=UTF-8", "_csrf": "207047e4fd96415c3a6b0f3a0d17ea8f4915a7f29edbbb3ecf76df139fdfe0ed"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "apache2handler", "environment": "development", "baseURL": "http://*************/ci_office/public", "timezone": "Asia/Muscat", "locale": "ar", "cspEnabled": false}}