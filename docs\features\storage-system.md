# Storage System Documentation

## Overview
The storage system handles file uploads and management for the Career Application System. It supports both local storage and FTP-based remote storage with automatic failover capabilities.

## Architecture

### Storage Library (`app/Libraries/Storage.php`)
The Storage class provides a unified interface for file operations with support for:
- **Local Storage**: Files stored in `writable/uploads/` directory
- **FTP Storage**: Remote file storage with FTP server integration
- **Hybrid Mode**: Automatic fallback between storage methods

### Key Features
- File upload with validation
- Duplicate file handling with timestamp-based naming
- Extension filtering and size limits
- Database tracking of uploaded files
- URL generation for file access

## Configuration Options

### Environment Variables
```env
# Storage Configuration
storage.path = C:\xampp\htdocs\career\writable\uploads
storage.ftp_username = 'storage'
storage.ftp_password = 'storage@99'
storage.ftp_server = '*************'
storage.extensions = 'pdf,jpeg,png,jpg'
storage.max_size = '2M'
storage.use_ftp = '1'
```

### Database Options (Manageable via Settings)
- **storage_path**: Local storage directory path
- **storage_ftp_server**: FTP server hostname/IP
- **storage_ftp_username**: FTP authentication username
- **storage_ftp_password**: FTP authentication password
- **storage_extensions**: Allowed file extensions (comma-separated)
- **storage_max_size**: Maximum file size (e.g., '2M', '10MB')
- **storage_use_ftp**: Enable/disable FTP storage (0/1)

## Implementation Details

### File Upload Process
1. **Validation**: Check file type, size, and upload errors
2. **Naming**: Generate unique filename with timestamp and random number
3. **Storage**: Save to local directory or upload via FTP
4. **Database**: Create File model record with original and stored names
5. **Response**: Return storage status, file ID, and any errors

### Storage Methods

#### Local Storage
```php
if (!move_uploaded_file($file['tmp_name'], $target)) {
    $error = true;
    $errors[] = tr('Failed to move uploaded file.');
}
```

#### FTP Storage
```php
$ftp_conn = $this->initializeFTP();
ftp_pasv($ftp_conn, true);
if (!ftp_put($ftp_conn, $target, $file['tmp_name'], FTP_BINARY)) {
    $error = true;
    $errors[] = tr('Failed to upload file to FTP.');
}
```

### File Retrieval
The `get()` method supports:
- **Numeric ID**: Retrieve by File model ID
- **Filename**: Retrieve by stored filename
- **URL Generation**: Return accessible URL for file access

## Settings Integration

### Validation Rules
```php
// Storage Configuration
"storage_path" =>["storage_path","required|max:500"],
"storage_ftp_server" =>["storage_ftp_server","max:200"],
"storage_ftp_username" =>["storage_ftp_username","max:100"],
"storage_ftp_password" =>["storage_ftp_password","max:100"],
"storage_extensions" =>["storage_extensions","required|max:200"],
"storage_max_size" =>["storage_max_size","required|max:10"],
"storage_use_ftp" =>["storage_use_ftp","required|in:0,1"],
```

### Status Monitoring
The Settings controller includes real-time storage status checks:

#### Local Storage Status
- Directory existence verification
- Write permission validation
- Accessibility confirmation

#### FTP Storage Status
- Connection testing
- Authentication verification
- Extension availability check

## API Reference

### Storage Library Methods

#### `save($file, $public=0)`
**Purpose**: Upload and store a file
**Parameters**: 
- `$file`: PHP upload file array
- `$public`: Public access flag (default: 0)
**Returns**: Storage object with status information

#### `get($file, $type="")`
**Purpose**: Retrieve file URL by ID or filename
**Parameters**:
- `$file`: File ID (numeric) or filename (string)
- `$type`: File type hint (optional)
**Returns**: File URL or false if not found

#### `read($remoteFilePath)`
**Purpose**: Read file content from FTP storage
**Parameters**: 
- `$remoteFilePath`: Remote file path
**Returns**: File content string or false

#### Status Methods
- `saved()`: Returns boolean upload success status
- `errors()`: Returns array of error messages

### HTML Input Generation
```php
$storage = new Storage();
echo $storage->input([
    'name' => 'document',
    'id' => 'doc-upload',
    'accept' => '.pdf,.doc,.docx'
]);
```

## Security Considerations

### File Validation
- Extension whitelist enforcement
- MIME type verification
- File size limits
- Upload error handling

### Access Control
- Database-tracked file access
- URL encoding for security
- FTP credential protection

### Error Handling
- Graceful fallback between storage methods
- Detailed error logging
- User-friendly error messages

## Troubleshooting

### Common Issues

#### Permission Errors
- Verify `writable/uploads/` directory permissions (755 or 777)
- Check web server user access rights
- Ensure FTP user has upload permissions

#### FTP Connection Issues
- Verify FTP server accessibility
- Check firewall settings for FTP ports
- Validate FTP credentials
- Ensure passive mode compatibility

#### File Size Limits
- Check PHP `upload_max_filesize` setting
- Verify `post_max_size` configuration
- Review web server upload limits
- Validate storage quota availability

### Debug Information
Access storage status via Settings > System Status > Storage Configuration for:
- Path accessibility status
- FTP connection status
- Extension availability
- Configuration values

## Integration Examples

### Form Upload
```html
<!-- File upload form -->
<form method="post" enctype="multipart/form-data">
    <?= (new \App\Libraries\Storage())->input(['name' => 'resume']) ?>
    <button type="submit">Upload Resume</button>
</form>
```

### Controller Processing
```php
if (isset($_FILES['resume'])) {
    $storage = new \App\Libraries\Storage();
    $result = $storage->save($_FILES['resume']);
    
    if ($result->saved()) {
        $fileId = $result->id;
        // Save file ID to application record
    } else {
        $errors = $result->errors();
        // Handle upload errors
    }
}
```

### File Display
```php
$storage = new \App\Libraries\Storage();
$fileUrl = $storage->get($fileId);
if ($fileUrl) {
    echo '<a href="' . $fileUrl . '" target="_blank">View File</a>';
}
```

## Migration Notes

### From Local to FTP Storage
1. Enable FTP in settings: `storage_use_ftp = 1`
2. Configure FTP credentials
3. Test connection via Settings page
4. Migrate existing files if needed

### From FTP to Local Storage
1. Disable FTP in settings: `storage_use_ftp = 0`
2. Ensure local directory permissions
3. Download FTP files if required
4. Update file URLs in database

## Performance Optimization

### Recommendations
- Use FTP passive mode for better connectivity
- Implement file caching for frequently accessed files
- Monitor disk space usage
- Regular cleanup of temporary files
- Consider CDN integration for public files

### Monitoring
- Track upload success/failure rates
- Monitor storage space usage
- Log FTP connection issues
- Performance metrics for file operations

## Future Enhancements

### Planned Features
- **Cloud Storage Integration**: AWS S3, Google Cloud support
- **Image Processing**: Automatic resizing and optimization
- **File Versioning**: Multiple versions of uploaded files
- **Bulk Operations**: Mass file management capabilities
- **Advanced Security**: Virus scanning integration 