<?php 


class Template
{
    protected $_string;
    protected $_data = array();

    public function __construct($string = null)
    {
        $this->_string = $string;
    }

    public function set($kys, $value="")
    {
        
        $kys = json_decode(json_encode($kys),true);

        if (is_array($kys)) {
            foreach ($kys as $key => $value) {
                $this->_data[$key] = $value;
            }

        } elseif (is_object($kys)) {
            foreach (get_object_vars($kys) as $key => $value) {
                $this->_data[$key] = $value;
            }
    
        }


        
        return $this;
    }

    public function __set($key, $value)
    {
        $this->_data[$key] = $value;
    }

    private function get_vars($string) {
        $regex = '/\\{[^}]*\\}/i';
        preg_match_all($regex, $string,$matches);

        return $matches;
    }

    public function render()
    {
        extract($this->_data);
        $vars = $this->get_vars($this->_string);
       
        foreach ($vars[0] as $key => $value) {

            $var_name = str_replace("{", '', $value);
            $var_name = str_replace("}", '', $var_name);
            if(isset($$var_name)){
                $this->_string = str_replace($value, $$var_name, $this->_string);
            }else{
                $this->_string = str_replace($value, '', $this->_string);
            }
            
        }
        return $this->_string;
    }
}



function template($string=""){
    return new Template($string);
}

