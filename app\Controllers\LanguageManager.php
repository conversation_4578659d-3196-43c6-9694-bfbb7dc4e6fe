<?php

namespace App\Controllers;

use Exception;

class LanguageManager extends BaseController
{
    private $arFilePath;
    private $untranslatedFilePath;
    private $backupDir;

    function __construct()
    {
        $this->d['nav']['active'] = "language-manager";
        $this->arFilePath = APPPATH . 'Language/ar/ar.php';
        $this->untranslatedFilePath = APPPATH . 'Language/ar_untranslated.php';
        $this->backupDir = WRITEPATH . 'language_backups/';
        
        // Create backup directory if it doesn't exist
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }

    public function index()
    {
        if (!_can("settings")) {
            return _error_code(403);
        }

        if (is_post()) {
            return $this->handleFormSubmission();
        }

        $this->d['page']['title'] = tr("Language Management");
        $this->d['nav'] = [
            "reload" => true,
            "breadcrumb" => [
                tr("Dashboard") => base_url("dashboard"),
                tr("Language Management") => "",
            ],
            "active" => "language-manager"
        ];

        // Load translations data
        $this->d['translations'] = $this->getTranslationsData();
        $this->d['statistics'] = $this->getStatistics();
        $this->d['duplicates'] = $this->findDuplicates();

        return view("language_manager/index", $this->d);
    }

    public function datatable()
    {
        if (!_can("settings")) {
            return _error_code(403);
        }

        $translations = $this->getTranslationsData();
        
        // Process for DataTable
        $data = [];
                 foreach ($translations as $key => $translation) {
             $data[] = [
                 'key' => $key,
                 'translated' => $translation['translated'] ?? '',
                 'untranslated' => $translation['untranslated'] ?? '',
                 'source' => $translation['source'],
                 'is_duplicate' => $translation['is_duplicate'],
                 'actions' => $key // Just pass the key for frontend button generation
             ];
         }

        return $this->response->setJSON([
            "draw" => intval(input('draw')),
            "recordsTotal" => count($data),
            "recordsFiltered" => count($data),
            "data" => $data
        ]);
    }

    public function save()
    {
        if (!_can("settings")) {
            return _error_code(403);
        }

        $action = input('action');
        
        switch ($action) {
            case 'update_translation':
                return $this->updateTranslation();
            case 'remove_duplicates':
                return $this->removeDuplicates();
            case 'backup_files':
                return $this->createBackup();
            case 'move_to_translated':
                return $this->moveToTranslated();
            case 'move_all_auto_translated':
                return $this->moveAllAutoTranslated();
            default:
                return _response([
                    "success" => false,
                    "message" => [tr("Invalid action")]
                ]);
        }
    }

    private function handleFormSubmission()
    {
        $v = validate([
            "action" => ["action", "required|in:update_translation,remove_duplicates,backup_files,move_to_translated,move_all_auto_translated"]
        ]);

        if ($v->fails()) {
            return _response([
                "success" => false,
                "message" => $v->errors()->all()
            ]);
        }

        return $this->save();
    }

    private function updateTranslation()
    {
        $key = input('key');
        $translatedValue = input('translated_value');
        $source = input('source');

        if (empty($key)) {
            return _response([
                "success" => false,
                "message" => [tr("Translation key is required")]
            ]);
        }

        try {
            // Create backup before making changes
            $this->createBackup();

            // Load current translations
            $arTranslations = $this->loadArTranslations();
            $untranslatedData = $this->loadUntranslatedData();

            if ($source === 'translated' || $source === 'both') {
                $arTranslations[$key] = $translatedValue;
                $this->saveArTranslations($arTranslations);
            }

            if ($source === 'untranslated' || $source === 'both') {
                if (!empty($translatedValue)) {
                    // If translation provided, move to translated file
                    $arTranslations[$key] = $translatedValue;
                    $this->saveArTranslations($arTranslations);
                    
                    // Remove from untranslated
                    unset($untranslatedData[$key]);
                    $this->saveUntranslatedData($untranslatedData);
                } else {
                    $untranslatedData[$key] = $key;
                    $this->saveUntranslatedData($untranslatedData);
                }
            }

            return _response([
                "success" => true,
                "message" => [tr("Translation updated successfully")]
            ]);

        } catch (Exception $e) {
            return _response([
                "success" => false,
                "message" => [tr("Error updating translation: ") . $e->getMessage()]
            ]);
        }
    }

    private function removeDuplicates()
    {
        try {
            // Create backup before making changes
            $this->createBackup();

            $duplicates = $this->findDuplicates();
            $arTranslations = $this->loadArTranslations();
            $untranslatedData = $this->loadUntranslatedData();

            $removedCount = 0;

            foreach ($duplicates as $key => $sources) {
                // Priority: keep in translated file, remove from untranslated
                if (in_array('both', $sources) || 
                    (in_array('translated', $sources) && in_array('untranslated', $sources))) {
                    
                    if (isset($untranslatedData[$key])) {
                        unset($untranslatedData[$key]);
                        $removedCount++;
                    }
                }
            }

            $this->saveUntranslatedData($untranslatedData);

            return _response([
                "success" => true,
                "message" => [tr("Removed {count} duplicate entries", ['count' => $removedCount])]
            ]);

        } catch (Exception $e) {
            return _response([
                "success" => false,
                "message" => [tr("Error removing duplicates: ") . $e->getMessage()]
            ]);
        }
    }

    private function createBackup()
    {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            
            // Backup ar.php
            if (file_exists($this->arFilePath)) {
                copy($this->arFilePath, $this->backupDir . "ar_{$timestamp}.php");
            }
            
            // Backup ar_untranslated.php
            if (file_exists($this->untranslatedFilePath)) {
                copy($this->untranslatedFilePath, $this->backupDir . "ar_untranslated_{$timestamp}.php");
            }

            return _response([
                "success" => true,
                "message" => [tr("Backup created successfully")]
            ]);

        } catch (Exception $e) {
            return _response([
                "success" => false,
                "message" => [tr("Error creating backup: ") . $e->getMessage()]
            ]);
        }
    }

    private function getTranslationsData()
    {
        $arTranslations = $this->loadArTranslations();
        $untranslatedData = $this->loadUntranslatedData();
        $duplicates = $this->findDuplicates();

        $combined = [];

        // Add translated entries
        foreach ($arTranslations as $key => $value) {
            $combined[$key] = [
                'translated' => $value,
                'untranslated' => '',
                'source' => 'translated',
                'is_duplicate' => isset($duplicates[$key])
            ];
        }

        // Add untranslated entries
        foreach ($untranslatedData as $key => $value) {
            if (isset($combined[$key])) {
                // Key exists in both files - mark as duplicate
                $combined[$key]['untranslated'] = $value;
                $combined[$key]['source'] = 'both';
                $combined[$key]['is_duplicate'] = true;
            } else {
                // Check if the "untranslated" value is actually already translated (contains Arabic)
                if ($this->isAlreadyTranslated($value)) {
                    $combined[$key] = [
                        'translated' => $value, // Use the Arabic value as translated
                        'untranslated' => '',
                        'source' => 'auto_translated', // Mark as auto-detected translation
                        'is_duplicate' => false
                    ];
                } else {
                    $combined[$key] = [
                        'translated' => '',
                        'untranslated' => $value,
                        'source' => 'untranslated',
                        'is_duplicate' => false
                    ];
                }
            }
        }

        return $combined;
    }

    private function loadArTranslations()
    {
        if (!file_exists($this->arFilePath)) {
            return [];
        }

        try {
            // Safely include the file instead of using eval
            $translations = include $this->arFilePath;
            return is_array($translations) ? $translations : [];
        } catch (Exception $e) {
            log_message('error', 'Error loading Arabic translations: ' . $e->getMessage());
            return [];
        }
    }

    private function loadUntranslatedData()
    {
        if (!file_exists($this->untranslatedFilePath)) {
            return [];
        }

        $content = file_get_contents($this->untranslatedFilePath);
        return json_decode($content, true) ?? [];
    }

    private function saveArTranslations($translations)
    {
        $content = "<?php\n\nreturn [\n";
        
        foreach ($translations as $key => $value) {
            $escapedKey = addslashes($key);
            $escapedValue = addslashes($value);
            $content .= "    \"{$escapedKey}\" => \"{$escapedValue}\",\n";
        }
        
        $content .= "];\n";
        
        return file_put_contents($this->arFilePath, $content);
    }

    private function saveUntranslatedData($data)
    {
        $content = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        return file_put_contents($this->untranslatedFilePath, $content);
    }

    private function findDuplicates()
    {
        $arTranslations = $this->loadArTranslations();
        $untranslatedData = $this->loadUntranslatedData();

        $duplicates = [];

        foreach ($arTranslations as $key => $value) {
            if (isset($untranslatedData[$key])) {
                $duplicates[$key] = ['translated', 'untranslated'];
            }
        }

        return $duplicates;
    }

    private function getStatistics()
    {
        $arTranslations = $this->loadArTranslations();
        $untranslatedData = $this->loadUntranslatedData();
        $duplicates = $this->findDuplicates();
        
        // Count auto-translated entries (already have Arabic text but in untranslated file)
        $autoTranslated = 0;
        $actuallyUntranslated = 0;
        
        foreach ($untranslatedData as $key => $value) {
            if (!isset($arTranslations[$key])) { // Don't count duplicates
                if ($this->isAlreadyTranslated($value)) {
                    $autoTranslated++;
                } else {
                    $actuallyUntranslated++;
                }
            }
        }

        return [
            'total_translated' => count($arTranslations) + $autoTranslated,
            'total_untranslated' => $actuallyUntranslated,
            'total_auto_translated' => $autoTranslated,
            'total_duplicates' => count($duplicates),
            'total_entries' => count($arTranslations) + count($untranslatedData) - count($duplicates)
        ];
    }



    private function moveToTranslated()
    {
        $key = input('key');
        
        if (empty($key)) {
            return _response([
                "success" => false,
                "message" => [tr("Translation key is required")]
            ]);
        }

        try {
            // Create backup before making changes
            $this->createBackup();

            $arTranslations = $this->loadArTranslations();
            $untranslatedData = $this->loadUntranslatedData();

            if (isset($untranslatedData[$key]) && $this->isAlreadyTranslated($untranslatedData[$key])) {
                // Move to translated file
                $arTranslations[$key] = $untranslatedData[$key];
                $this->saveArTranslations($arTranslations);

                // Remove from untranslated file
                unset($untranslatedData[$key]);
                $this->saveUntranslatedData($untranslatedData);

                return _response([
                    "success" => true,
                    "message" => [tr("Translation moved successfully")]
                ]);
            } else {
                return _response([
                    "success" => false,
                    "message" => [tr("Entry not found or not already translated")]
                ]);
            }

        } catch (Exception $e) {
            return _response([
                "success" => false,
                "message" => [tr("Error moving translation: ") . $e->getMessage()]
            ]);
        }
    }

    private function moveAllAutoTranslated()
    {
        try {
            // Create backup before making changes
            $this->createBackup();

            $arTranslations = $this->loadArTranslations();
            $untranslatedData = $this->loadUntranslatedData();
            $movedCount = 0;

            foreach ($untranslatedData as $key => $value) {
                // Only move if not already in translated file and is already translated
                if (!isset($arTranslations[$key]) && $this->isAlreadyTranslated($value)) {
                    $arTranslations[$key] = $value;
                    unset($untranslatedData[$key]);
                    $movedCount++;
                }
            }

            if ($movedCount > 0) {
                $this->saveArTranslations($arTranslations);
                $this->saveUntranslatedData($untranslatedData);
            }

            return _response([
                "success" => true,
                "message" => [tr("Moved {count} auto-translated entries", ['count' => $movedCount])]
            ]);

        } catch (Exception $e) {
            return _response([
                "success" => false,
                "message" => [tr("Error moving auto-translated entries: ") . $e->getMessage()]
            ]);
        }
    }

    /**
     * Check if a value is already translated (contains Arabic characters)
     */
    private function isAlreadyTranslated($value)
    {
        // Check if the value contains Arabic characters
        // Arabic Unicode range: \u0600-\u06FF, \u0750-\u077F, \uFB50-\uFDFF, \uFE70-\uFEFF
        return preg_match('/[\x{0600}-\x{06FF}\x{0750}-\x{077F}\x{FB50}-\x{FDFF}\x{FE70}-\x{FEFF}]/u', $value);
    }
} 