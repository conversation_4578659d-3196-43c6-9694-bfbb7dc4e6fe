<?= $this->extend('layouts/public') ?>
<?= $this->section('content') ?>
    

    <?php if (count($jobs)): ?>
        <p class="text-primary">
            <?= tr("public_message_text") ?>
        </p>
        <?= display()->messages() ?>
        <div class="row mt-5">
            <?php foreach ($jobs as $key => $job): ?>
                <div class="col-md-6 col-lg-4">
                    <div class="card card-hover gradient-card p-0">
                        <div class="card-header text-end">
                            <span class="badge badge-primary ">GSC-<?= str_pad($job->id, 5, "0", STR_PAD_LEFT) ?></span>
                        </div>
                        <div class="card-body">
                            <h3 class="text-primary mb-3"><?= esc($job->name) ?></h3>
                            <table>
                                <tr>
                                    <th class="pr-2"><i class="fas fa-users mr-2"></i> <?= tr("job_count") ?> </th>
                                    <td><?= esc($job->number) ?></td>
                                </tr>

                                <tr>
                                    <th class="pr-2"><i class="fas fa-venus-mars mr-2"></i> <?= tr("job_gender") ?> </th>
                                    <td><?= tr($job->gender) ?></td>
                                </tr>

                                <tr>
                                    <th class="pr-2"><i class="fas fa-calendar-alt mr-2"></i> <?= tr("job_end_date") ?> </th>
                                    <td><?= _d($job->end_date) ?></td>
                                </tr>

                            </table>
                     
                        </div>

                        <div class="card-footer bg-transparent border-0 ">
                            <a href="<?= base_url("job/".$job->code) ?>" class="btn btn-primary btn-block "><?= tr("Job details") ?> <i class="fas fa-arrow-left"></i></a>
                        </div>
                    </div>
                </div>
            <?php endforeach ?>
        </div>

    <?php else: ?>
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-briefcase fa-5x text-muted opacity-50"></i>
                    </div>
                    <h3 class="text-muted mb-3"><?= tr("No jobs available") ?></h3>
                    <p class="text-muted mb-4">
                        <?= tr("There are currently no job openings available. Please check back later for new opportunities.") ?>
                    </p>
                   
                </div>
            </div>
        </div>
    <?php endif ?>

    



<?= $this->endSection() ?>
