<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Capsule\Manager as DB;
use Illuminate\Database\Eloquent\Casts\Attribute;

class User extends Model
{

    use SoftDeletes;

    protected $table = 'users';
    protected $fillable = [
        'name', 'log_name', 'password', 'phone',
        'role_id', 'email', 'reset_password_token'
    ];

    protected function phone(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => _dr($value),
            set: fn (string $value) => _cr($value),
        );
    }

    protected function email(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => _dr($value),
            set: fn (string $value) => _cr($value),
        );
    }

    protected function logName(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => _dr($value),
            set: fn (string $value) => _cr($value),
        );
    }

    protected function cardId(): Attribute
    {
        return Attribute::make(
            get: fn (string $value) => _dr($value),
            set: fn (string $value) => _cr($value),
        );
    }


    public function role()
    {
        return $this->belongsTo(Role::class,"role_id");
    }



    public function validateCredentials($username, $password)
    {
        $user = $this->where('log_name', _cr($username))->orWhere("email",_cr($username))->orWhere("phone",_cr($username))->first();

        // var_dump($password);

        if ($user && password_verify($password, $user->password)) {
            return $user;
        }

        return false;
    }

    public function can($role){

        if (!auth()) {
            return false;
        }

    

        if (strlen($role)==0) {
            return true;
        }

        $roles = explode("|", $role);
        
        $roles_list = DB::table("roles")->where("id",$this->role_id)->first();

        // var_dump($roles_list);

        foreach ($roles as $key => $value) {
            if (in_array($value, json_decode($roles_list->roles))) {
                return true;
            }
        }
    
        return false;
    }
}