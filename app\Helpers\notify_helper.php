<?php
use Illuminate\Database\Capsule\Manager as DB;
use CodeIgniter\Log\Logger;

function _sms_old($phone,$msg){
	

	$phone=str_replace(' ', '', $phone);
	if (strlen($phone)!=8) {
		return true;
	}

	if (strlen($msg)<1) {
		return true;
	}

	$url = 'https://www.ismartsms.net/iBulkSMS/HttpWS/SMSDynamicRefIntlAPI.aspx';
	$data = array(
		"UserId"   => get_option("sms_user_id"),
		"Password" => get_option("sms_password"),
		"Message"  => $msg,
		"Header"   => "GSC",
		"MobileNo" => $phone,
		'Lang'     => '64',
	);


	// $url = "https://my.textcloud.app/send/create-message";
	// $data = array(
	// 	"authkey"   => 'p9cVbtsGSpQ3SAxVuZgXFZ3IWMZReOjlt33z9rkQkwioOzcwS9',
	// 	"appkey" => "8602db81-b960-476f-adcd-6b73851da295",
	// 	"message"  => $msg,

	// 	"to" => "968".$phone,
		
	// );

    $curl = curl_init();
    
    curl_setopt($curl, CURLOPT_HTTPHEADER, ["Content-type: application/x-www-form-urlencoded"]);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
   
    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
    curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
    $res = curl_exec($curl);

    curl_close($curl);

	return true;
}

function _sms($phone,$msg){
	

	$phone=str_replace(' ', '', $phone);
	if (strlen($phone)!=8) {
		return true;
	}

	if (strlen($msg)<1) {
		return true;
	}

	// $url = env("sms.endpoint");
	// $data = array(
	// 	"UserId"   => env("sms.UserId"),
	// 	"Password" => env("sms.Password"),
	// 	"Message"  => $msg,
	// 	"Header"   => "GSC",
	// 	"MobileNo" => $phone,
	// 	'Lang'     => '64',
	// );
	// 
	
	$username = get_option("sms_user_id");
	$password = get_option("sms_password");

	$url = "https://smsoman.com/user/bulkpush.asmx";
	$xmlRequest = <<<XML
	<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
	xmlns:tam="https://www.tamimahsms.com/">
	  <soapenv:Header/>
	  <soapenv:Body>
	    <tam:SendSMS>
	      <tam:UserName>{$username}</tam:UserName>
	      <tam:Password>{$password}</tam:Password>
	      <tam:Message><![CDATA[{$msg}]]></tam:Message>
	      <tam:Priority>1</tam:Priority>
	      <tam:Schdate></tam:Schdate>
	      <tam:Sender>TOGETHERFWD</tam:Sender>
	      <tam:AppID>12</tam:AppID>
	      <tam:SourceRef>Portal</tam:SourceRef>
	      <tam:MSISDNs>{$phone}</tam:MSISDNs>
	    </tam:SendSMS>
	  </soapenv:Body>
	</soapenv:Envelope>
	XML;

	// Initialize cURL session
	$ch = curl_init($url);

	// Set cURL options
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_POST, true);
	curl_setopt($ch, CURLOPT_POSTFIELDS, $xmlRequest);
	curl_setopt($ch, CURLOPT_HTTPHEADER, [
	    "Content-Type: text/xml; charset=utf-8",
	    "Content-Length: " . strlen($xmlRequest)
	]);

	// Execute the request and capture the response
	$response = curl_exec($ch);


	log_message('debug', "SMS Raw SOAP Response: " . print_r($response, true));



	// Close the cURL session
	curl_close($ch);

	return true;
}