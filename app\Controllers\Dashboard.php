<?php

namespace App\Controllers;
use Illuminate\Database\Capsule\Manager as DB;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Job;
use App\Models\Application;

class Dashboard extends BaseController
{
    public function index()
    {

        $this->d['nav']['reload'] = false;
        // Total counts
        $this->d['total_jobs'] = Job::count();
        $this->d['total_applications'] = Application::count();
        $this->d['active_jobs'] = Job::where('status', Job::STATUS_PUBLIC)->count();
        $this->d['closed_jobs'] = Job::where('status', Job::STATUS_PRIVATE)->count();
        
        // Recent statistics (last 30 days)
        $thirtyDaysAgo = Carbon::now()->subDays(30);
        $this->d['recent_applications'] = Application::where('created_at', '>=', $thirtyDaysAgo)->count();
        $this->d['recent_jobs'] = Job::where('created_at', '>=', $thirtyDaysAgo)->count();
        
        // Applications by status
        $this->d['applications_by_status'] = DB::table('applications')
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->get();
        
        // Status labels mapping
        $this->d['status_labels'] = [
            'not_eligible' => 'غير مستوفي الشروط',
            'new' => 'جديد',
            'eligible' => 'مستوفي الشروط',
            'test' => 'الاختبار',
            'interview' => 'المقابلة',
            'selected' => 'تم اختياره'
        ];
        
        // Top 8 Regions for pie chart
        $this->d['applications_by_region'] = DB::table('applications')
            ->join('regions', 'applications.reg_id', '=', 'regions.id')
            ->select('regions.name_ar as region_name', 'regions.name_en as region_name_en', DB::raw('count(*) as count'))
            ->groupBy('applications.reg_id', 'regions.name_ar', 'regions.name_en')
            ->orderBy('count', 'desc')
            ->limit(8)
            ->get();
        
        // Applications by Jobs
        $this->d['applications_by_jobs'] = DB::table('applications')
            ->join('jobs', 'applications.job_id', '=', 'jobs.id')
            ->select('jobs.name as job_name', DB::raw('count(*) as count'))
            ->groupBy('jobs.id', 'jobs.name')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();
            
        $this->d['jobs_by_gender'] = Job::select('gender', DB::raw('count(*) as count'))
            ->groupBy('gender')
            ->get();
        
        // Top regions by application count
        $this->d['top_regions'] = DB::table('applications')
            ->join('regions', 'applications.reg_id', '=', 'regions.id')
            ->select('regions.name_ar as region_name', 'regions.name_en as region_name_en', DB::raw('count(*) as count'))
            ->groupBy('applications.reg_id', 'regions.name_ar', 'regions.name_en')
            ->orderBy('count', 'desc')
            ->limit(10)
            ->get();
        
        // Gender distribution in applications
        $this->d['applications_by_gender'] = Application::select('gender', DB::raw('count(*) as count'))
            ->groupBy('gender')
            ->get();

        return view('dashboard/index', $this->d);
    }
}
