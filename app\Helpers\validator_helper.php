<?php 



use Rakit\Validation\Validator ;
use Rakit\Validation\Rule;
use Illuminate\Database\Capsule\Manager as DB;
use Illuminate\Database\Schema\Blueprint;
use CodeIgniter\Database\ConnectionInterface;

class IsPDFRule extends Rule
{
    public $message = ":attribute invalid file format";

    protected $fillableParams = [];



    public  function __construct()
    {
  
    }

    public function check($value): bool
    {

        $handle = fopen($value['tmp_name'], "r");
        $header = fread($handle, 5);
        fclose($handle);
        if ($header == '%PDF-') {
            return true;
        }

        return false;
    }

}

class UniqueRule extends Rule
{
    public $message = ":attribute already existed.";

    protected $fillableParams = ['table', 'column', 'except'];

    protected $db;

    public  function __construct(ConnectionInterface $db)
    {
        $this->db = $db;
        $this->message = ":attribute ".tr("already existed.");
    }

    public function check($value): bool
    {
        $this->requireParameters(['table', 'column']);

        $table = $this->parameter('table');
        $column = $this->parameter('column');
        $except = $this->parameter('except');

        $builder = $this->db->table($table);

        $builder->where($column, $value);

        if ($this->hasSoftDelete($table)) {
            $builder->where('deleted_at IS NULL', null, false);
        }

        if ($except) {
            $builder->where('id !=', $except);
        }

        $count = $builder->countAllResults();

        if ($count === 0) {

            $value = _cr($value);

            $builder = $this->db->table($table);

            $builder->where($column, $value);

            if ($this->hasSoftDelete($table)) {
                $builder->where('deleted_at IS NULL', null, false);
            }

            if ($except) {
                $builder->where('id !=', $except);
            }

            $count = $builder->countAllResults();
            
        }

        return $count === 0;
    }

    protected function hasSoftDelete($table)
    {
        $query = $this->db->query("SHOW COLUMNS FROM {$table} LIKE 'deleted_at'");
        return count($query->getResultArray()) > 0;
    }
}

class InTableRule extends Rule
{
     public $message = ":attribute is invalid.";

    protected $fillableParams = ['table', 'column'];


    public  function __construct(){
        $this->message = ":attribute ".tr("is invalid.");
    }
  

    public function check($value): bool
    {
        // make sure required parameters exists
        $this->requireParameters(['table', 'column']);

        // getting parameters
        $column = $this->parameter('column');
        $table = $this->parameter('table');
      
         $data = DB::table($table)
            ->where($column,$value)
            ->count();
        // do query
        // $data = $this->db->single("select count(*) as count from `{$table}` where `{$column}` = ?",[$value]);
       

        // true for valid, false for invalid
        return $data != 0;
    }
}





function validate($array,$messages=[]){
	$v =  new Validator;
    
	$db = \Config\Database::connect();
    $v->addValidator('unique', new UniqueRule($db));
    $v->addValidator('in_table', new InTableRule);
	$v->addValidator('file_pdf', new IsPDFRule);

	$alias = [];
	$validate = [];

	foreach ($array as $key => $value) {
		$alias[$value[0]]=$key;
		$validate[$value[0]]=$value[1];
	}

	$validation = $v->make($_POST + $_FILES,

		$validate
	);


	$validation->setAliases($alias );

    $cmessages=[
        "phone:required"=>"يجب إدخال رقم هاتف تابع  لمزودي الخدمة بسلطنة عمان ",
        "phone:min"=>"يجب أن يكون رقم الهاتف مكوناً من 8 أرقام ",
        "phone:max"=>"يجب أن يكون رقم الهاتف مكوناً من 8 أرقام ",
        "phone:integer"=>"يجب إدخال رقم هاتف تابع  لمزودي الخدمة بسلطنة عمان",
        // "phone:unique"=>"لقد قمت بالتصويت مسبقًا في هذا الاستطلاع، ونؤكد أنه تم احتساب مشاركتك به"

        "name1:required"=>"يجب إدخال الاسم الأول ",
        // "name1:alpha"=>"",
        "name1:min"=>"يجب أن لا يقل  الاسم الأول عن حرفين",
        "name1:max"=>"يجب أن لا يزيد الاسم الأول عن ٣٠ حرف ",

        "name1:required"=>"يجب إدخال الاسم الأول ",
        // "name1:alpha"=>"",
        "name1:min"=>"يجب أن لا يقل  الاسم الأول عن حرفين",
        "name1:max"=>"يجب أن لا يزيد الاسم الأول عن ٣٠ حرف ",

        "name2:required"=>"يجب إدخال اسم الأب",
        // "name1:alpha"=>"",
        "name2:min"=>"يجب أن لا يقل  اسم الأب عن حرفين",
        "name2:max"=>"يجب أن لا يزيد اسم الأب عن ٣٠ حرف ",

        "name3:required"=>"يجب إدخال اسم الجد ",
        // "name1:alpha"=>"",
        "name3:min"=>"يجب أن لا يقل  اسم الجد عن حرفين",
        "name3:max"=>"يجب أن لا يزيد اسم الجد عن ٣٠ حرف ",

        "name4:required"=>"يجب إدخال اسم القبيلة/الكنية ",
        // "name1:alpha"=>"",
        "name4:min"=>"يجب أن لا تقل  اسم القبيلة/الكنية عن حرفين",
        "name4:max"=>"يجب أن لا تزيد اسم القبيلة/الكنية عن ٣٠ حرف ",

        "email:required"=>"يجب تسجيل البريد الإلكتروني",
        "email:email"=>"يجب إدخال عنوان بريد الإلكتروني  صحيح",
        "email:min"=>"يجب أن لا يقل البريد الإلكتروني المدخل عن ١٠حروف ",
        "email:max"=>"يجب أن لا يزيد البريد الإلكتروني المدخل عن ١٥٠حرف ",
        "email:unique"=>"هذا البريد الإلكتروني سبق أن تم استخدامه لأغراض التسجيل",


        "card_id:required"=>"يجب إدخال رقم البطاقة الشخصية  ",
        // "card_id:integer"=>"",
        "card_id:unique"=>"رقم البطاقة الشخصية سبق أن تم استخدامه لأغراض التسجيل",

        "birth:required"=>"يجب إدخال تاريخ الميلاد",
        // "birth:date"=>"",
        // "birth:min"=>"",
        // "birth:max"=>"",

        "gender:in"=>"يجب اختيار الجنس ",
        "gender:required"=>"يجب اختيار الجنس",

        "reg_id:required"=>"يجب اختيار المحافظة",
        
        "reg_id:in_table"=>"يجب اختيار المحافظة",
        "city_id:in_table"=>"يجب اختيار الولاية",
        "city_id:required"=>"يجب اختيار الولاية",
        // "reg_id:in"=>"يجب اختيار المحافظة",

      
        "topic:in"=>"يجب اختيار أحد المحاور المدرجة  ",


    ];
    $messages = array_merge($messages,$cmessages);
    // $validation->setMessages($cmessages);
    $validation->setMessages($messages);

	$validation->validate();

	return $validation;
}


