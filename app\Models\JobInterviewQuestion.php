<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JobInterviewQuestion extends Model
{
    protected $table = 'job_interview_questions';
    
    protected $fillable = [
        'job_id',
        'question',
        'answer',
    ];

    /**
     * Relationship: JobInterviewQuestion belongs to Job
     * 
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function job()
    {
        return $this->belongsTo(Job::class, 'job_id');
    }

    /**
     * Scope to get questions for a specific job
     * 
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $jobId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForJob($query, $jobId)
    {
        return $query->where('job_id', $jobId);
    }

    /**
     * Scope to get questions with answers
     * 
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithAnswers($query)
    {
        return $query->whereNotNull('answer')->where('answer', '!=', '');
    }

    /**
     * Scope to get questions without answers
     * 
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithoutAnswers($query)
    {
        return $query->where(function($q) {
            $q->whereNull('answer')->orWhere('answer', '');
        });
    }

    /**
     * Check if question has an answer
     * 
     * @return bool
     */
    public function hasAnswer()
    {
        return !empty($this->answer);
    }
} 