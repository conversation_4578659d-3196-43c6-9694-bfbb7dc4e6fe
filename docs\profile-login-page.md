# Profile Login Page Documentation

## Overview

This document provides comprehensive technical documentation for the profile login page system, covering page elements, form submission mechanics, controller responses, and API endpoints.

## Page Elements

### HTML Structure

The login page (`app/Views/profile/login.php`) contains the following key elements:

```php
<form class="login-form wmin-sm-400 ajax" method="post" autocomplete="off" enctype="multipart/form-data">
    <input type="text" class="form-control text-center" name="phone" dir="ltr" 
           placeholder="Enter phone number" maxlength="8">
    <button type="submit" name="signin" class="btn btn-primary btn-large py-2 btn-block" 
            id="submit-btn">Sign In</button>
</form>
```

### Form Elements

| Element | Attributes | Purpose |
|---------|------------|---------|
| **Form Container** | `class="ajax"` | Enables AJAX form submission |
| **Phone Input** | `name="phone"`, `maxlength="8"`, `dir="ltr"` | 8-digit Oman phone number input |
| **Country Code Display** | `+968` | Fixed Oman country code suffix |
| **Submit Button** | `name="signin"`, `id="submit-btn"` | Form submission trigger |
| **Message Container** | `class="message"` | Dynamic message display area |

### CSS Classes

- **`ajax`**: Triggers AJAX form handling
- **`form-control`**: Bootstrap form styling
- **`text-center`**: Centers input text
- **`input-group`**: Groups input with country code
- **`btn-block`**: Full-width button

## Form Submission Process

### AJAX Submission Flow

Based on `app/Views/inc/foot.php`, the AJAX submission follows this pattern:

```javascript
$("body").on('submit','form.ajax', function(event) {
    event.preventDefault()
    
    var form = $(this);
    var action = form.attr("action") ? form.attr("action") : "";
    var method = form.attr("method") ? form.attr("method") : "post";
    var submit_button = form.find('button[type!=button]').attr('name');
    
    var formData = new FormData(this);
    
    if (submit_button) {
        formData.append(submit_button, 1);
    }
    
    formData.append("ajax_form", 1);
    
    // Submit via AJAX
    $.ajax({
        url: action,
        type: method,
        dataType: 'json',
        data: formData,
        cache: false,
        contentType: false,
        processData: false,
        success: function(data) {
            $(document).trigger('submit_complete', data);
        }
    });
});
```

### Submission Steps

1. **Form Validation**: Client-side validation occurs first
2. **FormData Creation**: Form data is serialized into FormData object
3. **Button State**: Submit button shows loading spinner
4. **AJAX Request**: POST request sent to controller
5. **Response Handling**: Server response triggers appropriate actions
6. **UI Updates**: Button state restored, messages displayed

## Controller Response Format

### ProfileController Login Method

**File**: `app/Controllers/ProfileController.php`

```php
public function login()
{
    if(profile_auth()){
        return redirect()->to(base_url('profile'));
    }

    if(is_post()){
        $v = validate([
            tr("Phone")=>["phone","required|integer|digits:8"],
        ]);
        
        if(!$v->passes()){
            return _response([
                "success"=>false,
                "message"=>$v->errors()->all(),
                "action"=>"",
            ]);
        }
        
        $phone = input("phone");
        $profile = Profile::where("phone",$phone)->first();

        if(!$profile){
            $profile = new Profile();
            $profile->phone = $phone;
            $profile->save();
        }
        
        // Success response logic here
    }

    return view('profile/login');
}
```

### Standard Response Structure

All AJAX responses follow this standardized format:

```php
return _response([
    "success" => boolean,        // true/false
    "message" => array,          // Array of messages
    "action" => string,          // JavaScript code to execute
    "data" => mixed             // Optional data payload
]);
```

### Response Examples

#### Success Response
```php
return _response([
    "success" => true,
    "message" => [tr("Login successful")],
    "action" => "location.href='" . base_url("profile") . "'"
]);
```

#### Validation Error Response
```php
return _response([
    "success" => false,
    "message" => $v->errors()->all(),
    "action" => ""
]);
```

#### OTP Required Response
```php
return _response([
    "success" => true,
    "message" => [tr("OTP sent to your phone")],
    "action" => '$("#otp-modal").modal("show")',
    "data" => ["delay" => 60]
]);
```

## Validation Rules

### Phone Number Validation

The system implements strict validation for Oman phone numbers:

```php
$v = validate([
    tr("Phone") => ["phone", "required|integer|digits:8"],
]);
```

### Validation Rules Breakdown

| Rule | Description | Example |
|------|-------------|---------|
| **required** | Field cannot be empty | `90123456` |
| **integer** | Must be numeric only | `90123456` |
| **digits:8** | Exactly 8 digits | `90123456` |
| **min:70000000** | Minimum value for Oman numbers | `70000000` |
| **max:99999999** | Maximum value for Oman numbers | `99999999` |

### Custom Error Messages

From `app/Helpers/validator_helper.php`:

```php
$cmessages = [
    "phone:required" => "يجب إدخال رقم هاتف تابع لمزودي الخدمة بسلطنة عمان",
    "phone:min" => "يجب أن يكون رقم الهاتف مكوناً من 8 أرقام",
    "phone:max" => "يجب أن يكون رقم الهاتف مكوناً من 8 أرقام",
    "phone:integer" => "يجب إدخال رقم هاتف تابع لمزودي الخدمة بسلطنة عمان"
];
```

## API Endpoints

### Primary Endpoint

**URL**: `/profile/login`  
**Method**: `POST`  
**Content-Type**: `multipart/form-data`

#### Request Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `phone` | string | Yes | 8-digit Oman phone number |
| `signin` | string | Yes | Submit button identifier |
| `ajax_form` | string | Yes | AJAX form flag |

#### Request Example

```javascript
POST /profile/login
Content-Type: multipart/form-data

phone=90123456
signin=1
ajax_form=1
```

### Response Formats

#### Successful Login
```json
{
    "success": true,
    "message": ["تم تسجيل الدخول بنجاح"],
    "action": "location.href='/profile'"
}
```

#### Validation Error
```json
{
    "success": false,
    "message": ["يجب أن يكون رقم الهاتف مكوناً من 8 أرقام"],
    "action": ""
}
```

#### Profile Creation
```json
{
    "success": true,  
    "message": ["تم إنشاء الملف الشخصي"],
    "action": "location.href='/profile/complete'"
}
```

## Security Features

### Rate Limiting

Failed login attempts are tracked and limited:

```php
private function failed_attempt($phone=""){
    DB::table("login_attempts")->insert([
        'ip' => _ip(),
        'phone' => $phone,
    ]);
    
    $failedAttempts = DB::table('login_attempts')
        ->where('phone', $phone)
        ->where('created_at', '>=', Carbon::now()->subHour())
        ->count();

    if ($failedAttempts >= 5) {
        $profile->locked_until = Carbon::now()->addHour();
        $profile->save();
    }
}
```

### Data Encryption

Phone numbers are encrypted using helper functions:

```php
// Encryption on save
protected function phone(): Attribute
{
    return Attribute::make(
        get: fn (string $value) => _dr($value),  // Decrypt
        set: fn (string $value) => _cr($value),  // Encrypt
    );
}
```

### Input Sanitization

All inputs are sanitized using the `a2e()` function:

```php
$_POST['phone'] = a2e(input('phone'));
```

## Error Handling

### Client-Side Error Display

Errors are displayed in the `.message` container:

```javascript
$(document).on('submit_complete', function(e, data) {
    if (!data.success) {
        $('.message').html('<div class="alert alert-danger">' + 
                          data.message.join('<br>') + '</div>');
    }
});
```

### Server-Side Error Logging

Errors are logged using the Log model:

```php
Log::new("Login attempt failed [phone:$phone]");
```

## Integration Points

### Session Management

Profile authentication uses session storage:

```php
if(profile_auth()){
    return redirect()->to(base_url('profile'));
}
```

### Database Models

- **Profile Model**: `app/Models/Profile.php`
- **User Model**: `app/Models/User.php` (for admin login)
- **Log Model**: `app/Models/Log.php` (for audit trail)

### Helper Functions

- **`profile_auth()`**: Check profile authentication status
- **`_cr()/_dr()`**: Encrypt/decrypt data
- **`a2e()`**: Arabic to English number conversion
- **`_response()`**: Standardized AJAX response formatter

## Configuration

### Environment Variables

```env
RECAPTCHA_ACTIVE=true
RECAPTCHA_SITE_KEY=your_site_key
RECAPTCHA_SECRET_KEY=your_secret_key
```

### Application Settings

Phone validation ranges can be configured in the validation rules based on Oman mobile number ranges (70000000-99999999).

## Testing Guidelines

### Unit Tests

Test the controller methods:

```php
public function test_phone_validation()
{
    $response = $this->post('/profile/login', ['phone' => '90123456']);
    $this->assertEquals(200, $response->getStatusCode());
}
```

### Integration Tests

Test the complete AJAX flow:

```javascript
// Test AJAX submission
$.post('/profile/login', {
    phone: '90123456',
    signin: 1,
    ajax_form: 1
}, function(data) {
    expect(data.success).toBe(true);
});
```

## Performance Considerations

### Database Optimization

- Index on phone number field for faster lookups
- Rate limiting table cleanup for old records
- Session cleanup for expired sessions

### Caching Strategy

- Cache validation rules
- Store frequently accessed profile data
- Implement Redis for session storage in production

## Troubleshooting

### Common Issues

1. **AJAX Form Not Submitting**: Ensure `ajax` class is present
2. **Validation Errors**: Check phone number format (8 digits)
3. **Country Code Display**: Verify Bootstrap input-group structure
4. **Response Not Handled**: Check JavaScript response handling

### Debug Mode

Enable debugging by checking response in browser dev tools:

```javascript
console.log('Form submission response:', data);
``` 