<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProfileQualification extends Model
{
    protected $table = 'profile_qualifications';

    protected $fillable = [
        'profile_id',
        'major',
        'location',
        'gpa',
        'end_year',
        'file_id'
    ];

    public function profile()
    {
        return $this->belongsTo(Profile::class, 'profile_id');
    }

    public function getFileUrl()
    {
        if ($this->file_id) {
            return storage()->get($this->file_id);
        }
        return null;
    }
}
